globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/cafeteria/profile/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./components/providers/preload-provider.tsx":{"*":{"id":"(ssr)/./components/providers/preload-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/theme-context.tsx":{"*":{"id":"(ssr)/./components/theme-context.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/ui/toaster.tsx":{"*":{"id":"(ssr)/./components/ui/toaster.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/page.tsx":{"*":{"id":"(ssr)/./app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/cafeteria/sidebar.tsx":{"*":{"id":"(ssr)/./components/cafeteria/sidebar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./contexts/annotation-context.tsx":{"*":{"id":"(ssr)/./contexts/annotation-context.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/cafeteria/dashboard/page.tsx":{"*":{"id":"(ssr)/./app/cafeteria/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/cafeteria/menu/page.tsx":{"*":{"id":"(ssr)/./app/cafeteria/menu/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/cafeteria/orders/page.tsx":{"*":{"id":"(ssr)/./app/cafeteria/orders/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/cafeteria/inventory/page.tsx":{"*":{"id":"(ssr)/./app/cafeteria/inventory/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/cafeteria/analytics/page.tsx":{"*":{"id":"(ssr)/./app/cafeteria/analytics/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/cafeteria/support/page.tsx":{"*":{"id":"(ssr)/./app/cafeteria/support/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/admin/sidebar.tsx":{"*":{"id":"(ssr)/./components/admin/sidebar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/dashboard/page.tsx":{"*":{"id":"(ssr)/./app/admin/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/user-management/page.tsx":{"*":{"id":"(ssr)/./app/admin/user-management/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/cafeteria-approvals/page.tsx":{"*":{"id":"(ssr)/./app/admin/cafeteria-approvals/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/admin/header.tsx":{"*":{"id":"(ssr)/./components/admin/header.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/ui/tabs.tsx":{"*":{"id":"(ssr)/./components/ui/tabs.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/cafeteria-ratings/page.tsx":{"*":{"id":"(ssr)/./app/admin/cafeteria-ratings/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/order-insights/page.tsx":{"*":{"id":"(ssr)/./app/admin/order-insights/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/analytics/page.tsx":{"*":{"id":"(ssr)/./app/admin/analytics/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/customer-service/page.tsx":{"*":{"id":"(ssr)/./app/admin/customer-service/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/audit-logs/page.tsx":{"*":{"id":"(ssr)/./app/admin/audit-logs/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/reports/page.tsx":{"*":{"id":"(ssr)/./app/admin/reports/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/cafeteria/profile/page.tsx":{"*":{"id":"(ssr)/./app/cafeteria/profile/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/settings/page.tsx":{"*":{"id":"(ssr)/./app/admin/settings/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/cafeteria/settings/page.tsx":{"*":{"id":"(ssr)/./app/cafeteria/settings/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/forgot-password/page.tsx":{"*":{"id":"(ssr)/./app/forgot-password/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/register/page.tsx":{"*":{"id":"(ssr)/./app/register/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/contact/page.tsx":{"*":{"id":"(ssr)/./app/contact/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/about/page.tsx":{"*":{"id":"(ssr)/./app/about/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\app\\globals.css":{"id":"(app-pages-browser)/./app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\components\\providers\\preload-provider.tsx":{"id":"(app-pages-browser)/./components/providers/preload-provider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\components\\theme-context.tsx":{"id":"(app-pages-browser)/./components/theme-context.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\components\\ui\\toaster.tsx":{"id":"(app-pages-browser)/./components/ui/toaster.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\node_modules\\next\\font\\google\\target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\app\\page.tsx":{"id":"(app-pages-browser)/./app/page.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\components\\cafeteria\\sidebar.tsx":{"id":"(app-pages-browser)/./components/cafeteria/sidebar.tsx","name":"*","chunks":["app/cafeteria/layout","static/chunks/app/cafeteria/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\contexts\\annotation-context.tsx":{"id":"(app-pages-browser)/./contexts/annotation-context.tsx","name":"*","chunks":["app/cafeteria/layout","static/chunks/app/cafeteria/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\app\\cafeteria\\dashboard\\page.tsx":{"id":"(app-pages-browser)/./app/cafeteria/dashboard/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\app\\cafeteria\\menu\\page.tsx":{"id":"(app-pages-browser)/./app/cafeteria/menu/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\app\\cafeteria\\orders\\page.tsx":{"id":"(app-pages-browser)/./app/cafeteria/orders/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\app\\cafeteria\\inventory\\page.tsx":{"id":"(app-pages-browser)/./app/cafeteria/inventory/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\app\\cafeteria\\analytics\\page.tsx":{"id":"(app-pages-browser)/./app/cafeteria/analytics/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\app\\cafeteria\\support\\page.tsx":{"id":"(app-pages-browser)/./app/cafeteria/support/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\components\\admin\\sidebar.tsx":{"id":"(app-pages-browser)/./components/admin/sidebar.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\app\\admin\\dashboard\\page.tsx":{"id":"(app-pages-browser)/./app/admin/dashboard/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\app\\admin\\user-management\\page.tsx":{"id":"(app-pages-browser)/./app/admin/user-management/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\app\\admin\\cafeteria-approvals\\page.tsx":{"id":"(app-pages-browser)/./app/admin/cafeteria-approvals/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\components\\admin\\header.tsx":{"id":"(app-pages-browser)/./components/admin/header.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\components\\ui\\tabs.tsx":{"id":"(app-pages-browser)/./components/ui/tabs.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\app\\admin\\cafeteria-ratings\\page.tsx":{"id":"(app-pages-browser)/./app/admin/cafeteria-ratings/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\app\\admin\\order-insights\\page.tsx":{"id":"(app-pages-browser)/./app/admin/order-insights/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\app\\admin\\analytics\\page.tsx":{"id":"(app-pages-browser)/./app/admin/analytics/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\app\\admin\\customer-service\\page.tsx":{"id":"(app-pages-browser)/./app/admin/customer-service/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\app\\admin\\audit-logs\\page.tsx":{"id":"(app-pages-browser)/./app/admin/audit-logs/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\app\\admin\\reports\\page.tsx":{"id":"(app-pages-browser)/./app/admin/reports/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\app\\cafeteria\\profile\\page.tsx":{"id":"(app-pages-browser)/./app/cafeteria/profile/page.tsx","name":"*","chunks":["app/cafeteria/profile/page","static/chunks/app/cafeteria/profile/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\app\\admin\\settings\\page.tsx":{"id":"(app-pages-browser)/./app/admin/settings/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\app\\cafeteria\\settings\\page.tsx":{"id":"(app-pages-browser)/./app/cafeteria/settings/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\app\\forgot-password\\page.tsx":{"id":"(app-pages-browser)/./app/forgot-password/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\app\\register\\page.tsx":{"id":"(app-pages-browser)/./app/register/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\app\\contact\\page.tsx":{"id":"(app-pages-browser)/./app/contact/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\app\\about\\page.tsx":{"id":"(app-pages-browser)/./app/about/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\":[],"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\app\\page":[],"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\app\\cafeteria\\layout":[],"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\app\\cafeteria\\profile\\page":[]},"rscModuleMapping":{"(app-pages-browser)/./app/globals.css":{"*":{"id":"(rsc)/./app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/providers/preload-provider.tsx":{"*":{"id":"(rsc)/./components/providers/preload-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/theme-context.tsx":{"*":{"id":"(rsc)/./components/theme-context.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/ui/toaster.tsx":{"*":{"id":"(rsc)/./components/ui/toaster.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/page.tsx":{"*":{"id":"(rsc)/./app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/cafeteria/sidebar.tsx":{"*":{"id":"(rsc)/./components/cafeteria/sidebar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./contexts/annotation-context.tsx":{"*":{"id":"(rsc)/./contexts/annotation-context.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/cafeteria/dashboard/page.tsx":{"*":{"id":"(rsc)/./app/cafeteria/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/cafeteria/menu/page.tsx":{"*":{"id":"(rsc)/./app/cafeteria/menu/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/cafeteria/orders/page.tsx":{"*":{"id":"(rsc)/./app/cafeteria/orders/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/cafeteria/inventory/page.tsx":{"*":{"id":"(rsc)/./app/cafeteria/inventory/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/cafeteria/analytics/page.tsx":{"*":{"id":"(rsc)/./app/cafeteria/analytics/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/cafeteria/support/page.tsx":{"*":{"id":"(rsc)/./app/cafeteria/support/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/admin/sidebar.tsx":{"*":{"id":"(rsc)/./components/admin/sidebar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/dashboard/page.tsx":{"*":{"id":"(rsc)/./app/admin/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/user-management/page.tsx":{"*":{"id":"(rsc)/./app/admin/user-management/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/cafeteria-approvals/page.tsx":{"*":{"id":"(rsc)/./app/admin/cafeteria-approvals/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/admin/header.tsx":{"*":{"id":"(rsc)/./components/admin/header.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/ui/tabs.tsx":{"*":{"id":"(rsc)/./components/ui/tabs.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/cafeteria-ratings/page.tsx":{"*":{"id":"(rsc)/./app/admin/cafeteria-ratings/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/order-insights/page.tsx":{"*":{"id":"(rsc)/./app/admin/order-insights/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/analytics/page.tsx":{"*":{"id":"(rsc)/./app/admin/analytics/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/customer-service/page.tsx":{"*":{"id":"(rsc)/./app/admin/customer-service/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/audit-logs/page.tsx":{"*":{"id":"(rsc)/./app/admin/audit-logs/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/reports/page.tsx":{"*":{"id":"(rsc)/./app/admin/reports/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/cafeteria/profile/page.tsx":{"*":{"id":"(rsc)/./app/cafeteria/profile/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/settings/page.tsx":{"*":{"id":"(rsc)/./app/admin/settings/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/cafeteria/settings/page.tsx":{"*":{"id":"(rsc)/./app/cafeteria/settings/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/forgot-password/page.tsx":{"*":{"id":"(rsc)/./app/forgot-password/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/register/page.tsx":{"*":{"id":"(rsc)/./app/register/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/contact/page.tsx":{"*":{"id":"(rsc)/./app/contact/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/about/page.tsx":{"*":{"id":"(rsc)/./app/about/page.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}