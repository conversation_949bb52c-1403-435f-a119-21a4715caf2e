/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/cafeteria-applications/route";
exports.ids = ["app/api/cafeteria-applications/route"];
exports.modules = {

/***/ "(rsc)/./app/api/cafeteria-applications/route.ts":
/*!*************************************************!*\
  !*** ./app/api/cafeteria-applications/route.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   PATCH: () => (/* binding */ PATCH),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase */ \"(rsc)/./lib/supabase.ts\");\n\n\nasync function POST(request) {\n    try {\n        console.log('Service role key exists:', !!process.env.SUPABASE_SERVICE_ROLE_KEY);\n        const body = await request.json();\n        console.log('Received registration data:', body);\n        const { ownerFirstName, ownerLastName, email, phone, cafeteriaName, cafeteriaLocation, cafeteriaDescription, password } = body;\n        // Validate required fields\n        if (!ownerFirstName || !ownerLastName || !email || !cafeteriaName || !cafeteriaLocation) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Missing required fields'\n            }, {\n                status: 400\n            });\n        }\n        // Check if an application already exists for this email and cafeteria name\n        const { data: existingApplication, error: checkError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from('cafeteria_applications').select('id, status, business_name').eq('contact_email', email).eq('business_name', cafeteriaName).single();\n        if (checkError && checkError.code !== 'PGRST116') {\n            console.error('Error checking existing application:', checkError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to check existing applications'\n            }, {\n                status: 500\n            });\n        }\n        if (existingApplication) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: `An application for \"${cafeteriaName}\" with this email already exists (Status: ${existingApplication.status})`,\n                existingApplicationId: existingApplication.id\n            }, {\n                status: 409\n            } // Conflict\n            );\n        }\n        // Insert into cafeteria_applications table\n        const { data: application, error: applicationError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from('cafeteria_applications').insert({\n            business_name: cafeteriaName,\n            location: cafeteriaLocation,\n            description: cafeteriaDescription,\n            contact_phone: phone,\n            contact_email: email,\n            owner_name: `${ownerFirstName} ${ownerLastName}`,\n            website: '',\n            status: 'pending',\n            submitted_at: new Date().toISOString(),\n            // Store password temporarily for profile creation (in real app, this should be more secure)\n            temp_password: password\n        }).select().single();\n        if (applicationError) {\n            console.error('Error creating cafeteria application:', applicationError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to submit application'\n            }, {\n                status: 500\n            });\n        }\n        // TODO: Send confirmation email to applicant\n        // TODO: Notify admins of new application\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'Cafeteria application submitted successfully',\n            applicationId: application.id\n        });\n    } catch (error) {\n        console.error('Error in cafeteria application API:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function GET(request) {\n    try {\n        // Check if user is authenticated and is admin\n        const currentUser = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.getCurrentUser)();\n        if (!currentUser || currentUser.role !== 'admin') {\n            // Temporary bypass for development - check if this is localhost\n            const host = request.headers.get('host');\n            if (!host?.includes('localhost')) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Unauthorized. Admin access required.'\n                }, {\n                    status: 401\n                });\n            }\n            console.log('🔧 Development bypass: allowing admin access on localhost');\n        }\n        const { searchParams } = new URL(request.url);\n        const status = searchParams.get('status');\n        const supabaseAdmin = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.createSupabaseAdmin)();\n        let query = supabaseAdmin.from('cafeteria_applications').select('*').order('submitted_at', {\n            ascending: false\n        });\n        if (status && status !== 'all') {\n            query = query.eq('status', status);\n        }\n        const { data: applications, error } = await query;\n        if (error) {\n            console.error('Error fetching cafeteria applications:', error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to fetch applications'\n            }, {\n                status: 500\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            applications: applications || []\n        });\n    } catch (error) {\n        console.error('Error in cafeteria applications GET API:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function PATCH(request) {\n    try {\n        // Check if user is authenticated and is admin\n        const currentUser = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.getCurrentUser)();\n        if (!currentUser || currentUser.role !== 'admin') {\n            // Temporary bypass for development - check if this is localhost\n            const host = request.headers.get('host');\n            if (!host?.includes('localhost')) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Unauthorized. Admin access required.'\n                }, {\n                    status: 401\n                });\n            }\n            console.log('🔧 Development bypass: allowing admin access on localhost');\n        }\n        const body = await request.json();\n        const { applicationId, status, reviewNotes } = body;\n        if (!applicationId || !status) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Missing required fields'\n            }, {\n                status: 400\n            });\n        }\n        // Update application status\n        const supabaseAdmin = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.createSupabaseAdmin)();\n        const { data: application, error } = await supabaseAdmin.from('cafeteria_applications').update({\n            status,\n            review_notes: reviewNotes,\n            reviewed_at: new Date().toISOString()\n        }).eq('id', applicationId).select().single();\n        if (error) {\n            console.error('Error updating cafeteria application:', error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to update application'\n            }, {\n                status: 500\n            });\n        }\n        // If approved, create user account, profile, and cafeteria record\n        if (status === 'approved') {\n            try {\n                // 1. Check if user already exists, if not create user account in auth.users\n                let authUserId;\n                // Check if user already exists\n                const { data: existingUsers } = await supabaseAdmin.auth.admin.listUsers({\n                    filter: `email.eq.${application.contact_email}`\n                });\n                if (existingUsers.users && existingUsers.users.length > 0) {\n                    // User already exists, use existing user ID\n                    authUserId = existingUsers.users[0].id;\n                    console.log('Using existing auth user:', authUserId);\n                    // Update password for existing user\n                    const { error: updateError } = await supabaseAdmin.auth.admin.updateUserById(authUserId, {\n                        password: application.temp_password\n                    });\n                    if (updateError) {\n                        console.error('Error updating password:', updateError);\n                    }\n                } else {\n                    // Create new user\n                    const { data: authUser, error: authError } = await supabaseAdmin.auth.admin.createUser({\n                        email: application.contact_email,\n                        password: application.temp_password,\n                        email_confirm: true\n                    });\n                    if (authError) {\n                        console.error('Error creating auth user:', authError);\n                        throw authError;\n                    }\n                    authUserId = authUser.user.id;\n                    console.log('Created new auth user:', authUserId);\n                }\n                // 2. Create or update profile in profiles table\n                const { error: profileError } = await supabaseAdmin.from('profiles').upsert({\n                    id: authUserId,\n                    email: application.contact_email,\n                    full_name: application.owner_name,\n                    phone: application.contact_phone,\n                    role: 'cafeteria_manager',\n                    created_at: new Date().toISOString(),\n                    updated_at: new Date().toISOString()\n                });\n                if (profileError) {\n                    console.error('Error creating profile:', profileError);\n                    throw profileError;\n                }\n                console.log('Created/updated profile for user:', authUserId);\n                // 3. Check if cafeteria already exists for this owner and application\n                const { data: existingCafeteria, error: checkError } = await supabaseAdmin.from('cafeterias').select('id, name, owner_id').eq('owner_id', authUserId).eq('name', application.business_name).single();\n                if (checkError && checkError.code !== 'PGRST116') {\n                    console.error('Error checking existing cafeteria:', checkError);\n                    throw checkError;\n                }\n                if (existingCafeteria) {\n                    // Cafeteria already exists, just update its status\n                    console.log('Cafeteria already exists, updating status:', existingCafeteria.id);\n                    const { error: updateError } = await supabaseAdmin.from('cafeterias').update({\n                        approval_status: 'approved',\n                        is_active: true,\n                        is_open: true,\n                        // Update other fields in case they changed\n                        location: application.location,\n                        description: application.description\n                    }).eq('id', existingCafeteria.id);\n                    if (updateError) {\n                        console.error('Error updating existing cafeteria:', updateError);\n                        throw updateError;\n                    }\n                    console.log('Updated existing cafeteria record for:', application.business_name);\n                } else {\n                    // Create new cafeteria record\n                    console.log('Creating new cafeteria record for:', application.business_name);\n                    const { error: cafeteriaError } = await supabaseAdmin.from('cafeterias').insert({\n                        name: application.business_name,\n                        location: application.location,\n                        description: application.description,\n                        owner_id: authUserId,\n                        approval_status: 'approved',\n                        is_active: true,\n                        is_open: true,\n                        rating: 0,\n                        created_at: new Date().toISOString()\n                    });\n                    if (cafeteriaError) {\n                        console.error('Error creating cafeteria record:', cafeteriaError);\n                        throw cafeteriaError;\n                    }\n                    console.log('Created new cafeteria record for:', application.business_name);\n                }\n                // 4. Clear the temporary password from the application record\n                await supabaseAdmin.from('cafeteria_applications').update({\n                    temp_password: null\n                }).eq('id', applicationId);\n            } catch (error) {\n                console.error('Error in approval process:', error);\n                // If any step fails, we should probably revert the status\n                await supabaseAdmin.from('cafeteria_applications').update({\n                    status: 'pending',\n                    review_notes: `Approval failed: ${error}`\n                }).eq('id', applicationId);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Failed to complete approval process'\n                }, {\n                    status: 500\n                });\n            }\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: `Application ${status} successfully`,\n            application\n        });\n    } catch (error) {\n        console.error('Error in cafeteria application PATCH API:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/cafeteria-applications/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/supabase.ts":
/*!*************************!*\
  !*** ./lib/supabase.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addMenuItem: () => (/* binding */ addMenuItem),\n/* harmony export */   assignChatToAgent: () => (/* binding */ assignChatToAgent),\n/* harmony export */   closeChatConversation: () => (/* binding */ closeChatConversation),\n/* harmony export */   convertTicketToChat: () => (/* binding */ convertTicketToChat),\n/* harmony export */   createCareer: () => (/* binding */ createCareer),\n/* harmony export */   createChatConversation: () => (/* binding */ createChatConversation),\n/* harmony export */   createExportLog: () => (/* binding */ createExportLog),\n/* harmony export */   createNotification: () => (/* binding */ createNotification),\n/* harmony export */   createOrderNotification: () => (/* binding */ createOrderNotification),\n/* harmony export */   createSupabaseAdmin: () => (/* binding */ createSupabaseAdmin),\n/* harmony export */   deleteCareer: () => (/* binding */ deleteCareer),\n/* harmony export */   deleteChartAnnotation: () => (/* binding */ deleteChartAnnotation),\n/* harmony export */   deleteInventoryItem: () => (/* binding */ deleteInventoryItem),\n/* harmony export */   deleteMenuItem: () => (/* binding */ deleteMenuItem),\n/* harmony export */   fetchAllSupportTicketsForAdmin: () => (/* binding */ fetchAllSupportTicketsForAdmin),\n/* harmony export */   fetchStudentMessages: () => (/* binding */ fetchStudentMessages),\n/* harmony export */   fetchSupportTickets: () => (/* binding */ fetchSupportTickets),\n/* harmony export */   fetchSupportTicketsByType: () => (/* binding */ fetchSupportTicketsByType),\n/* harmony export */   getAnalyticsData: () => (/* binding */ getAnalyticsData),\n/* harmony export */   getCafeterias: () => (/* binding */ getCafeterias),\n/* harmony export */   getCareers: () => (/* binding */ getCareers),\n/* harmony export */   getChartAnnotations: () => (/* binding */ getChartAnnotations),\n/* harmony export */   getChartConfigurations: () => (/* binding */ getChartConfigurations),\n/* harmony export */   getChatConversations: () => (/* binding */ getChatConversations),\n/* harmony export */   getChatForTicket: () => (/* binding */ getChatForTicket),\n/* harmony export */   getChatMessages: () => (/* binding */ getChatMessages),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   getInventoryItems: () => (/* binding */ getInventoryItems),\n/* harmony export */   getMenuItems: () => (/* binding */ getMenuItems),\n/* harmony export */   getOrders: () => (/* binding */ getOrders),\n/* harmony export */   getPublicSystemSettings: () => (/* binding */ getPublicSystemSettings),\n/* harmony export */   getSystemSetting: () => (/* binding */ getSystemSetting),\n/* harmony export */   getUserAnalyticsPreferences: () => (/* binding */ getUserAnalyticsPreferences),\n/* harmony export */   getUserExportLogs: () => (/* binding */ getUserExportLogs),\n/* harmony export */   getUserThemePreference: () => (/* binding */ getUserThemePreference),\n/* harmony export */   insertAnalyticsData: () => (/* binding */ insertAnalyticsData),\n/* harmony export */   logUserActivity: () => (/* binding */ logUserActivity),\n/* harmony export */   markMessagesAsRead: () => (/* binding */ markMessagesAsRead),\n/* harmony export */   saveChartAnnotation: () => (/* binding */ saveChartAnnotation),\n/* harmony export */   saveChartConfiguration: () => (/* binding */ saveChartConfiguration),\n/* harmony export */   saveInventoryItem: () => (/* binding */ saveInventoryItem),\n/* harmony export */   saveUserAnalyticsPreferences: () => (/* binding */ saveUserAnalyticsPreferences),\n/* harmony export */   saveUserThemePreference: () => (/* binding */ saveUserThemePreference),\n/* harmony export */   sendChatMessage: () => (/* binding */ sendChatMessage),\n/* harmony export */   signOut: () => (/* binding */ signOut),\n/* harmony export */   submitContactForm: () => (/* binding */ submitContactForm),\n/* harmony export */   submitStudentMessage: () => (/* binding */ submitStudentMessage),\n/* harmony export */   submitSupportTicket: () => (/* binding */ submitSupportTicket),\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   testSupabaseConnection: () => (/* binding */ testSupabaseConnection),\n/* harmony export */   trackAnalyticsEvent: () => (/* binding */ trackAnalyticsEvent),\n/* harmony export */   trackNavigation: () => (/* binding */ trackNavigation),\n/* harmony export */   updateCareer: () => (/* binding */ updateCareer),\n/* harmony export */   updateChartAnnotation: () => (/* binding */ updateChartAnnotation),\n/* harmony export */   updateExportLog: () => (/* binding */ updateExportLog),\n/* harmony export */   updateInventoryItem: () => (/* binding */ updateInventoryItem),\n/* harmony export */   updateMenuItem: () => (/* binding */ updateMenuItem),\n/* harmony export */   updateSystemSetting: () => (/* binding */ updateSystemSetting)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\nconst supabaseUrl = \"https://lqtnaxvqkoynaziiinqh.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImxxdG5heHZxa295bmF6aWlpbnFoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc1ODUzMjcsImV4cCI6MjA2MzE2MTMyN30.MEMp-4fuLCMKaW-E_g56vsYFNKqzrftjhYfD_w1u0PA\";\nif (!supabaseUrl || !supabaseAnonKey) {\n    throw new Error('Missing Supabase environment variables');\n}\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey, {\n    auth: {\n        autoRefreshToken: true,\n        persistSession: true,\n        detectSessionInUrl: true\n    }\n});\n// Service role client for admin operations (bypasses RLS) - SERVER SIDE ONLY\nconst createSupabaseAdmin = ()=>{\n    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n    if (!supabaseServiceKey) {\n        throw new Error('SUPABASE_SERVICE_ROLE_KEY is required for admin operations');\n    }\n    return (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseServiceKey, {\n        auth: {\n            autoRefreshToken: false,\n            persistSession: false\n        }\n    });\n};\n// Connection test function\nconst testSupabaseConnection = async ()=>{\n    try {\n        const { data, error } = await supabase.from('cafeterias').select('id, name').limit(1);\n        if (error) {\n            console.error('Supabase connection test failed:', error);\n            return {\n                success: false,\n                error: error.message\n            };\n        }\n        return {\n            success: true,\n            data\n        };\n    } catch (error) {\n        console.error('Supabase connection test error:', error);\n        return {\n            success: false,\n            error: 'Connection failed'\n        };\n    }\n};\n// Auth helper functions\nconst getCurrentUser = async ()=>{\n    try {\n        const { data: { user }, error } = await supabase.auth.getUser();\n        if (error) {\n            console.error('Error getting current user:', error);\n            return null;\n        }\n        if (!user) {\n            return null;\n        }\n        // Get user profile from profiles table\n        const { data: profile, error: profileError } = await supabase.from('profiles').select('*').eq('id', user.id).single();\n        if (profileError) {\n            console.error('Error getting user profile:', profileError);\n            // Check if user is admin based on email\n            const isAdmin = user.email === '<EMAIL>' || user.email?.includes('admin') || user.user_metadata?.role === 'admin';\n            // Return basic user info if profile fetch fails\n            return {\n                ...user,\n                full_name: user.user_metadata?.full_name || user.email?.split('@')[0] || 'User',\n                role: isAdmin ? 'admin' : user.user_metadata?.role || 'student',\n                avatar_url: null,\n                phone: null,\n                is_suspended: false,\n                suspension_reason: null,\n                status: 'active',\n                is_active: true\n            };\n        }\n        // Combine auth user with profile data\n        return {\n            ...user,\n            ...profile\n        };\n    } catch (error) {\n        console.error('Error getting current user:', error);\n        return null;\n    }\n};\nconst signOut = async ()=>{\n    try {\n        const { error } = await supabase.auth.signOut();\n        return {\n            error\n        };\n    } catch (error) {\n        console.error('Error signing out:', error);\n        return {\n            error: 'Sign out failed'\n        };\n    }\n};\n// Theme preference functions\nconst getUserThemePreference = async (userId)=>{\n    try {\n        const { data, error } = await supabase.from('theme_preferences').select('*').eq('user_id', userId).single();\n        if (error) {\n            // If table doesn't exist or no data, return default dark theme\n            console.log('Theme preferences not found, using default dark theme');\n            return {\n                id: '',\n                user_id: userId,\n                theme: 'dark',\n                auto_switch: false,\n                created_at: '',\n                updated_at: ''\n            };\n        }\n        return data;\n    } catch (error) {\n        console.log('Theme preferences error, using default dark theme:', error);\n        return {\n            id: '',\n            user_id: userId,\n            theme: 'dark',\n            auto_switch: false,\n            created_at: '',\n            updated_at: ''\n        };\n    }\n};\nconst saveUserThemePreference = async (userId, theme, autoSwitch = false)=>{\n    try {\n        const { error } = await supabase.from('theme_preferences').upsert({\n            user_id: userId,\n            theme,\n            auto_switch: autoSwitch,\n            updated_at: new Date().toISOString()\n        });\n        if (error) {\n            console.log('Theme preferences table not available, skipping save');\n            return true // Return true to not break the app\n            ;\n        }\n        return true;\n    } catch (error) {\n        console.log('Theme preferences save error, continuing without saving:', error);\n        return true // Return true to not break the app\n        ;\n    }\n};\n// Contact form functions\nconst submitContactForm = async (formData)=>{\n    const { error } = await supabase.from('contact_submissions').insert([\n        formData\n    ]);\n    if (error) {\n        console.error('Error submitting contact form:', error);\n        return false;\n    }\n    return true;\n};\n// System settings functions\nconst getSystemSetting = async (key)=>{\n    const { data, error } = await supabase.from('system_settings').select('setting_value').eq('setting_key', key).single();\n    if (error) {\n        console.error('Error fetching system setting:', error);\n        return null;\n    }\n    return data?.setting_value;\n};\nconst getPublicSystemSettings = async ()=>{\n    const { data, error } = await supabase.from('system_settings').select('setting_key, setting_value').eq('is_public', true);\n    if (error) {\n        console.error('Error fetching public system settings:', error);\n        return {};\n    }\n    const settings = {};\n    data?.forEach((setting)=>{\n        settings[setting.setting_key] = setting.setting_value;\n    });\n    return settings;\n};\nconst updateSystemSetting = async (key, value)=>{\n    const { error } = await supabase.from('system_settings').update({\n        setting_value: value,\n        updated_at: new Date().toISOString()\n    }).eq('setting_key', key);\n    if (error) {\n        console.error('Error updating system setting:', error);\n        return false;\n    }\n    return true;\n};\n// Chart annotation functions\nconst getChartAnnotations = async (chartId)=>{\n    const { data, error } = await supabase.from('chart_annotations').select('*').eq('chart_id', chartId).order('created_at', {\n        ascending: true\n    });\n    if (error) {\n        console.error('Error fetching chart annotations:', error);\n        return [];\n    }\n    return data || [];\n};\nconst saveChartAnnotation = async (annotation)=>{\n    const { error } = await supabase.from('chart_annotations').insert([\n        annotation\n    ]);\n    if (error) {\n        console.error('Error saving chart annotation:', error);\n        return false;\n    }\n    return true;\n};\nconst updateChartAnnotation = async (id, updates)=>{\n    const { error } = await supabase.from('chart_annotations').update({\n        ...updates,\n        updated_at: new Date().toISOString()\n    }).eq('id', id);\n    if (error) {\n        console.error('Error updating chart annotation:', error);\n        return false;\n    }\n    return true;\n};\nconst deleteChartAnnotation = async (id)=>{\n    const { error } = await supabase.from('chart_annotations').delete().eq('id', id);\n    if (error) {\n        console.error('Error deleting chart annotation:', error);\n        return false;\n    }\n    return true;\n};\n// Inventory functions\nconst getInventoryItems = async (cafeteriaId)=>{\n    const { data, error } = await supabase.from('inventory_items').select('*').eq('cafeteria_id', cafeteriaId).order('name', {\n        ascending: true\n    });\n    if (error) {\n        console.error('Error fetching inventory items:', error);\n        return [];\n    }\n    return data || [];\n};\nconst saveInventoryItem = async (item)=>{\n    const { error } = await supabase.from('inventory_items').insert([\n        item\n    ]);\n    if (error) {\n        console.error('Error saving inventory item:', error);\n        return false;\n    }\n    return true;\n};\nconst updateInventoryItem = async (id, updates)=>{\n    try {\n        console.log('🔥 INVENTORY UPDATE FUNCTION CALLED 🔥');\n        console.log('Updating inventory item:', {\n            id,\n            updates\n        });\n        // Check if user is authenticated\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        if (authError || !user) {\n            console.error('User not authenticated for inventory update:', authError);\n            return false;\n        }\n        console.log('Authenticated user for inventory update:', user.id);\n        const updateData = {\n            ...updates,\n            updated_at: new Date().toISOString()\n        };\n        // DETAILED DEBUGGING: Log everything about the status\n        console.log('=== INVENTORY UPDATE DEBUG ===');\n        console.log('Full updateData object:', JSON.stringify(updateData, null, 2));\n        console.log('updateData.status type:', typeof updateData.status);\n        console.log('updateData.status value:', updateData.status);\n        // Map status values to match database constraint exactly\n        if (updateData.status) {\n            console.log('Original status value:', updateData.status);\n            // Ensure we only use the exact values allowed by the constraint\n            const allowedStatuses = [\n                'in_stock',\n                'low_stock',\n                'out_of_stock',\n                'expired'\n            ];\n            if (!allowedStatuses.includes(updateData.status)) {\n                console.log('Invalid status value, mapping to valid one');\n                // Map common variations to valid values\n                const statusMapping = {\n                    'in-stock': 'in_stock',\n                    'low': 'low_stock',\n                    'out-of-stock': 'out_of_stock',\n                    'available': 'in_stock',\n                    'unavailable': 'out_of_stock'\n                };\n                updateData.status = statusMapping[updateData.status] || 'in_stock';\n            }\n            console.log('Final status value:', updateData.status);\n            console.log('Final status type:', typeof updateData.status);\n        }\n        console.log('Final update data being sent:', JSON.stringify(updateData, null, 2));\n        console.log('=== END DEBUG ===');\n        // Try to update the inventory item\n        // If there's an ambiguous column reference error, it's likely due to a missing inventory_alerts table\n        const { data, error } = await supabase.from('inventory_items').update(updateData).eq('id', id).select();\n        if (error) {\n            console.error('Error updating inventory item:', {\n                error,\n                message: error.message,\n                details: error.details,\n                hint: error.hint,\n                code: error.code\n            });\n            console.error('Error details JSON:', JSON.stringify(error, null, 2));\n            // Handle specific RLS policy error\n            if (error.code === '42501' && error.message?.includes('row-level security policy')) {\n                console.error('❌ RLS POLICY ERROR: The trigger cannot create alerts due to row-level security.');\n                console.error('💡 SOLUTION: We\\'ll handle alert creation in the application instead of the trigger.');\n            }\n            return false;\n        }\n        if (!data || data.length === 0) {\n            console.error('No inventory item found with ID or access denied:', id);\n            return false;\n        }\n        console.log('Inventory item updated successfully:', data);\n        // Handle alert creation manually since triggers have RLS issues\n        const updatedItem = data[0];\n        if (updatedItem.status === 'low_stock' || updatedItem.status === 'out_of_stock') {\n            console.log('Creating inventory alert for status:', updatedItem.status);\n            // Check if alert already exists\n            const { data: existingAlert } = await supabase.from('inventory_alerts').select('id').eq('inventory_item_id', updatedItem.id).eq('alert_type', updatedItem.status).eq('is_resolved', false).single();\n            if (!existingAlert) {\n                // Create new alert\n                const alertMessage = updatedItem.status === 'out_of_stock' ? `${updatedItem.name} is out of stock` : `${updatedItem.name} is running low (${updatedItem.quantity} ${updatedItem.unit} remaining)`;\n                const { error: alertError } = await supabase.from('inventory_alerts').insert({\n                    cafeteria_id: updatedItem.cafeteria_id,\n                    inventory_item_id: updatedItem.id,\n                    alert_type: updatedItem.status,\n                    message: alertMessage,\n                    is_resolved: false\n                });\n                if (alertError) {\n                    console.error('Error creating inventory alert:', alertError);\n                } else {\n                    console.log('✅ Inventory alert created successfully');\n                }\n            }\n        }\n        // Resolve alerts when status improves\n        if (updatedItem.status === 'in_stock') {\n            console.log('Resolving inventory alerts for improved status');\n            const { error: resolveError } = await supabase.from('inventory_alerts').update({\n                is_resolved: true,\n                resolved_at: new Date().toISOString()\n            }).eq('inventory_item_id', updatedItem.id).in('alert_type', [\n                'low_stock',\n                'out_of_stock'\n            ]).eq('is_resolved', false);\n            if (resolveError) {\n                console.error('Error resolving inventory alerts:', resolveError);\n            } else {\n                console.log('✅ Inventory alerts resolved successfully');\n            }\n        }\n        return true;\n    } catch (err) {\n        console.error('Unexpected error updating inventory item:', err);\n        return false;\n    }\n};\nconst deleteInventoryItem = async (id)=>{\n    const { error } = await supabase.from('inventory_items').delete().eq('id', id);\n    if (error) {\n        console.error('Error deleting inventory item:', error);\n        return false;\n    }\n    return true;\n};\n// Export log functions\nconst createExportLog = async (exportData)=>{\n    const { data, error } = await supabase.from('export_logs').insert([\n        exportData\n    ]).select('id').single();\n    if (error) {\n        console.error('Error creating export log:', error);\n        return null;\n    }\n    return data?.id || null;\n};\nconst updateExportLog = async (id, updates)=>{\n    const { error } = await supabase.from('export_logs').update(updates).eq('id', id);\n    if (error) {\n        console.error('Error updating export log:', error);\n        return false;\n    }\n    return true;\n};\nconst getUserExportLogs = async (userId)=>{\n    const { data, error } = await supabase.from('export_logs').select('*').eq('user_id', userId).order('created_at', {\n        ascending: false\n    });\n    if (error) {\n        console.error('Error fetching export logs:', error);\n        return [];\n    }\n    return data || [];\n};\n// Analytics data functions\nconst getAnalyticsData = async (cafeteriaId, metricType, startDate, endDate)=>{\n    const { data, error } = await supabase.from('analytics_data').select('date_recorded, metric_value').eq('cafeteria_id', cafeteriaId).eq('metric_type', metricType).gte('date_recorded', startDate).lte('date_recorded', endDate).order('date_recorded', {\n        ascending: true\n    });\n    if (error) {\n        console.error('Error fetching analytics data:', error);\n        return [];\n    }\n    return data?.map((item)=>({\n            date: item.date_recorded,\n            value: Number(item.metric_value)\n        })) || [];\n};\nconst insertAnalyticsData = async (cafeteriaId, metricType, value, date)=>{\n    const { error } = await supabase.from('analytics_data').insert([\n        {\n            cafeteria_id: cafeteriaId,\n            metric_type: metricType,\n            metric_value: value,\n            date_recorded: date || new Date().toISOString().split('T')[0]\n        }\n    ]);\n    if (error) {\n        console.error('Error inserting analytics data:', error);\n        return false;\n    }\n    return true;\n};\n// User analytics preferences functions\nconst getUserAnalyticsPreferences = async (userId)=>{\n    const { data, error } = await supabase.from('user_analytics_preferences').select('*').eq('user_id', userId).single();\n    if (error && error.code !== 'PGRST116') {\n        console.error('Error fetching user analytics preferences:', error);\n        return null;\n    }\n    return data;\n};\nconst saveUserAnalyticsPreferences = async (userId, preferences)=>{\n    const { error } = await supabase.from('user_analytics_preferences').upsert({\n        user_id: userId,\n        ...preferences,\n        updated_at: new Date().toISOString()\n    });\n    if (error) {\n        console.error('Error saving user analytics preferences:', error);\n        return false;\n    }\n    return true;\n};\n// Navigation tracking functions\nconst trackNavigation = async (userId, fromPage, toPage, timeSpent)=>{\n    const { error } = await supabase.from('navigation_logs').insert([\n        {\n            user_id: userId,\n            from_page: fromPage,\n            to_page: toPage,\n            time_spent_seconds: timeSpent\n        }\n    ]);\n    if (error) {\n        console.error('Error tracking navigation:', error);\n        return false;\n    }\n    return true;\n};\n// Notification functions\nconst createNotification = async (userId, title, message, type = 'info', relatedOrderId)=>{\n    const { error } = await supabase.from('notifications').insert([\n        {\n            user_id: userId,\n            title,\n            message,\n            type,\n            related_order_id: relatedOrderId,\n            is_read: false,\n            created_at: new Date().toISOString()\n        }\n    ]);\n    if (error) {\n        console.error('Error creating notification:', error);\n        return false;\n    }\n    return true;\n};\n// Create order notification for cafeteria owners\nconst createOrderNotification = async (cafeteriaId, orderNumber, customerName)=>{\n    try {\n        // Get cafeteria owner\n        const { data: cafeteria, error: cafeteriaError } = await supabase.from('cafeterias').select('owner_id, name').eq('id', cafeteriaId).single();\n        if (cafeteriaError || !cafeteria) {\n            console.error('Error fetching cafeteria:', cafeteriaError);\n            return false;\n        }\n        const title = 'New Order Received';\n        const message = `New order #${orderNumber} received${customerName ? ` from ${customerName}` : ''} at ${cafeteria.name}`;\n        return await createNotification(cafeteria.owner_id, title, message, 'order', orderNumber);\n    } catch (error) {\n        console.error('Error creating order notification:', error);\n        return false;\n    }\n};\n// Analytics events tracking\nconst trackAnalyticsEvent = async (userId, eventType, eventData, pageUrl)=>{\n    const { error } = await supabase.from('analytics_events').insert([\n        {\n            user_id: userId,\n            event_type: eventType,\n            event_data: eventData,\n            page_url: pageUrl,\n            user_agent: typeof navigator !== 'undefined' ? navigator.userAgent : null\n        }\n    ]);\n    if (error) {\n        console.error('Error tracking analytics event:', error);\n        return false;\n    }\n    return true;\n};\n// Chart configuration functions\nconst getChartConfigurations = async (userId)=>{\n    const { data, error } = await supabase.from('chart_configurations').select('*').eq('user_id', userId).order('created_at', {\n        ascending: false\n    });\n    if (error) {\n        console.error('Error fetching chart configurations:', error);\n        return [];\n    }\n    return data || [];\n};\nconst saveChartConfiguration = async (userId, chartType, title, configuration, isDefault = false)=>{\n    const { error } = await supabase.from('chart_configurations').insert([\n        {\n            user_id: userId,\n            chart_type: chartType,\n            chart_title: title,\n            configuration,\n            is_default: isDefault\n        }\n    ]);\n    if (error) {\n        console.error('Error saving chart configuration:', error);\n        return false;\n    }\n    return true;\n};\n// User activity logging\nconst logUserActivity = async (userId, activityType, description, entityType, entityId, metadata)=>{\n    const { error } = await supabase.from('user_activity_logs').insert([\n        {\n            user_id: userId,\n            activity_type: activityType,\n            activity_description: description,\n            entity_type: entityType,\n            entity_id: entityId,\n            metadata: metadata || {}\n        }\n    ]);\n    if (error) {\n        console.error('Error logging user activity:', error);\n        return false;\n    }\n    return true;\n};\nconst getCareers = async ()=>{\n    const { data, error } = await supabase.from('careers').select('*').eq('status', 'active').order('created_at', {\n        ascending: false\n    });\n    if (error) {\n        console.error('Error fetching careers:', error);\n        return [];\n    }\n    return data || [];\n};\nconst createCareer = async (career)=>{\n    const { error } = await supabase.from('careers').insert([\n        career\n    ]);\n    if (error) {\n        console.error('Error creating career:', error);\n        return false;\n    }\n    return true;\n};\nconst updateCareer = async (id, updates)=>{\n    const { error } = await supabase.from('careers').update({\n        ...updates,\n        updated_at: new Date().toISOString()\n    }).eq('id', id);\n    if (error) {\n        console.error('Error updating career:', error);\n        return false;\n    }\n    return true;\n};\nconst deleteCareer = async (id)=>{\n    const { error } = await supabase.from('careers').update({\n        status: 'inactive'\n    }).eq('id', id);\n    if (error) {\n        console.error('Error deleting career:', error);\n        return false;\n    }\n    return true;\n};\n// Menu Items functions\nconst getMenuItems = async (cafeteriaId)=>{\n    let query = supabase.from('menu_items').select('*').order('name', {\n        ascending: true\n    });\n    if (cafeteriaId) {\n        query = query.eq('cafeteria_id', cafeteriaId);\n    }\n    const { data, error } = await query;\n    if (error) {\n        console.error('Error fetching menu items:', error);\n        return [];\n    }\n    return data || [];\n};\nconst addMenuItem = async (menuItem)=>{\n    const { error } = await supabase.from('menu_items').insert([\n        menuItem\n    ]);\n    if (error) {\n        console.error('Error adding menu item:', error);\n        return false;\n    }\n    return true;\n};\nconst updateMenuItem = async (id, updates)=>{\n    const { error } = await supabase.from('menu_items').update({\n        ...updates,\n        updated_at: new Date().toISOString()\n    }).eq('id', id);\n    if (error) {\n        console.error('Error updating menu item:', error);\n        return false;\n    }\n    return true;\n};\nconst deleteMenuItem = async (id)=>{\n    const { error } = await supabase.from('menu_items').delete().eq('id', id);\n    if (error) {\n        console.error('Error deleting menu item:', error);\n        return false;\n    }\n    return true;\n};\n// Cafeterias functions\nconst getCafeterias = async ()=>{\n    const { data, error } = await supabase.from('cafeterias').select('*').order('name', {\n        ascending: true\n    });\n    if (error) {\n        console.error('Error fetching cafeterias:', error);\n        return [];\n    }\n    return data || [];\n};\n// Support Tickets functions\nconst submitSupportTicket = async (ticket)=>{\n    // Generate a unique ticket number\n    const ticketNumber = `TKT-${Date.now()}-${Math.random().toString(36).substr(2, 5).toUpperCase()}`;\n    // Get user role if user_type not provided\n    let userType = ticket.user_type;\n    if (!userType) {\n        const { data: profile } = await supabase.from('profiles').select('role').eq('id', ticket.user_id).single();\n        if (profile?.role === 'cafeteria_manager') {\n            userType = 'cafeteria';\n        } else if (profile?.role === 'admin') {\n            userType = 'admin';\n        } else {\n            userType = 'student';\n        }\n    }\n    const ticketData = {\n        ticket_number: ticketNumber,\n        user_id: ticket.user_id,\n        title: ticket.title,\n        description: ticket.description,\n        category: ticket.category || 'general_inquiry',\n        priority: ticket.priority,\n        status: ticket.status || 'open',\n        user_type: userType,\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString()\n    };\n    const { error } = await supabase.from('support_tickets').insert([\n        ticketData\n    ]);\n    if (error) {\n        console.error('Error submitting support ticket:', error);\n        return false;\n    }\n    return true;\n};\nconst fetchSupportTickets = async (userId)=>{\n    try {\n        // First try with the join\n        let query = supabase.from('support_tickets').select(`\n        *,\n        profiles!user_id(\n          full_name,\n          role,\n          phone\n        )\n      `).order('created_at', {\n            ascending: false\n        });\n        if (userId) {\n            query = query.eq('user_id', userId);\n        }\n        const { data, error } = await query;\n        if (error) {\n            console.error('Error fetching support tickets with profiles join:', error);\n            console.error('Error details:', {\n                message: error.message,\n                details: error.details,\n                hint: error.hint,\n                code: error.code\n            });\n            // Fallback: try without the join\n            console.log('Trying fallback query without profiles join...');\n            let fallbackQuery = supabase.from('support_tickets').select('*').order('created_at', {\n                ascending: false\n            });\n            if (userId) {\n                fallbackQuery = fallbackQuery.eq('user_id', userId);\n            }\n            const { data: fallbackData, error: fallbackError } = await fallbackQuery;\n            if (fallbackError) {\n                console.error('Fallback query also failed:', fallbackError);\n                return [];\n            }\n            console.log('Fallback query successful, returning data without profile info');\n            return fallbackData || [];\n        }\n        return data || [];\n    } catch (error) {\n        console.error('Unexpected error in fetchSupportTickets:', error);\n        return [];\n    }\n};\n// Admin function to fetch all tickets with user information\nconst fetchAllSupportTicketsForAdmin = async ()=>{\n    try {\n        // First get support tickets with profiles\n        const { data: tickets, error: ticketsError } = await supabase.from('support_tickets').select(`\n        *,\n        profiles(\n          full_name,\n          role,\n          phone\n        )\n      `).order('created_at', {\n            ascending: false\n        });\n        if (ticketsError) {\n            console.error('Error fetching support tickets:', ticketsError);\n            return [];\n        }\n        // Then get user emails from auth.users\n        const userIds = tickets?.map((ticket)=>ticket.user_id).filter(Boolean) || [];\n        if (userIds.length === 0) {\n            return tickets || [];\n        }\n        const { data: authUsers, error: authError } = await supabase.auth.admin.listUsers();\n        if (authError) {\n            console.error('Error fetching auth users:', authError);\n            return tickets || [];\n        }\n        // Combine the data\n        const ticketsWithEmails = tickets?.map((ticket)=>({\n                ...ticket,\n                user_email: authUsers.users.find((user)=>user.id === ticket.user_id)?.email || 'No email'\n            })) || [];\n        return ticketsWithEmails;\n    } catch (error) {\n        console.error('Error in fetchAllSupportTicketsForAdmin:', error);\n        return [];\n    }\n};\n// Fetch tickets by user type (for admin dashboard)\nconst fetchSupportTicketsByType = async (userType)=>{\n    const { data, error } = await supabase.from('support_tickets').select(`\n      *,\n      profiles(\n        full_name,\n        role,\n        phone\n      )\n    `).eq('user_type', userType).order('created_at', {\n        ascending: false\n    });\n    if (error) {\n        console.error(`Error fetching ${userType} support tickets:`, error);\n        return [];\n    }\n    return data || [];\n};\n// Legacy functions for backward compatibility\nconst submitStudentMessage = async (message)=>{\n    return await submitSupportTicket({\n        user_id: message.user_id,\n        title: message.subject,\n        description: message.content,\n        category: message.user_type,\n        priority: message.priority,\n        status: message.status\n    });\n};\nconst fetchStudentMessages = async ()=>{\n    try {\n        console.log('Fetching student messages...');\n        const tickets = await fetchSupportTickets();\n        console.log('Successfully fetched tickets:', tickets.length);\n        return tickets;\n    } catch (error) {\n        console.error('Error in fetchStudentMessages:', error);\n        return [];\n    }\n};\n// Chat System Functions\nconst createChatConversation = async (conversation)=>{\n    // Get user role if user_type not provided\n    let userType = conversation.user_type;\n    if (!userType) {\n        const { data: profile } = await supabase.from('profiles').select('role').eq('id', conversation.user_id).single();\n        if (profile?.role === 'cafeteria_manager') {\n            userType = 'cafeteria';\n        } else if (profile?.role === 'admin') {\n            userType = 'admin';\n        } else {\n            userType = 'student';\n        }\n    }\n    const { data, error } = await supabase.from('chat_conversations').insert([\n        {\n            user_id: conversation.user_id,\n            subject: conversation.subject,\n            category: conversation.category || 'general_inquiry',\n            priority: conversation.priority || 'medium',\n            user_type: userType,\n            order_id: conversation.order_id || null,\n            ticket_id: conversation.ticket_id || null,\n            status: 'open'\n        }\n    ]).select('id').single();\n    if (error) {\n        console.error('Error creating chat conversation:', error);\n        return null;\n    }\n    return data?.id || null;\n};\nconst sendChatMessage = async (message)=>{\n    const { error } = await supabase.from('chat_messages').insert([\n        {\n            conversation_id: message.conversation_id,\n            sender_id: message.sender_id,\n            content: message.content,\n            message_type: message.message_type || 'text',\n            file_url: message.file_url || null,\n            file_name: message.file_name || null,\n            file_size: message.file_size || null,\n            is_read: false\n        }\n    ]);\n    if (error) {\n        console.error('Error sending chat message:', error);\n        return false;\n    }\n    // Update conversation updated_at\n    await supabase.from('chat_conversations').update({\n        updated_at: new Date().toISOString()\n    }).eq('id', message.conversation_id);\n    return true;\n};\nconst getChatConversations = async (userId, userType)=>{\n    let query = supabase.from('chat_conversations').select(`\n      *,\n      profiles!chat_conversations_user_id_fkey(\n        full_name,\n        email,\n        role\n      ),\n      support_agent:profiles!chat_conversations_support_agent_id_fkey(\n        full_name,\n        email\n      )\n    `).order('updated_at', {\n        ascending: false\n    });\n    if (userId) {\n        query = query.eq('user_id', userId);\n    }\n    if (userType) {\n        query = query.eq('user_type', userType);\n    }\n    const { data, error } = await query;\n    if (error) {\n        console.error('Error fetching chat conversations:', error);\n        return [];\n    }\n    return data || [];\n};\nconst getChatMessages = async (conversationId)=>{\n    const { data, error } = await supabase.from('chat_messages').select(`\n      *,\n      sender:profiles!chat_messages_sender_id_fkey(\n        full_name,\n        email,\n        role\n      )\n    `).eq('conversation_id', conversationId).order('created_at', {\n        ascending: true\n    });\n    if (error) {\n        console.error('Error fetching chat messages:', error);\n        return [];\n    }\n    return data || [];\n};\nconst markMessagesAsRead = async (conversationId, userId)=>{\n    const { error } = await supabase.from('chat_messages').update({\n        is_read: true\n    }).eq('conversation_id', conversationId).neq('sender_id', userId);\n    if (error) {\n        console.error('Error marking messages as read:', error);\n        return false;\n    }\n    return true;\n};\nconst assignChatToAgent = async (conversationId, agentId)=>{\n    const { error } = await supabase.from('chat_conversations').update({\n        support_agent_id: agentId,\n        status: 'in_progress',\n        updated_at: new Date().toISOString()\n    }).eq('id', conversationId);\n    if (error) {\n        console.error('Error assigning chat to agent:', error);\n        return false;\n    }\n    return true;\n};\nconst closeChatConversation = async (conversationId, rating, feedback)=>{\n    const updateData = {\n        status: 'closed',\n        closed_at: new Date().toISOString(),\n        updated_at: new Date().toISOString()\n    };\n    if (rating) updateData.rating = rating;\n    if (feedback) updateData.feedback = feedback;\n    const { error } = await supabase.from('chat_conversations').update(updateData).eq('id', conversationId);\n    if (error) {\n        console.error('Error closing chat conversation:', error);\n        return false;\n    }\n    return true;\n};\n// Convert support ticket to chat conversation\nconst convertTicketToChat = async (ticketId)=>{\n    // Get ticket details\n    const { data: ticket, error: ticketError } = await supabase.from('support_tickets').select('*').eq('id', ticketId).single();\n    if (ticketError || !ticket) {\n        console.error('Error fetching ticket for conversion:', ticketError);\n        return null;\n    }\n    // Create chat conversation linked to ticket\n    const conversationId = await createChatConversation({\n        user_id: ticket.user_id,\n        subject: ticket.title,\n        category: ticket.category,\n        priority: ticket.priority,\n        user_type: ticket.user_type,\n        order_id: ticket.order_id,\n        ticket_id: ticketId\n    });\n    if (conversationId) {\n        // Add initial message with ticket description\n        await sendChatMessage({\n            conversation_id: conversationId,\n            sender_id: ticket.user_id,\n            content: `Original ticket: ${ticket.description}`,\n            message_type: 'text'\n        });\n        // Update ticket status to indicate it has a chat\n        await supabase.from('support_tickets').update({\n            status: 'in_progress',\n            updated_at: new Date().toISOString()\n        }).eq('id', ticketId);\n    }\n    return conversationId;\n};\n// Get chat conversation for a ticket\nconst getChatForTicket = async (ticketId)=>{\n    const { data, error } = await supabase.from('chat_conversations').select(`\n      *,\n      profiles!chat_conversations_user_id_fkey(\n        full_name,\n        email,\n        role\n      ),\n      support_agent:profiles!chat_conversations_support_agent_id_fkey(\n        full_name,\n        email\n      )\n    `).eq('ticket_id', ticketId).single();\n    if (error) {\n        console.error('Error fetching chat for ticket:', error);\n        return null;\n    }\n    return data;\n};\n// Orders functions\nconst getOrders = async (cafeteriaId)=>{\n    let query = supabase.from('orders').select('*').order('created_at', {\n        ascending: false\n    });\n    if (cafeteriaId) {\n        query = query.eq('cafeteria_id', cafeteriaId);\n    }\n    const { data, error } = await query;\n    if (error) {\n        console.error('Error fetching orders:', error);\n        return [];\n    }\n    return data || [];\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./lib/supabase.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcafeteria-applications%2Froute&page=%2Fapi%2Fcafeteria-applications%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcafeteria-applications%2Froute.ts&appDir=C%3A%5CUsers%5Clkmd2%5CDesktop%5Cweb%20and%20mob%5Cgradproject%20v5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Clkmd2%5CDesktop%5Cweb%20and%20mob%5Cgradproject%20v5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcafeteria-applications%2Froute&page=%2Fapi%2Fcafeteria-applications%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcafeteria-applications%2Froute.ts&appDir=C%3A%5CUsers%5Clkmd2%5CDesktop%5Cweb%20and%20mob%5Cgradproject%20v5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Clkmd2%5CDesktop%5Cweb%20and%20mob%5Cgradproject%20v5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_lkmd2_Desktop_web_and_mob_gradproject_v5_app_api_cafeteria_applications_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/cafeteria-applications/route.ts */ \"(rsc)/./app/api/cafeteria-applications/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/cafeteria-applications/route\",\n        pathname: \"/api/cafeteria-applications\",\n        filename: \"route\",\n        bundlePath: \"app/api/cafeteria-applications/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\web and mob\\\\gradproject v5\\\\app\\\\api\\\\cafeteria-applications\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_lkmd2_Desktop_web_and_mob_gradproject_v5_app_api_cafeteria_applications_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcafeteria-applications%2Froute&page=%2Fapi%2Fcafeteria-applications%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcafeteria-applications%2Froute.ts&appDir=C%3A%5CUsers%5Clkmd2%5CDesktop%5Cweb%20and%20mob%5Cgradproject%20v5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Clkmd2%5CDesktop%5Cweb%20and%20mob%5Cgradproject%20v5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcafeteria-applications%2Froute&page=%2Fapi%2Fcafeteria-applications%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcafeteria-applications%2Froute.ts&appDir=C%3A%5CUsers%5Clkmd2%5CDesktop%5Cweb%20and%20mob%5Cgradproject%20v5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Clkmd2%5CDesktop%5Cweb%20and%20mob%5Cgradproject%20v5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();