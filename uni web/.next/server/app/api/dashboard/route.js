/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/dashboard/route";
exports.ids = ["app/api/dashboard/route"];
exports.modules = {

/***/ "(rsc)/./app/api/dashboard/route.ts":
/*!************************************!*\
  !*** ./app/api/dashboard/route.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase */ \"(rsc)/./lib/supabase.ts\");\n\n\n// Revenue model: 4% service fee from users (capped at 20 EGP) + 10% commission from cafeterias\n// Revenue is now calculated and stored in the orders table as user_service_fee, cafeteria_commission, admin_revenue\nasync function GET(request) {\n    try {\n        const supabaseAdmin = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.createSupabaseAdmin)();\n        const { searchParams } = new URL(request.url);\n        const timeRange = searchParams.get('timeRange') || 'This Month';\n        const cafeteriaId = searchParams.get('cafeteriaId');\n        // Calculate date range based on timeRange\n        const now = new Date();\n        let startDate;\n        let endDate = now;\n        switch(timeRange){\n            case 'Today':\n                startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());\n                break;\n            case 'This Week':\n                const dayOfWeek = now.getDay();\n                startDate = new Date(now.getTime() - dayOfWeek * 24 * 60 * 60 * 1000);\n                startDate.setHours(0, 0, 0, 0);\n                break;\n            case 'This Month':\n                startDate = new Date(now.getFullYear(), now.getMonth(), 1);\n                break;\n            case 'This Quarter':\n                const quarter = Math.floor(now.getMonth() / 3);\n                startDate = new Date(now.getFullYear(), quarter * 3, 1);\n                break;\n            case 'This Year':\n                startDate = new Date(now.getFullYear(), 0, 1);\n                break;\n            default:\n                startDate = new Date(now.getFullYear(), now.getMonth(), 1);\n        }\n        // Fetch all orders (including all statuses for comprehensive data)\n        let ordersQuery = supabaseAdmin.from('orders').select('*').order('created_at', {\n            ascending: false\n        });\n        // Apply date filter based on time range\n        if (timeRange === 'This Year') {\n            // For \"This Year\", show all data from the beginning of current year\n            const yearStart = new Date(now.getFullYear(), 0, 1);\n            ordersQuery = ordersQuery.gte('created_at', yearStart.toISOString());\n        } else if (timeRange !== 'All Time') {\n            // For other ranges, apply the calculated date filter\n            ordersQuery = ordersQuery.gte('created_at', startDate.toISOString()).lte('created_at', endDate.toISOString());\n        }\n        // For \"All Time\", no date filter is applied\n        if (cafeteriaId && cafeteriaId !== 'all') {\n            ordersQuery = ordersQuery.eq('cafeteria_id', cafeteriaId);\n        }\n        const { data: allOrders, error: ordersError } = await ordersQuery;\n        if (ordersError) {\n            console.error('Error fetching orders:', ordersError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to fetch orders'\n            }, {\n                status: 500\n            });\n        }\n        // Filter orders for metrics (exclude ALL cancelled orders)\n        const orders = allOrders?.filter((order)=>order.status !== 'cancelled') || [];\n        console.log('Admin Dashboard - Orders fetched:', {\n            total: allOrders?.length || 0,\n            filtered: orders.length,\n            timeRange,\n            dateRange: `${startDate.toISOString()} to ${endDate.toISOString()}`,\n            sampleOrder: orders[0]\n        });\n        // Fetch cafeterias\n        const { data: cafeterias, error: cafeteriasError } = await supabaseAdmin.from('cafeterias').select('id, name, is_active, approval_status');\n        if (cafeteriasError) {\n            console.error('Error fetching cafeterias:', cafeteriasError);\n        }\n        // Fetch total users count from profiles\n        const { count: totalUsers, error: usersError } = await supabaseAdmin.from('profiles').select('*', {\n            count: 'exact',\n            head: true\n        });\n        if (usersError) {\n            console.error('Error fetching users count:', usersError);\n        }\n        // Calculate metrics\n        const totalOrders = orders?.length || 0;\n        const totalOrderValue = orders?.reduce((sum, order)=>{\n            const amount = parseFloat(order.total_amount) || 0;\n            return sum + amount;\n        }, 0) || 0;\n        // Calculate revenue using the admin_revenue field (which already contains userFee + commission)\n        const totalRevenue = orders?.reduce((sum, order)=>{\n            const adminRevenue = parseFloat(order.admin_revenue) || 0;\n            return sum + adminRevenue;\n        }, 0) || 0;\n        console.log('Admin Dashboard - Final metrics:', {\n            totalOrders,\n            totalOrderValue,\n            totalRevenue,\n            activeCafeterias: cafeterias?.filter((c)=>c.is_active && c.approval_status === 'approved').length || 0,\n            totalUsers: totalUsers || 0,\n            hasData: orders.length > 0\n        });\n        // Generate chart data for the last 12 months\n        const chartMonths = Array.from({\n            length: 12\n        }, (_, i)=>{\n            const date = new Date();\n            date.setMonth(date.getMonth() - (11 - i));\n            return date;\n        });\n        // Fetch orders for chart data (last 12 months) - include all meaningful orders\n        const chartStartDate = new Date();\n        chartStartDate.setMonth(chartStartDate.getMonth() - 11);\n        chartStartDate.setDate(1);\n        chartStartDate.setHours(0, 0, 0, 0);\n        const { data: allChartOrders } = await supabaseAdmin.from('orders').select('total_amount, created_at, cafeteria_id, user_service_fee, cafeteria_commission, admin_revenue, status').gte('created_at', chartStartDate.toISOString()).order('created_at', {\n            ascending: false\n        });\n        // Filter chart orders (exclude ALL cancelled orders)\n        const chartOrders = allChartOrders?.filter((order)=>order.status !== 'cancelled') || [];\n        console.log('Admin Dashboard - Chart data:', {\n            totalChartOrders: allChartOrders?.length || 0,\n            filteredChartOrders: chartOrders.length,\n            chartStartDate: chartStartDate.toISOString()\n        });\n        // Process chart data\n        const revenueChart = chartMonths.map((month)=>{\n            const monthStart = new Date(month.getFullYear(), month.getMonth(), 1);\n            const monthEnd = new Date(month.getFullYear(), month.getMonth() + 1, 0, 23, 59, 59);\n            const monthOrders = chartOrders?.filter((order)=>{\n                const orderDate = new Date(order.created_at);\n                return orderDate >= monthStart && orderDate <= monthEnd;\n            }) || [];\n            return monthOrders.reduce((sum, order)=>{\n                const adminRevenue = parseFloat(order.admin_revenue) || 0;\n                return sum + adminRevenue;\n            }, 0);\n        });\n        const ordersChart = chartMonths.map((month)=>{\n            const monthStart = new Date(month.getFullYear(), month.getMonth(), 1);\n            const monthEnd = new Date(month.getFullYear(), month.getMonth() + 1, 0, 23, 59, 59);\n            return chartOrders?.filter((order)=>{\n                const orderDate = new Date(order.created_at);\n                return orderDate >= monthStart && orderDate <= monthEnd;\n            }).length || 0;\n        });\n        // For users chart, simulate growth over time\n        const usersChart = chartMonths.map((_, index)=>{\n            const baseUsers = totalUsers || 0;\n            const growthFactor = 0.7 + index * 0.025 // Gradual growth\n            ;\n            return Math.floor(baseUsers * growthFactor);\n        });\n        // Calculate cafeteria-specific metrics\n        const cafeteriaMetrics = cafeterias?.map((cafeteria)=>{\n            const cafeteriaOrders = orders?.filter((order)=>order.cafeteria_id === cafeteria.id) || [];\n            const cafeteriaOrderValue = cafeteriaOrders.reduce((sum, order)=>{\n                const amount = parseFloat(order.total_amount) || 0;\n                return sum + amount;\n            }, 0);\n            // Admin revenue (platform's cut)\n            const adminRevenue = cafeteriaOrders.reduce((sum, order)=>{\n                const adminRev = parseFloat(order.admin_revenue) || 0;\n                return sum + adminRev;\n            }, 0);\n            // Actual cafeteria revenue (total - admin cut)\n            const actualCafeteriaRevenue = cafeteriaOrderValue - adminRevenue;\n            // Estimate users per cafeteria (simplified)\n            const estimatedUsers = Math.floor((totalUsers || 0) / (cafeterias?.length || 1));\n            return {\n                id: cafeteria.id,\n                name: cafeteria.name,\n                status: cafeteria.is_active ? 'active' : 'inactive',\n                users: estimatedUsers,\n                orders: cafeteriaOrders.length,\n                orderValue: cafeteriaOrderValue,\n                revenue: adminRevenue,\n                cafeteriaRevenue: actualCafeteriaRevenue // Cafeteria's actual revenue\n            };\n        }) || [];\n        // Get active cafeterias count\n        const activeCafeterias = cafeterias?.filter((c)=>c.is_active && c.approval_status === 'approved').length || 0;\n        // Calculate revenue breakdown using existing fields\n        const userServiceFees = orders?.reduce((sum, order)=>{\n            return sum + (parseFloat(order.user_service_fee) || 0);\n        }, 0) || 0;\n        const cafeteriaCommissions = orders?.reduce((sum, order)=>{\n            return sum + (parseFloat(order.cafeteria_commission) || 0);\n        }, 0) || 0;\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            metrics: {\n                totalCafeterias: cafeterias?.length || 0,\n                activeCafeterias,\n                totalUsers: totalUsers || 0,\n                totalOrders,\n                totalOrderValue,\n                totalRevenue,\n                userServiceFees,\n                cafeteriaCommissions\n            },\n            charts: {\n                revenue: revenueChart,\n                orders: ordersChart,\n                users: usersChart,\n                months: chartMonths.map((month)=>month.toLocaleDateString('en-US', {\n                        month: 'short'\n                    }))\n            },\n            cafeterias: cafeteriaMetrics,\n            timeRange,\n            dateRange: {\n                start: startDate.toISOString(),\n                end: endDate.toISOString()\n            }\n        });\n    } catch (error) {\n        console.error('Error in dashboard API:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST - Update dashboard settings or trigger data refresh\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { action } = body;\n        if (action === 'refresh') {\n            // Trigger a data refresh\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                message: 'Dashboard data refreshed successfully'\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Invalid action'\n        }, {\n            status: 400\n        });\n    } catch (error) {\n        console.error('Error in dashboard POST:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/dashboard/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/supabase.ts":
/*!*************************!*\
  !*** ./lib/supabase.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addMenuItem: () => (/* binding */ addMenuItem),\n/* harmony export */   assignChatToAgent: () => (/* binding */ assignChatToAgent),\n/* harmony export */   closeChatConversation: () => (/* binding */ closeChatConversation),\n/* harmony export */   convertTicketToChat: () => (/* binding */ convertTicketToChat),\n/* harmony export */   createCareer: () => (/* binding */ createCareer),\n/* harmony export */   createChatConversation: () => (/* binding */ createChatConversation),\n/* harmony export */   createExportLog: () => (/* binding */ createExportLog),\n/* harmony export */   createNotification: () => (/* binding */ createNotification),\n/* harmony export */   createOrderNotification: () => (/* binding */ createOrderNotification),\n/* harmony export */   createSupabaseAdmin: () => (/* binding */ createSupabaseAdmin),\n/* harmony export */   deleteCareer: () => (/* binding */ deleteCareer),\n/* harmony export */   deleteChartAnnotation: () => (/* binding */ deleteChartAnnotation),\n/* harmony export */   deleteInventoryItem: () => (/* binding */ deleteInventoryItem),\n/* harmony export */   deleteMenuItem: () => (/* binding */ deleteMenuItem),\n/* harmony export */   fetchAllSupportTicketsForAdmin: () => (/* binding */ fetchAllSupportTicketsForAdmin),\n/* harmony export */   fetchStudentMessages: () => (/* binding */ fetchStudentMessages),\n/* harmony export */   fetchSupportTickets: () => (/* binding */ fetchSupportTickets),\n/* harmony export */   fetchSupportTicketsByType: () => (/* binding */ fetchSupportTicketsByType),\n/* harmony export */   getAnalyticsData: () => (/* binding */ getAnalyticsData),\n/* harmony export */   getCafeterias: () => (/* binding */ getCafeterias),\n/* harmony export */   getCareers: () => (/* binding */ getCareers),\n/* harmony export */   getChartAnnotations: () => (/* binding */ getChartAnnotations),\n/* harmony export */   getChartConfigurations: () => (/* binding */ getChartConfigurations),\n/* harmony export */   getChatConversations: () => (/* binding */ getChatConversations),\n/* harmony export */   getChatForTicket: () => (/* binding */ getChatForTicket),\n/* harmony export */   getChatMessages: () => (/* binding */ getChatMessages),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   getInventoryItems: () => (/* binding */ getInventoryItems),\n/* harmony export */   getMenuItems: () => (/* binding */ getMenuItems),\n/* harmony export */   getOrders: () => (/* binding */ getOrders),\n/* harmony export */   getPublicSystemSettings: () => (/* binding */ getPublicSystemSettings),\n/* harmony export */   getSystemSetting: () => (/* binding */ getSystemSetting),\n/* harmony export */   getUserAnalyticsPreferences: () => (/* binding */ getUserAnalyticsPreferences),\n/* harmony export */   getUserExportLogs: () => (/* binding */ getUserExportLogs),\n/* harmony export */   getUserThemePreference: () => (/* binding */ getUserThemePreference),\n/* harmony export */   insertAnalyticsData: () => (/* binding */ insertAnalyticsData),\n/* harmony export */   logUserActivity: () => (/* binding */ logUserActivity),\n/* harmony export */   markMessagesAsRead: () => (/* binding */ markMessagesAsRead),\n/* harmony export */   saveChartAnnotation: () => (/* binding */ saveChartAnnotation),\n/* harmony export */   saveChartConfiguration: () => (/* binding */ saveChartConfiguration),\n/* harmony export */   saveInventoryItem: () => (/* binding */ saveInventoryItem),\n/* harmony export */   saveUserAnalyticsPreferences: () => (/* binding */ saveUserAnalyticsPreferences),\n/* harmony export */   saveUserThemePreference: () => (/* binding */ saveUserThemePreference),\n/* harmony export */   sendChatMessage: () => (/* binding */ sendChatMessage),\n/* harmony export */   signOut: () => (/* binding */ signOut),\n/* harmony export */   submitContactForm: () => (/* binding */ submitContactForm),\n/* harmony export */   submitStudentMessage: () => (/* binding */ submitStudentMessage),\n/* harmony export */   submitSupportTicket: () => (/* binding */ submitSupportTicket),\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   testSupabaseConnection: () => (/* binding */ testSupabaseConnection),\n/* harmony export */   trackAnalyticsEvent: () => (/* binding */ trackAnalyticsEvent),\n/* harmony export */   trackNavigation: () => (/* binding */ trackNavigation),\n/* harmony export */   updateCareer: () => (/* binding */ updateCareer),\n/* harmony export */   updateChartAnnotation: () => (/* binding */ updateChartAnnotation),\n/* harmony export */   updateExportLog: () => (/* binding */ updateExportLog),\n/* harmony export */   updateInventoryItem: () => (/* binding */ updateInventoryItem),\n/* harmony export */   updateMenuItem: () => (/* binding */ updateMenuItem),\n/* harmony export */   updateSystemSetting: () => (/* binding */ updateSystemSetting)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\nconst supabaseUrl = \"https://lqtnaxvqkoynaziiinqh.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImxxdG5heHZxa295bmF6aWlpbnFoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc1ODUzMjcsImV4cCI6MjA2MzE2MTMyN30.MEMp-4fuLCMKaW-E_g56vsYFNKqzrftjhYfD_w1u0PA\";\nif (!supabaseUrl || !supabaseAnonKey) {\n    throw new Error('Missing Supabase environment variables');\n}\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey, {\n    auth: {\n        autoRefreshToken: true,\n        persistSession: true,\n        detectSessionInUrl: true\n    }\n});\n// Service role client for admin operations (bypasses RLS) - SERVER SIDE ONLY\nconst createSupabaseAdmin = ()=>{\n    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n    if (!supabaseServiceKey) {\n        throw new Error('SUPABASE_SERVICE_ROLE_KEY is required for admin operations');\n    }\n    return (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseServiceKey, {\n        auth: {\n            autoRefreshToken: false,\n            persistSession: false\n        }\n    });\n};\n// Connection test function\nconst testSupabaseConnection = async ()=>{\n    try {\n        const { data, error } = await supabase.from('cafeterias').select('id, name').limit(1);\n        if (error) {\n            console.error('Supabase connection test failed:', error);\n            return {\n                success: false,\n                error: error.message\n            };\n        }\n        return {\n            success: true,\n            data\n        };\n    } catch (error) {\n        console.error('Supabase connection test error:', error);\n        return {\n            success: false,\n            error: 'Connection failed'\n        };\n    }\n};\n// Auth helper functions\nconst getCurrentUser = async ()=>{\n    try {\n        const { data: { user }, error } = await supabase.auth.getUser();\n        if (error) {\n            console.error('Error getting current user:', error);\n            return null;\n        }\n        if (!user) {\n            return null;\n        }\n        // Get user profile from profiles table\n        const { data: profile, error: profileError } = await supabase.from('profiles').select('*').eq('id', user.id).single();\n        if (profileError) {\n            console.error('Error getting user profile:', profileError);\n            // Check if user is admin based on email\n            const isAdmin = user.email === '<EMAIL>' || user.email?.includes('admin') || user.user_metadata?.role === 'admin';\n            // Return basic user info if profile fetch fails\n            return {\n                ...user,\n                full_name: user.user_metadata?.full_name || user.email?.split('@')[0] || 'User',\n                role: isAdmin ? 'admin' : user.user_metadata?.role || 'student',\n                avatar_url: null,\n                phone: null,\n                is_suspended: false,\n                suspension_reason: null,\n                status: 'active',\n                is_active: true\n            };\n        }\n        // Combine auth user with profile data\n        return {\n            ...user,\n            ...profile\n        };\n    } catch (error) {\n        console.error('Error getting current user:', error);\n        return null;\n    }\n};\nconst signOut = async ()=>{\n    try {\n        const { error } = await supabase.auth.signOut();\n        return {\n            error\n        };\n    } catch (error) {\n        console.error('Error signing out:', error);\n        return {\n            error: 'Sign out failed'\n        };\n    }\n};\n// Theme preference functions\nconst getUserThemePreference = async (userId)=>{\n    try {\n        const { data, error } = await supabase.from('theme_preferences').select('*').eq('user_id', userId).single();\n        if (error) {\n            // If table doesn't exist or no data, return default dark theme\n            console.log('Theme preferences not found, using default dark theme');\n            return {\n                id: '',\n                user_id: userId,\n                theme: 'dark',\n                auto_switch: false,\n                created_at: '',\n                updated_at: ''\n            };\n        }\n        return data;\n    } catch (error) {\n        console.log('Theme preferences error, using default dark theme:', error);\n        return {\n            id: '',\n            user_id: userId,\n            theme: 'dark',\n            auto_switch: false,\n            created_at: '',\n            updated_at: ''\n        };\n    }\n};\nconst saveUserThemePreference = async (userId, theme, autoSwitch = false)=>{\n    try {\n        const { error } = await supabase.from('theme_preferences').upsert({\n            user_id: userId,\n            theme,\n            auto_switch: autoSwitch,\n            updated_at: new Date().toISOString()\n        });\n        if (error) {\n            console.log('Theme preferences table not available, skipping save');\n            return true // Return true to not break the app\n            ;\n        }\n        return true;\n    } catch (error) {\n        console.log('Theme preferences save error, continuing without saving:', error);\n        return true // Return true to not break the app\n        ;\n    }\n};\n// Contact form functions\nconst submitContactForm = async (formData)=>{\n    const { error } = await supabase.from('contact_submissions').insert([\n        formData\n    ]);\n    if (error) {\n        console.error('Error submitting contact form:', error);\n        return false;\n    }\n    return true;\n};\n// System settings functions\nconst getSystemSetting = async (key)=>{\n    const { data, error } = await supabase.from('system_settings').select('setting_value').eq('setting_key', key).single();\n    if (error) {\n        console.error('Error fetching system setting:', error);\n        return null;\n    }\n    return data?.setting_value;\n};\nconst getPublicSystemSettings = async ()=>{\n    const { data, error } = await supabase.from('system_settings').select('setting_key, setting_value').eq('is_public', true);\n    if (error) {\n        console.error('Error fetching public system settings:', error);\n        return {};\n    }\n    const settings = {};\n    data?.forEach((setting)=>{\n        settings[setting.setting_key] = setting.setting_value;\n    });\n    return settings;\n};\nconst updateSystemSetting = async (key, value)=>{\n    const { error } = await supabase.from('system_settings').update({\n        setting_value: value,\n        updated_at: new Date().toISOString()\n    }).eq('setting_key', key);\n    if (error) {\n        console.error('Error updating system setting:', error);\n        return false;\n    }\n    return true;\n};\n// Chart annotation functions\nconst getChartAnnotations = async (chartId)=>{\n    const { data, error } = await supabase.from('chart_annotations').select('*').eq('chart_id', chartId).order('created_at', {\n        ascending: true\n    });\n    if (error) {\n        console.error('Error fetching chart annotations:', error);\n        return [];\n    }\n    return data || [];\n};\nconst saveChartAnnotation = async (annotation)=>{\n    const { error } = await supabase.from('chart_annotations').insert([\n        annotation\n    ]);\n    if (error) {\n        console.error('Error saving chart annotation:', error);\n        return false;\n    }\n    return true;\n};\nconst updateChartAnnotation = async (id, updates)=>{\n    const { error } = await supabase.from('chart_annotations').update({\n        ...updates,\n        updated_at: new Date().toISOString()\n    }).eq('id', id);\n    if (error) {\n        console.error('Error updating chart annotation:', error);\n        return false;\n    }\n    return true;\n};\nconst deleteChartAnnotation = async (id)=>{\n    const { error } = await supabase.from('chart_annotations').delete().eq('id', id);\n    if (error) {\n        console.error('Error deleting chart annotation:', error);\n        return false;\n    }\n    return true;\n};\n// Inventory functions\nconst getInventoryItems = async (cafeteriaId)=>{\n    const { data, error } = await supabase.from('inventory_items').select('*').eq('cafeteria_id', cafeteriaId).order('name', {\n        ascending: true\n    });\n    if (error) {\n        console.error('Error fetching inventory items:', error);\n        return [];\n    }\n    return data || [];\n};\nconst saveInventoryItem = async (item)=>{\n    const { error } = await supabase.from('inventory_items').insert([\n        item\n    ]);\n    if (error) {\n        console.error('Error saving inventory item:', error);\n        return false;\n    }\n    return true;\n};\nconst updateInventoryItem = async (id, updates)=>{\n    try {\n        console.log('🔥 INVENTORY UPDATE FUNCTION CALLED 🔥');\n        console.log('Updating inventory item:', {\n            id,\n            updates\n        });\n        // Check if user is authenticated\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        if (authError || !user) {\n            console.error('User not authenticated for inventory update:', authError);\n            return false;\n        }\n        console.log('Authenticated user for inventory update:', user.id);\n        const updateData = {\n            ...updates,\n            updated_at: new Date().toISOString()\n        };\n        // DETAILED DEBUGGING: Log everything about the status\n        console.log('=== INVENTORY UPDATE DEBUG ===');\n        console.log('Full updateData object:', JSON.stringify(updateData, null, 2));\n        console.log('updateData.status type:', typeof updateData.status);\n        console.log('updateData.status value:', updateData.status);\n        // Map status values to match database constraint exactly\n        if (updateData.status) {\n            console.log('Original status value:', updateData.status);\n            // Ensure we only use the exact values allowed by the constraint\n            const allowedStatuses = [\n                'in_stock',\n                'low_stock',\n                'out_of_stock',\n                'expired'\n            ];\n            if (!allowedStatuses.includes(updateData.status)) {\n                console.log('Invalid status value, mapping to valid one');\n                // Map common variations to valid values\n                const statusMapping = {\n                    'in-stock': 'in_stock',\n                    'low': 'low_stock',\n                    'out-of-stock': 'out_of_stock',\n                    'available': 'in_stock',\n                    'unavailable': 'out_of_stock'\n                };\n                updateData.status = statusMapping[updateData.status] || 'in_stock';\n            }\n            console.log('Final status value:', updateData.status);\n            console.log('Final status type:', typeof updateData.status);\n        }\n        console.log('Final update data being sent:', JSON.stringify(updateData, null, 2));\n        console.log('=== END DEBUG ===');\n        // Try to update the inventory item\n        // If there's an ambiguous column reference error, it's likely due to a missing inventory_alerts table\n        const { data, error } = await supabase.from('inventory_items').update(updateData).eq('id', id).select();\n        if (error) {\n            console.error('Error updating inventory item:', {\n                error,\n                message: error.message,\n                details: error.details,\n                hint: error.hint,\n                code: error.code\n            });\n            console.error('Error details JSON:', JSON.stringify(error, null, 2));\n            // Handle specific RLS policy error\n            if (error.code === '42501' && error.message?.includes('row-level security policy')) {\n                console.error('❌ RLS POLICY ERROR: The trigger cannot create alerts due to row-level security.');\n                console.error('💡 SOLUTION: We\\'ll handle alert creation in the application instead of the trigger.');\n            }\n            return false;\n        }\n        if (!data || data.length === 0) {\n            console.error('No inventory item found with ID or access denied:', id);\n            return false;\n        }\n        console.log('Inventory item updated successfully:', data);\n        // Handle alert creation manually since triggers have RLS issues\n        const updatedItem = data[0];\n        if (updatedItem.status === 'low_stock' || updatedItem.status === 'out_of_stock') {\n            console.log('Creating inventory alert for status:', updatedItem.status);\n            // Check if alert already exists\n            const { data: existingAlert } = await supabase.from('inventory_alerts').select('id').eq('inventory_item_id', updatedItem.id).eq('alert_type', updatedItem.status).eq('is_resolved', false).single();\n            if (!existingAlert) {\n                // Create new alert\n                const alertMessage = updatedItem.status === 'out_of_stock' ? `${updatedItem.name} is out of stock` : `${updatedItem.name} is running low (${updatedItem.quantity} ${updatedItem.unit} remaining)`;\n                const { error: alertError } = await supabase.from('inventory_alerts').insert({\n                    cafeteria_id: updatedItem.cafeteria_id,\n                    inventory_item_id: updatedItem.id,\n                    alert_type: updatedItem.status,\n                    message: alertMessage,\n                    is_resolved: false\n                });\n                if (alertError) {\n                    console.error('Error creating inventory alert:', alertError);\n                } else {\n                    console.log('✅ Inventory alert created successfully');\n                }\n            }\n        }\n        // Resolve alerts when status improves\n        if (updatedItem.status === 'in_stock') {\n            console.log('Resolving inventory alerts for improved status');\n            const { error: resolveError } = await supabase.from('inventory_alerts').update({\n                is_resolved: true,\n                resolved_at: new Date().toISOString()\n            }).eq('inventory_item_id', updatedItem.id).in('alert_type', [\n                'low_stock',\n                'out_of_stock'\n            ]).eq('is_resolved', false);\n            if (resolveError) {\n                console.error('Error resolving inventory alerts:', resolveError);\n            } else {\n                console.log('✅ Inventory alerts resolved successfully');\n            }\n        }\n        return true;\n    } catch (err) {\n        console.error('Unexpected error updating inventory item:', err);\n        return false;\n    }\n};\nconst deleteInventoryItem = async (id)=>{\n    const { error } = await supabase.from('inventory_items').delete().eq('id', id);\n    if (error) {\n        console.error('Error deleting inventory item:', error);\n        return false;\n    }\n    return true;\n};\n// Export log functions\nconst createExportLog = async (exportData)=>{\n    const { data, error } = await supabase.from('export_logs').insert([\n        exportData\n    ]).select('id').single();\n    if (error) {\n        console.error('Error creating export log:', error);\n        return null;\n    }\n    return data?.id || null;\n};\nconst updateExportLog = async (id, updates)=>{\n    const { error } = await supabase.from('export_logs').update(updates).eq('id', id);\n    if (error) {\n        console.error('Error updating export log:', error);\n        return false;\n    }\n    return true;\n};\nconst getUserExportLogs = async (userId)=>{\n    const { data, error } = await supabase.from('export_logs').select('*').eq('user_id', userId).order('created_at', {\n        ascending: false\n    });\n    if (error) {\n        console.error('Error fetching export logs:', error);\n        return [];\n    }\n    return data || [];\n};\n// Analytics data functions\nconst getAnalyticsData = async (cafeteriaId, metricType, startDate, endDate)=>{\n    const { data, error } = await supabase.from('analytics_data').select('date_recorded, metric_value').eq('cafeteria_id', cafeteriaId).eq('metric_type', metricType).gte('date_recorded', startDate).lte('date_recorded', endDate).order('date_recorded', {\n        ascending: true\n    });\n    if (error) {\n        console.error('Error fetching analytics data:', error);\n        return [];\n    }\n    return data?.map((item)=>({\n            date: item.date_recorded,\n            value: Number(item.metric_value)\n        })) || [];\n};\nconst insertAnalyticsData = async (cafeteriaId, metricType, value, date)=>{\n    const { error } = await supabase.from('analytics_data').insert([\n        {\n            cafeteria_id: cafeteriaId,\n            metric_type: metricType,\n            metric_value: value,\n            date_recorded: date || new Date().toISOString().split('T')[0]\n        }\n    ]);\n    if (error) {\n        console.error('Error inserting analytics data:', error);\n        return false;\n    }\n    return true;\n};\n// User analytics preferences functions\nconst getUserAnalyticsPreferences = async (userId)=>{\n    const { data, error } = await supabase.from('user_analytics_preferences').select('*').eq('user_id', userId).single();\n    if (error && error.code !== 'PGRST116') {\n        console.error('Error fetching user analytics preferences:', error);\n        return null;\n    }\n    return data;\n};\nconst saveUserAnalyticsPreferences = async (userId, preferences)=>{\n    const { error } = await supabase.from('user_analytics_preferences').upsert({\n        user_id: userId,\n        ...preferences,\n        updated_at: new Date().toISOString()\n    });\n    if (error) {\n        console.error('Error saving user analytics preferences:', error);\n        return false;\n    }\n    return true;\n};\n// Navigation tracking functions\nconst trackNavigation = async (userId, fromPage, toPage, timeSpent)=>{\n    const { error } = await supabase.from('navigation_logs').insert([\n        {\n            user_id: userId,\n            from_page: fromPage,\n            to_page: toPage,\n            time_spent_seconds: timeSpent\n        }\n    ]);\n    if (error) {\n        console.error('Error tracking navigation:', error);\n        return false;\n    }\n    return true;\n};\n// Notification functions\nconst createNotification = async (userId, title, message, type = 'info', relatedOrderId)=>{\n    const { error } = await supabase.from('notifications').insert([\n        {\n            user_id: userId,\n            title,\n            message,\n            type,\n            related_order_id: relatedOrderId,\n            is_read: false,\n            created_at: new Date().toISOString()\n        }\n    ]);\n    if (error) {\n        console.error('Error creating notification:', error);\n        return false;\n    }\n    return true;\n};\n// Create order notification for cafeteria owners\nconst createOrderNotification = async (cafeteriaId, orderNumber, customerName)=>{\n    try {\n        // Get cafeteria owner\n        const { data: cafeteria, error: cafeteriaError } = await supabase.from('cafeterias').select('owner_id, name').eq('id', cafeteriaId).single();\n        if (cafeteriaError || !cafeteria) {\n            console.error('Error fetching cafeteria:', cafeteriaError);\n            return false;\n        }\n        const title = 'New Order Received';\n        const message = `New order #${orderNumber} received${customerName ? ` from ${customerName}` : ''} at ${cafeteria.name}`;\n        return await createNotification(cafeteria.owner_id, title, message, 'order', orderNumber);\n    } catch (error) {\n        console.error('Error creating order notification:', error);\n        return false;\n    }\n};\n// Analytics events tracking\nconst trackAnalyticsEvent = async (userId, eventType, eventData, pageUrl)=>{\n    const { error } = await supabase.from('analytics_events').insert([\n        {\n            user_id: userId,\n            event_type: eventType,\n            event_data: eventData,\n            page_url: pageUrl,\n            user_agent: typeof navigator !== 'undefined' ? navigator.userAgent : null\n        }\n    ]);\n    if (error) {\n        console.error('Error tracking analytics event:', error);\n        return false;\n    }\n    return true;\n};\n// Chart configuration functions\nconst getChartConfigurations = async (userId)=>{\n    const { data, error } = await supabase.from('chart_configurations').select('*').eq('user_id', userId).order('created_at', {\n        ascending: false\n    });\n    if (error) {\n        console.error('Error fetching chart configurations:', error);\n        return [];\n    }\n    return data || [];\n};\nconst saveChartConfiguration = async (userId, chartType, title, configuration, isDefault = false)=>{\n    const { error } = await supabase.from('chart_configurations').insert([\n        {\n            user_id: userId,\n            chart_type: chartType,\n            chart_title: title,\n            configuration,\n            is_default: isDefault\n        }\n    ]);\n    if (error) {\n        console.error('Error saving chart configuration:', error);\n        return false;\n    }\n    return true;\n};\n// User activity logging\nconst logUserActivity = async (userId, activityType, description, entityType, entityId, metadata)=>{\n    const { error } = await supabase.from('user_activity_logs').insert([\n        {\n            user_id: userId,\n            activity_type: activityType,\n            activity_description: description,\n            entity_type: entityType,\n            entity_id: entityId,\n            metadata: metadata || {}\n        }\n    ]);\n    if (error) {\n        console.error('Error logging user activity:', error);\n        return false;\n    }\n    return true;\n};\nconst getCareers = async ()=>{\n    const { data, error } = await supabase.from('careers').select('*').eq('status', 'active').order('created_at', {\n        ascending: false\n    });\n    if (error) {\n        console.error('Error fetching careers:', error);\n        return [];\n    }\n    return data || [];\n};\nconst createCareer = async (career)=>{\n    const { error } = await supabase.from('careers').insert([\n        career\n    ]);\n    if (error) {\n        console.error('Error creating career:', error);\n        return false;\n    }\n    return true;\n};\nconst updateCareer = async (id, updates)=>{\n    const { error } = await supabase.from('careers').update({\n        ...updates,\n        updated_at: new Date().toISOString()\n    }).eq('id', id);\n    if (error) {\n        console.error('Error updating career:', error);\n        return false;\n    }\n    return true;\n};\nconst deleteCareer = async (id)=>{\n    const { error } = await supabase.from('careers').update({\n        status: 'inactive'\n    }).eq('id', id);\n    if (error) {\n        console.error('Error deleting career:', error);\n        return false;\n    }\n    return true;\n};\n// Menu Items functions\nconst getMenuItems = async (cafeteriaId)=>{\n    let query = supabase.from('menu_items').select('*').order('name', {\n        ascending: true\n    });\n    if (cafeteriaId) {\n        query = query.eq('cafeteria_id', cafeteriaId);\n    }\n    const { data, error } = await query;\n    if (error) {\n        console.error('Error fetching menu items:', error);\n        return [];\n    }\n    return data || [];\n};\nconst addMenuItem = async (menuItem)=>{\n    const { error } = await supabase.from('menu_items').insert([\n        menuItem\n    ]);\n    if (error) {\n        console.error('Error adding menu item:', error);\n        return false;\n    }\n    return true;\n};\nconst updateMenuItem = async (id, updates)=>{\n    const { error } = await supabase.from('menu_items').update({\n        ...updates,\n        updated_at: new Date().toISOString()\n    }).eq('id', id);\n    if (error) {\n        console.error('Error updating menu item:', error);\n        return false;\n    }\n    return true;\n};\nconst deleteMenuItem = async (id)=>{\n    const { error } = await supabase.from('menu_items').delete().eq('id', id);\n    if (error) {\n        console.error('Error deleting menu item:', error);\n        return false;\n    }\n    return true;\n};\n// Cafeterias functions\nconst getCafeterias = async ()=>{\n    const { data, error } = await supabase.from('cafeterias').select('*').order('name', {\n        ascending: true\n    });\n    if (error) {\n        console.error('Error fetching cafeterias:', error);\n        return [];\n    }\n    return data || [];\n};\n// Support Tickets functions\nconst submitSupportTicket = async (ticket)=>{\n    // Generate a unique ticket number\n    const ticketNumber = `TKT-${Date.now()}-${Math.random().toString(36).substr(2, 5).toUpperCase()}`;\n    // Get user role if user_type not provided\n    let userType = ticket.user_type;\n    if (!userType) {\n        const { data: profile } = await supabase.from('profiles').select('role').eq('id', ticket.user_id).single();\n        if (profile?.role === 'cafeteria_manager') {\n            userType = 'cafeteria';\n        } else if (profile?.role === 'admin') {\n            userType = 'admin';\n        } else {\n            userType = 'student';\n        }\n    }\n    const ticketData = {\n        ticket_number: ticketNumber,\n        user_id: ticket.user_id,\n        title: ticket.title,\n        description: ticket.description,\n        category: ticket.category || 'general_inquiry',\n        priority: ticket.priority,\n        status: ticket.status || 'open',\n        user_type: userType,\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString()\n    };\n    const { error } = await supabase.from('support_tickets').insert([\n        ticketData\n    ]);\n    if (error) {\n        console.error('Error submitting support ticket:', error);\n        return false;\n    }\n    return true;\n};\nconst fetchSupportTickets = async (userId)=>{\n    try {\n        // First try with the join\n        let query = supabase.from('support_tickets').select(`\n        *,\n        profiles!user_id(\n          full_name,\n          role,\n          phone\n        )\n      `).order('created_at', {\n            ascending: false\n        });\n        if (userId) {\n            query = query.eq('user_id', userId);\n        }\n        const { data, error } = await query;\n        if (error) {\n            console.error('Error fetching support tickets with profiles join:', error);\n            console.error('Error details:', {\n                message: error.message,\n                details: error.details,\n                hint: error.hint,\n                code: error.code\n            });\n            // Fallback: try without the join\n            console.log('Trying fallback query without profiles join...');\n            let fallbackQuery = supabase.from('support_tickets').select('*').order('created_at', {\n                ascending: false\n            });\n            if (userId) {\n                fallbackQuery = fallbackQuery.eq('user_id', userId);\n            }\n            const { data: fallbackData, error: fallbackError } = await fallbackQuery;\n            if (fallbackError) {\n                console.error('Fallback query also failed:', fallbackError);\n                return [];\n            }\n            console.log('Fallback query successful, returning data without profile info');\n            return fallbackData || [];\n        }\n        return data || [];\n    } catch (error) {\n        console.error('Unexpected error in fetchSupportTickets:', error);\n        return [];\n    }\n};\n// Admin function to fetch all tickets with user information\nconst fetchAllSupportTicketsForAdmin = async ()=>{\n    try {\n        // First get support tickets with profiles\n        const { data: tickets, error: ticketsError } = await supabase.from('support_tickets').select(`\n        *,\n        profiles(\n          full_name,\n          role,\n          phone\n        )\n      `).order('created_at', {\n            ascending: false\n        });\n        if (ticketsError) {\n            console.error('Error fetching support tickets:', ticketsError);\n            return [];\n        }\n        // Then get user emails from auth.users\n        const userIds = tickets?.map((ticket)=>ticket.user_id).filter(Boolean) || [];\n        if (userIds.length === 0) {\n            return tickets || [];\n        }\n        const { data: authUsers, error: authError } = await supabase.auth.admin.listUsers();\n        if (authError) {\n            console.error('Error fetching auth users:', authError);\n            return tickets || [];\n        }\n        // Combine the data\n        const ticketsWithEmails = tickets?.map((ticket)=>({\n                ...ticket,\n                user_email: authUsers.users.find((user)=>user.id === ticket.user_id)?.email || 'No email'\n            })) || [];\n        return ticketsWithEmails;\n    } catch (error) {\n        console.error('Error in fetchAllSupportTicketsForAdmin:', error);\n        return [];\n    }\n};\n// Fetch tickets by user type (for admin dashboard)\nconst fetchSupportTicketsByType = async (userType)=>{\n    const { data, error } = await supabase.from('support_tickets').select(`\n      *,\n      profiles(\n        full_name,\n        role,\n        phone\n      )\n    `).eq('user_type', userType).order('created_at', {\n        ascending: false\n    });\n    if (error) {\n        console.error(`Error fetching ${userType} support tickets:`, error);\n        return [];\n    }\n    return data || [];\n};\n// Legacy functions for backward compatibility\nconst submitStudentMessage = async (message)=>{\n    return await submitSupportTicket({\n        user_id: message.user_id,\n        title: message.subject,\n        description: message.content,\n        category: message.user_type,\n        priority: message.priority,\n        status: message.status\n    });\n};\nconst fetchStudentMessages = async ()=>{\n    try {\n        console.log('Fetching student messages...');\n        const tickets = await fetchSupportTickets();\n        console.log('Successfully fetched tickets:', tickets.length);\n        return tickets;\n    } catch (error) {\n        console.error('Error in fetchStudentMessages:', error);\n        return [];\n    }\n};\n// Chat System Functions\nconst createChatConversation = async (conversation)=>{\n    // Get user role if user_type not provided\n    let userType = conversation.user_type;\n    if (!userType) {\n        const { data: profile } = await supabase.from('profiles').select('role').eq('id', conversation.user_id).single();\n        if (profile?.role === 'cafeteria_manager') {\n            userType = 'cafeteria';\n        } else if (profile?.role === 'admin') {\n            userType = 'admin';\n        } else {\n            userType = 'student';\n        }\n    }\n    const { data, error } = await supabase.from('chat_conversations').insert([\n        {\n            user_id: conversation.user_id,\n            subject: conversation.subject,\n            category: conversation.category || 'general_inquiry',\n            priority: conversation.priority || 'medium',\n            user_type: userType,\n            order_id: conversation.order_id || null,\n            ticket_id: conversation.ticket_id || null,\n            status: 'open'\n        }\n    ]).select('id').single();\n    if (error) {\n        console.error('Error creating chat conversation:', error);\n        return null;\n    }\n    return data?.id || null;\n};\nconst sendChatMessage = async (message)=>{\n    const { error } = await supabase.from('chat_messages').insert([\n        {\n            conversation_id: message.conversation_id,\n            sender_id: message.sender_id,\n            content: message.content,\n            message_type: message.message_type || 'text',\n            file_url: message.file_url || null,\n            file_name: message.file_name || null,\n            file_size: message.file_size || null,\n            is_read: false\n        }\n    ]);\n    if (error) {\n        console.error('Error sending chat message:', error);\n        return false;\n    }\n    // Update conversation updated_at\n    await supabase.from('chat_conversations').update({\n        updated_at: new Date().toISOString()\n    }).eq('id', message.conversation_id);\n    return true;\n};\nconst getChatConversations = async (userId, userType)=>{\n    let query = supabase.from('chat_conversations').select(`\n      *,\n      profiles!chat_conversations_user_id_fkey(\n        full_name,\n        email,\n        role\n      ),\n      support_agent:profiles!chat_conversations_support_agent_id_fkey(\n        full_name,\n        email\n      )\n    `).order('updated_at', {\n        ascending: false\n    });\n    if (userId) {\n        query = query.eq('user_id', userId);\n    }\n    if (userType) {\n        query = query.eq('user_type', userType);\n    }\n    const { data, error } = await query;\n    if (error) {\n        console.error('Error fetching chat conversations:', error);\n        return [];\n    }\n    return data || [];\n};\nconst getChatMessages = async (conversationId)=>{\n    const { data, error } = await supabase.from('chat_messages').select(`\n      *,\n      sender:profiles!chat_messages_sender_id_fkey(\n        full_name,\n        email,\n        role\n      )\n    `).eq('conversation_id', conversationId).order('created_at', {\n        ascending: true\n    });\n    if (error) {\n        console.error('Error fetching chat messages:', error);\n        return [];\n    }\n    return data || [];\n};\nconst markMessagesAsRead = async (conversationId, userId)=>{\n    const { error } = await supabase.from('chat_messages').update({\n        is_read: true\n    }).eq('conversation_id', conversationId).neq('sender_id', userId);\n    if (error) {\n        console.error('Error marking messages as read:', error);\n        return false;\n    }\n    return true;\n};\nconst assignChatToAgent = async (conversationId, agentId)=>{\n    const { error } = await supabase.from('chat_conversations').update({\n        support_agent_id: agentId,\n        status: 'in_progress',\n        updated_at: new Date().toISOString()\n    }).eq('id', conversationId);\n    if (error) {\n        console.error('Error assigning chat to agent:', error);\n        return false;\n    }\n    return true;\n};\nconst closeChatConversation = async (conversationId, rating, feedback)=>{\n    const updateData = {\n        status: 'closed',\n        closed_at: new Date().toISOString(),\n        updated_at: new Date().toISOString()\n    };\n    if (rating) updateData.rating = rating;\n    if (feedback) updateData.feedback = feedback;\n    const { error } = await supabase.from('chat_conversations').update(updateData).eq('id', conversationId);\n    if (error) {\n        console.error('Error closing chat conversation:', error);\n        return false;\n    }\n    return true;\n};\n// Convert support ticket to chat conversation\nconst convertTicketToChat = async (ticketId)=>{\n    // Get ticket details\n    const { data: ticket, error: ticketError } = await supabase.from('support_tickets').select('*').eq('id', ticketId).single();\n    if (ticketError || !ticket) {\n        console.error('Error fetching ticket for conversion:', ticketError);\n        return null;\n    }\n    // Create chat conversation linked to ticket\n    const conversationId = await createChatConversation({\n        user_id: ticket.user_id,\n        subject: ticket.title,\n        category: ticket.category,\n        priority: ticket.priority,\n        user_type: ticket.user_type,\n        order_id: ticket.order_id,\n        ticket_id: ticketId\n    });\n    if (conversationId) {\n        // Add initial message with ticket description\n        await sendChatMessage({\n            conversation_id: conversationId,\n            sender_id: ticket.user_id,\n            content: `Original ticket: ${ticket.description}`,\n            message_type: 'text'\n        });\n        // Update ticket status to indicate it has a chat\n        await supabase.from('support_tickets').update({\n            status: 'in_progress',\n            updated_at: new Date().toISOString()\n        }).eq('id', ticketId);\n    }\n    return conversationId;\n};\n// Get chat conversation for a ticket\nconst getChatForTicket = async (ticketId)=>{\n    const { data, error } = await supabase.from('chat_conversations').select(`\n      *,\n      profiles!chat_conversations_user_id_fkey(\n        full_name,\n        email,\n        role\n      ),\n      support_agent:profiles!chat_conversations_support_agent_id_fkey(\n        full_name,\n        email\n      )\n    `).eq('ticket_id', ticketId).single();\n    if (error) {\n        console.error('Error fetching chat for ticket:', error);\n        return null;\n    }\n    return data;\n};\n// Orders functions\nconst getOrders = async (cafeteriaId)=>{\n    let query = supabase.from('orders').select('*').order('created_at', {\n        ascending: false\n    });\n    if (cafeteriaId) {\n        query = query.eq('cafeteria_id', cafeteriaId);\n    }\n    const { data, error } = await query;\n    if (error) {\n        console.error('Error fetching orders:', error);\n        return [];\n    }\n    return data || [];\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/supabase.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdashboard%2Froute&page=%2Fapi%2Fdashboard%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Froute.ts&appDir=C%3A%5CUsers%5Clkmd2%5CDesktop%5Cweb%20and%20mob%5Cgradproject%20v5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Clkmd2%5CDesktop%5Cweb%20and%20mob%5Cgradproject%20v5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdashboard%2Froute&page=%2Fapi%2Fdashboard%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Froute.ts&appDir=C%3A%5CUsers%5Clkmd2%5CDesktop%5Cweb%20and%20mob%5Cgradproject%20v5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Clkmd2%5CDesktop%5Cweb%20and%20mob%5Cgradproject%20v5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_lkmd2_Desktop_web_and_mob_gradproject_v5_app_api_dashboard_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/dashboard/route.ts */ \"(rsc)/./app/api/dashboard/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/dashboard/route\",\n        pathname: \"/api/dashboard\",\n        filename: \"route\",\n        bundlePath: \"app/api/dashboard/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\web and mob\\\\gradproject v5\\\\app\\\\api\\\\dashboard\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_lkmd2_Desktop_web_and_mob_gradproject_v5_app_api_dashboard_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhcGklMkZkYXNoYm9hcmQlMkZyb3V0ZSZwYWdlPSUyRmFwaSUyRmRhc2hib2FyZCUyRnJvdXRlJmFwcFBhdGhzPSZwYWdlUGF0aD1wcml2YXRlLW5leHQtYXBwLWRpciUyRmFwaSUyRmRhc2hib2FyZCUyRnJvdXRlLnRzJmFwcERpcj1DJTNBJTVDVXNlcnMlNUNsa21kMiU1Q0Rlc2t0b3AlNUN3ZWIlMjBhbmQlMjBtb2IlNUNncmFkcHJvamVjdCUyMHY1JTVDYXBwJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMmcm9vdERpcj1DJTNBJTVDVXNlcnMlNUNsa21kMiU1Q0Rlc2t0b3AlNUN3ZWIlMjBhbmQlMjBtb2IlNUNncmFkcHJvamVjdCUyMHY1JmlzRGV2PXRydWUmdHNjb25maWdQYXRoPXRzY29uZmlnLmpzb24mYmFzZVBhdGg9JmFzc2V0UHJlZml4PSZuZXh0Q29uZmlnT3V0cHV0PSZwcmVmZXJyZWRSZWdpb249Jm1pZGRsZXdhcmVDb25maWc9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQUErRjtBQUN2QztBQUNxQjtBQUNxQztBQUNsSDtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IseUdBQW1CO0FBQzNDO0FBQ0EsY0FBYyxrRUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsWUFBWTtBQUNaLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQSxRQUFRLHNEQUFzRDtBQUM5RDtBQUNBLFdBQVcsNEVBQVc7QUFDdEI7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUMwRjs7QUFFMUYiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBBcHBSb3V0ZVJvdXRlTW9kdWxlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUtbW9kdWxlcy9hcHAtcm91dGUvbW9kdWxlLmNvbXBpbGVkXCI7XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1raW5kXCI7XG5pbXBvcnQgeyBwYXRjaEZldGNoIGFzIF9wYXRjaEZldGNoIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvbGliL3BhdGNoLWZldGNoXCI7XG5pbXBvcnQgKiBhcyB1c2VybGFuZCBmcm9tIFwiQzpcXFxcVXNlcnNcXFxcbGttZDJcXFxcRGVza3RvcFxcXFx3ZWIgYW5kIG1vYlxcXFxncmFkcHJvamVjdCB2NVxcXFxhcHBcXFxcYXBpXFxcXGRhc2hib2FyZFxcXFxyb3V0ZS50c1wiO1xuLy8gV2UgaW5qZWN0IHRoZSBuZXh0Q29uZmlnT3V0cHV0IGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCBuZXh0Q29uZmlnT3V0cHV0ID0gXCJcIlxuY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgQXBwUm91dGVSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuQVBQX1JPVVRFLFxuICAgICAgICBwYWdlOiBcIi9hcGkvZGFzaGJvYXJkL3JvdXRlXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9hcGkvZGFzaGJvYXJkXCIsXG4gICAgICAgIGZpbGVuYW1lOiBcInJvdXRlXCIsXG4gICAgICAgIGJ1bmRsZVBhdGg6IFwiYXBwL2FwaS9kYXNoYm9hcmQvcm91dGVcIlxuICAgIH0sXG4gICAgcmVzb2x2ZWRQYWdlUGF0aDogXCJDOlxcXFxVc2Vyc1xcXFxsa21kMlxcXFxEZXNrdG9wXFxcXHdlYiBhbmQgbW9iXFxcXGdyYWRwcm9qZWN0IHY1XFxcXGFwcFxcXFxhcGlcXFxcZGFzaGJvYXJkXFxcXHJvdXRlLnRzXCIsXG4gICAgbmV4dENvbmZpZ091dHB1dCxcbiAgICB1c2VybGFuZFxufSk7XG4vLyBQdWxsIG91dCB0aGUgZXhwb3J0cyB0aGF0IHdlIG5lZWQgdG8gZXhwb3NlIGZyb20gdGhlIG1vZHVsZS4gVGhpcyBzaG91bGRcbi8vIGJlIGVsaW1pbmF0ZWQgd2hlbiB3ZSd2ZSBtb3ZlZCB0aGUgb3RoZXIgcm91dGVzIHRvIHRoZSBuZXcgZm9ybWF0LiBUaGVzZVxuLy8gYXJlIHVzZWQgdG8gaG9vayBpbnRvIHRoZSByb3V0ZS5cbmNvbnN0IHsgd29ya0FzeW5jU3RvcmFnZSwgd29ya1VuaXRBc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzIH0gPSByb3V0ZU1vZHVsZTtcbmZ1bmN0aW9uIHBhdGNoRmV0Y2goKSB7XG4gICAgcmV0dXJuIF9wYXRjaEZldGNoKHtcbiAgICAgICAgd29ya0FzeW5jU3RvcmFnZSxcbiAgICAgICAgd29ya1VuaXRBc3luY1N0b3JhZ2VcbiAgICB9KTtcbn1cbmV4cG9ydCB7IHJvdXRlTW9kdWxlLCB3b3JrQXN5bmNTdG9yYWdlLCB3b3JrVW5pdEFzeW5jU3RvcmFnZSwgc2VydmVySG9va3MsIHBhdGNoRmV0Y2gsICB9O1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcHAtcm91dGUuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdashboard%2Froute&page=%2Fapi%2Fdashboard%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Froute.ts&appDir=C%3A%5CUsers%5Clkmd2%5CDesktop%5Cweb%20and%20mob%5Cgradproject%20v5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Clkmd2%5CDesktop%5Cweb%20and%20mob%5Cgradproject%20v5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdashboard%2Froute&page=%2Fapi%2Fdashboard%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Froute.ts&appDir=C%3A%5CUsers%5Clkmd2%5CDesktop%5Cweb%20and%20mob%5Cgradproject%20v5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Clkmd2%5CDesktop%5Cweb%20and%20mob%5Cgradproject%20v5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();