/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/support-tickets/route";
exports.ids = ["app/api/support-tickets/route"];
exports.modules = {

/***/ "(rsc)/./app/api/support-tickets/route.ts":
/*!******************************************!*\
  !*** ./app/api/support-tickets/route.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   PATCH: () => (/* binding */ PATCH),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase */ \"(rsc)/./lib/supabase.ts\");\n\n\nasync function GET(request) {\n    try {\n        console.log('🔍 Support tickets API called');\n        console.log('Environment check:', {\n            hasSupabaseUrl: !!\"https://lqtnaxvqkoynaziiinqh.supabase.co\",\n            hasServiceKey: !!process.env.SUPABASE_SERVICE_ROLE_KEY,\n            serviceKeyLength: process.env.SUPABASE_SERVICE_ROLE_KEY?.length || 0\n        });\n        const supabaseAdmin = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.createSupabaseAdmin)();\n        const { searchParams } = new URL(request.url);\n        const status = searchParams.get('status') // 'open', 'in_progress', 'resolved', 'closed', or 'all'\n        ;\n        const userType = searchParams.get('userType') // 'student', 'cafeteria', 'admin', or 'all'\n        ;\n        const userId = searchParams.get('userId') // specific user ID\n        ;\n        const limit = parseInt(searchParams.get('limit') || '50');\n        console.log('📋 Query parameters:', {\n            status,\n            userType,\n            userId,\n            limit\n        });\n        // Get support tickets\n        let ticketsQuery = supabaseAdmin.from('support_tickets').select('*').order('created_at', {\n            ascending: false\n        }).limit(limit);\n        // Apply filters\n        if (status && status !== 'all') {\n            ticketsQuery = ticketsQuery.eq('status', status);\n        }\n        if (userType && userType !== 'all') {\n            ticketsQuery = ticketsQuery.eq('user_type', userType);\n        }\n        if (userId) {\n            ticketsQuery = ticketsQuery.eq('user_id', userId);\n        }\n        console.log('🔍 Executing tickets query...');\n        const { data: tickets, error: ticketsError } = await ticketsQuery;\n        if (ticketsError) {\n            console.error('❌ Error fetching support tickets:', ticketsError);\n            console.error('Error details:', {\n                message: ticketsError.message,\n                details: ticketsError.details,\n                hint: ticketsError.hint,\n                code: ticketsError.code\n            });\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to fetch support tickets',\n                details: ticketsError.message\n            }, {\n                status: 500\n            });\n        }\n        console.log('✅ Tickets fetched successfully:', tickets?.length || 0);\n        if (!tickets || tickets.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                tickets: [],\n                total: 0,\n                counts: {\n                    open: 0,\n                    in_progress: 0,\n                    resolved: 0,\n                    closed: 0\n                }\n            });\n        }\n        // Get user IDs for batch fetching\n        const userIds = [\n            ...new Set(tickets.map((ticket)=>ticket.user_id).filter(Boolean))\n        ];\n        // Fetch profiles\n        const { data: profiles } = await supabaseAdmin.from('profiles').select('id, full_name, role, phone').in('id', userIds);\n        // Fetch auth users for emails\n        const { data: authUsers } = await supabaseAdmin.auth.admin.listUsers();\n        const authUsersMap = new Map(authUsers.users.map((user)=>[\n                user.id,\n                user\n            ]));\n        // Create lookup map for profiles\n        const profilesMap = new Map(profiles?.map((profile)=>[\n                profile.id,\n                profile\n            ]) || []);\n        // Get ticket IDs for fetching conversations and messages\n        const ticketIds = tickets.map((ticket)=>ticket.id);\n        // Fetch conversations for these tickets\n        const { data: conversations } = await supabaseAdmin.from('chat_conversations').select('id, ticket_id').in('ticket_id', ticketIds);\n        // Create conversation lookup map\n        const conversationMap = new Map(conversations?.map((conv)=>[\n                conv.ticket_id,\n                conv.id\n            ]) || []);\n        // Fetch all messages for these conversations\n        const conversationIds = conversations?.map((conv)=>conv.id) || [];\n        const { data: allMessages } = await supabaseAdmin.from('chat_messages').select('*').in('conversation_id', conversationIds).order('created_at', {\n            ascending: true\n        });\n        // Group messages by conversation ID\n        const messagesByConversation = new Map();\n        allMessages?.forEach((message)=>{\n            const convId = message.conversation_id;\n            if (!messagesByConversation.has(convId)) {\n                messagesByConversation.set(convId, []);\n            }\n            messagesByConversation.get(convId).push(message);\n        });\n        // Process tickets\n        const processedTickets = tickets.map((ticket)=>{\n            const profile = profilesMap.get(ticket.user_id);\n            const authUser = authUsersMap.get(ticket.user_id);\n            // Format time\n            const ticketTime = new Date(ticket.created_at);\n            const now = new Date();\n            const diffInMinutes = Math.floor((now.getTime() - ticketTime.getTime()) / (1000 * 60));\n            let timeString = \"\";\n            if (diffInMinutes < 60) {\n                timeString = `${diffInMinutes} mins ago`;\n            } else if (diffInMinutes < 1440) {\n                timeString = `${Math.floor(diffInMinutes / 60)} hours ago`;\n            } else {\n                timeString = ticketTime.toLocaleDateString();\n            }\n            // Determine status color and priority color\n            let statusColor = 'gray';\n            let priorityColor = 'gray';\n            switch(ticket.status?.toLowerCase()){\n                case 'open':\n                    statusColor = 'red';\n                    break;\n                case 'in_progress':\n                    statusColor = 'yellow';\n                    break;\n                case 'resolved':\n                    statusColor = 'green';\n                    break;\n                case 'closed':\n                    statusColor = 'gray';\n                    break;\n            }\n            switch(ticket.priority?.toLowerCase()){\n                case 'low':\n                    priorityColor = 'green';\n                    break;\n                case 'medium':\n                    priorityColor = 'yellow';\n                    break;\n                case 'high':\n                    priorityColor = 'orange';\n                    break;\n                case 'urgent':\n                    priorityColor = 'red';\n                    break;\n            }\n            // Format user type for display\n            let userTypeDisplay = ticket.user_type || 'Unknown';\n            switch(ticket.user_type?.toLowerCase()){\n                case 'student':\n                    userTypeDisplay = 'Student';\n                    break;\n                case 'cafeteria':\n                    userTypeDisplay = 'Cafeteria';\n                    break;\n                case 'admin':\n                    userTypeDisplay = 'Admin';\n                    break;\n            }\n            // Get conversation and messages for this ticket\n            const conversationId = conversationMap.get(ticket.id);\n            const messages = conversationId ? messagesByConversation.get(conversationId) || [] : [];\n            // Convert chat messages to responses format (excluding the initial message which is the description)\n            const responses = messages.filter((msg)=>msg.content !== ticket.description) // Exclude the initial ticket description\n            .map((msg)=>{\n                // Check if sender is admin by looking up their profile\n                const senderProfile = profilesMap.get(msg.sender_id);\n                const isAdmin = senderProfile?.role === 'admin' || msg.sender_id === '156df217-77cc-499a-b0df-d45d0770215c' || // UniEats Administrator\n                msg.sender_id === '634764ad-bb60-464b-bc08-a54a634134cf' // Test Admin\n                ;\n                return {\n                    id: msg.id,\n                    content: msg.content,\n                    timestamp: msg.created_at,\n                    isAdmin: isAdmin,\n                    adminName: isAdmin ? senderProfile?.full_name || 'Admin Support' : undefined\n                };\n            });\n            return {\n                id: ticket.id,\n                ticketNumber: ticket.ticket_number,\n                title: ticket.title,\n                description: ticket.description,\n                category: ticket.category || 'general_inquiry',\n                status: {\n                    raw: ticket.status,\n                    label: ticket.status?.charAt(0).toUpperCase() + ticket.status?.slice(1).replace('_', ' ') || 'Unknown',\n                    color: statusColor\n                },\n                priority: {\n                    raw: ticket.priority,\n                    label: ticket.priority?.charAt(0).toUpperCase() + ticket.priority?.slice(1) || 'Medium',\n                    color: priorityColor\n                },\n                user: {\n                    id: ticket.user_id,\n                    name: profile?.full_name || authUser?.email?.split('@')[0] || 'Unknown User',\n                    email: authUser?.email || 'No email',\n                    phone: profile?.phone || 'No phone',\n                    role: profile?.role || 'unknown',\n                    type: userTypeDisplay,\n                    image: \"/diverse-group-city.png\"\n                },\n                time: timeString,\n                createdAt: ticket.created_at,\n                updatedAt: ticket.updated_at,\n                isUnread: ticket.status === 'open',\n                assignedTo: ticket.assigned_to,\n                resolutionNotes: ticket.resolution,\n                lastResponseAt: messages.length > 0 ? messages[messages.length - 1].created_at : ticket.updated_at,\n                responseCount: responses.length,\n                responses: responses\n            };\n        });\n        // Calculate counts for all statuses\n        const counts = {\n            open: processedTickets.filter((ticket)=>ticket.status.raw === 'open').length,\n            in_progress: processedTickets.filter((ticket)=>ticket.status.raw === 'in_progress').length,\n            resolved: processedTickets.filter((ticket)=>ticket.status.raw === 'resolved').length,\n            closed: processedTickets.filter((ticket)=>ticket.status.raw === 'closed').length\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            tickets: processedTickets,\n            total: processedTickets.length,\n            counts,\n            filters: {\n                status: status || 'all',\n                userType: userType || 'all',\n                userId: userId || null\n            }\n        });\n    } catch (error) {\n        console.error('Error in support tickets API:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(request) {\n    try {\n        const supabaseAdmin = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.createSupabaseAdmin)();\n        const body = await request.json();\n        const { user_id, title, description, category, priority, user_type } = body;\n        if (!user_id || !title || !description) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'User ID, title, and description are required'\n            }, {\n                status: 400\n            });\n        }\n        // Generate a unique ticket number\n        const ticketNumber = `TKT-${Date.now()}-${Math.random().toString(36).substr(2, 5).toUpperCase()}`;\n        // Get user role if user_type not provided\n        let finalUserType = user_type;\n        if (!finalUserType) {\n            const { data: profile } = await supabaseAdmin.from('profiles').select('role').eq('id', user_id).single();\n            if (profile?.role === 'cafeteria_manager') {\n                finalUserType = 'cafeteria';\n            } else if (profile?.role === 'admin') {\n                finalUserType = 'admin';\n            } else {\n                finalUserType = 'student';\n            }\n        }\n        const ticketData = {\n            ticket_number: ticketNumber,\n            user_id,\n            title,\n            description,\n            category: category || 'general_inquiry',\n            priority: priority || 'medium',\n            status: 'open',\n            user_type: finalUserType,\n            created_at: new Date().toISOString(),\n            updated_at: new Date().toISOString()\n        };\n        const { data: ticket, error } = await supabaseAdmin.from('support_tickets').insert([\n            ticketData\n        ]).select().single();\n        if (error) {\n            console.error('Error creating support ticket:', error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to create support ticket'\n            }, {\n                status: 500\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            ticket,\n            message: 'Support ticket created successfully'\n        });\n    } catch (error) {\n        console.error('Error creating support ticket:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function PATCH(request) {\n    try {\n        const supabaseAdmin = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.createSupabaseAdmin)();\n        const body = await request.json();\n        const { ticketId, updates } = body;\n        if (!ticketId || !updates) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Ticket ID and updates are required'\n            }, {\n                status: 400\n            });\n        }\n        // Map common field names to actual database columns\n        const mappedUpdates = {\n            ...updates\n        };\n        // Handle field name mappings\n        if (updates.resolution_notes) {\n            mappedUpdates.resolution = updates.resolution_notes;\n            delete mappedUpdates.resolution_notes;\n        }\n        if (updates.is_read !== undefined) {\n            // is_read doesn't exist in support_tickets, we'll use status instead\n            delete mappedUpdates.is_read;\n        }\n        if (updates.response_count !== undefined) {\n            // response_count doesn't exist, remove it\n            delete mappedUpdates.response_count;\n        }\n        if (updates.last_response_at !== undefined) {\n            // last_response_at doesn't exist, we'll use updated_at\n            delete mappedUpdates.last_response_at;\n        }\n        const { error } = await supabaseAdmin.from('support_tickets').update({\n            ...mappedUpdates,\n            updated_at: new Date().toISOString()\n        }).eq('id', ticketId);\n        if (error) {\n            console.error('Error updating support ticket:', error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to update support ticket'\n            }, {\n                status: 500\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'Support ticket updated successfully'\n        });\n    } catch (error) {\n        console.error('Error updating support ticket:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/support-tickets/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/supabase.ts":
/*!*************************!*\
  !*** ./lib/supabase.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addMenuItem: () => (/* binding */ addMenuItem),\n/* harmony export */   assignChatToAgent: () => (/* binding */ assignChatToAgent),\n/* harmony export */   closeChatConversation: () => (/* binding */ closeChatConversation),\n/* harmony export */   convertTicketToChat: () => (/* binding */ convertTicketToChat),\n/* harmony export */   createCareer: () => (/* binding */ createCareer),\n/* harmony export */   createChatConversation: () => (/* binding */ createChatConversation),\n/* harmony export */   createExportLog: () => (/* binding */ createExportLog),\n/* harmony export */   createNotification: () => (/* binding */ createNotification),\n/* harmony export */   createOrderNotification: () => (/* binding */ createOrderNotification),\n/* harmony export */   createSupabaseAdmin: () => (/* binding */ createSupabaseAdmin),\n/* harmony export */   deleteCareer: () => (/* binding */ deleteCareer),\n/* harmony export */   deleteChartAnnotation: () => (/* binding */ deleteChartAnnotation),\n/* harmony export */   deleteInventoryItem: () => (/* binding */ deleteInventoryItem),\n/* harmony export */   deleteMenuItem: () => (/* binding */ deleteMenuItem),\n/* harmony export */   fetchAllSupportTicketsForAdmin: () => (/* binding */ fetchAllSupportTicketsForAdmin),\n/* harmony export */   fetchStudentMessages: () => (/* binding */ fetchStudentMessages),\n/* harmony export */   fetchSupportTickets: () => (/* binding */ fetchSupportTickets),\n/* harmony export */   fetchSupportTicketsByType: () => (/* binding */ fetchSupportTicketsByType),\n/* harmony export */   getAnalyticsData: () => (/* binding */ getAnalyticsData),\n/* harmony export */   getCafeterias: () => (/* binding */ getCafeterias),\n/* harmony export */   getCareers: () => (/* binding */ getCareers),\n/* harmony export */   getChartAnnotations: () => (/* binding */ getChartAnnotations),\n/* harmony export */   getChartConfigurations: () => (/* binding */ getChartConfigurations),\n/* harmony export */   getChatConversations: () => (/* binding */ getChatConversations),\n/* harmony export */   getChatForTicket: () => (/* binding */ getChatForTicket),\n/* harmony export */   getChatMessages: () => (/* binding */ getChatMessages),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   getInventoryItems: () => (/* binding */ getInventoryItems),\n/* harmony export */   getMenuItems: () => (/* binding */ getMenuItems),\n/* harmony export */   getOrders: () => (/* binding */ getOrders),\n/* harmony export */   getPublicSystemSettings: () => (/* binding */ getPublicSystemSettings),\n/* harmony export */   getSystemSetting: () => (/* binding */ getSystemSetting),\n/* harmony export */   getUserAnalyticsPreferences: () => (/* binding */ getUserAnalyticsPreferences),\n/* harmony export */   getUserExportLogs: () => (/* binding */ getUserExportLogs),\n/* harmony export */   getUserThemePreference: () => (/* binding */ getUserThemePreference),\n/* harmony export */   insertAnalyticsData: () => (/* binding */ insertAnalyticsData),\n/* harmony export */   logUserActivity: () => (/* binding */ logUserActivity),\n/* harmony export */   markMessagesAsRead: () => (/* binding */ markMessagesAsRead),\n/* harmony export */   saveChartAnnotation: () => (/* binding */ saveChartAnnotation),\n/* harmony export */   saveChartConfiguration: () => (/* binding */ saveChartConfiguration),\n/* harmony export */   saveInventoryItem: () => (/* binding */ saveInventoryItem),\n/* harmony export */   saveUserAnalyticsPreferences: () => (/* binding */ saveUserAnalyticsPreferences),\n/* harmony export */   saveUserThemePreference: () => (/* binding */ saveUserThemePreference),\n/* harmony export */   sendChatMessage: () => (/* binding */ sendChatMessage),\n/* harmony export */   signOut: () => (/* binding */ signOut),\n/* harmony export */   submitContactForm: () => (/* binding */ submitContactForm),\n/* harmony export */   submitStudentMessage: () => (/* binding */ submitStudentMessage),\n/* harmony export */   submitSupportTicket: () => (/* binding */ submitSupportTicket),\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   testSupabaseConnection: () => (/* binding */ testSupabaseConnection),\n/* harmony export */   trackAnalyticsEvent: () => (/* binding */ trackAnalyticsEvent),\n/* harmony export */   trackNavigation: () => (/* binding */ trackNavigation),\n/* harmony export */   updateCareer: () => (/* binding */ updateCareer),\n/* harmony export */   updateChartAnnotation: () => (/* binding */ updateChartAnnotation),\n/* harmony export */   updateExportLog: () => (/* binding */ updateExportLog),\n/* harmony export */   updateInventoryItem: () => (/* binding */ updateInventoryItem),\n/* harmony export */   updateMenuItem: () => (/* binding */ updateMenuItem),\n/* harmony export */   updateSystemSetting: () => (/* binding */ updateSystemSetting)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\nconst supabaseUrl = \"https://lqtnaxvqkoynaziiinqh.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImxxdG5heHZxa295bmF6aWlpbnFoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc1ODUzMjcsImV4cCI6MjA2MzE2MTMyN30.MEMp-4fuLCMKaW-E_g56vsYFNKqzrftjhYfD_w1u0PA\";\nif (!supabaseUrl || !supabaseAnonKey) {\n    throw new Error('Missing Supabase environment variables');\n}\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey, {\n    auth: {\n        autoRefreshToken: true,\n        persistSession: true,\n        detectSessionInUrl: true\n    }\n});\n// Service role client for admin operations (bypasses RLS) - SERVER SIDE ONLY\nconst createSupabaseAdmin = ()=>{\n    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n    if (!supabaseServiceKey) {\n        throw new Error('SUPABASE_SERVICE_ROLE_KEY is required for admin operations');\n    }\n    return (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseServiceKey, {\n        auth: {\n            autoRefreshToken: false,\n            persistSession: false\n        }\n    });\n};\n// Connection test function\nconst testSupabaseConnection = async ()=>{\n    try {\n        const { data, error } = await supabase.from('cafeterias').select('id, name').limit(1);\n        if (error) {\n            console.error('Supabase connection test failed:', error);\n            return {\n                success: false,\n                error: error.message\n            };\n        }\n        return {\n            success: true,\n            data\n        };\n    } catch (error) {\n        console.error('Supabase connection test error:', error);\n        return {\n            success: false,\n            error: 'Connection failed'\n        };\n    }\n};\n// Auth helper functions\nconst getCurrentUser = async ()=>{\n    try {\n        const { data: { user }, error } = await supabase.auth.getUser();\n        if (error) {\n            console.error('Error getting current user:', error);\n            return null;\n        }\n        if (!user) {\n            return null;\n        }\n        // Get user profile from profiles table\n        const { data: profile, error: profileError } = await supabase.from('profiles').select('*').eq('id', user.id).single();\n        if (profileError) {\n            console.error('Error getting user profile:', profileError);\n            // Check if user is admin based on email\n            const isAdmin = user.email === '<EMAIL>' || user.email?.includes('admin') || user.user_metadata?.role === 'admin';\n            // Return basic user info if profile fetch fails\n            return {\n                ...user,\n                full_name: user.user_metadata?.full_name || user.email?.split('@')[0] || 'User',\n                role: isAdmin ? 'admin' : user.user_metadata?.role || 'student',\n                avatar_url: null,\n                phone: null,\n                is_suspended: false,\n                suspension_reason: null,\n                status: 'active',\n                is_active: true\n            };\n        }\n        // Combine auth user with profile data\n        return {\n            ...user,\n            ...profile\n        };\n    } catch (error) {\n        console.error('Error getting current user:', error);\n        return null;\n    }\n};\nconst signOut = async ()=>{\n    try {\n        const { error } = await supabase.auth.signOut();\n        return {\n            error\n        };\n    } catch (error) {\n        console.error('Error signing out:', error);\n        return {\n            error: 'Sign out failed'\n        };\n    }\n};\n// Theme preference functions\nconst getUserThemePreference = async (userId)=>{\n    try {\n        const { data, error } = await supabase.from('theme_preferences').select('*').eq('user_id', userId).single();\n        if (error) {\n            // If table doesn't exist or no data, return default dark theme\n            console.log('Theme preferences not found, using default dark theme');\n            return {\n                id: '',\n                user_id: userId,\n                theme: 'dark',\n                auto_switch: false,\n                created_at: '',\n                updated_at: ''\n            };\n        }\n        return data;\n    } catch (error) {\n        console.log('Theme preferences error, using default dark theme:', error);\n        return {\n            id: '',\n            user_id: userId,\n            theme: 'dark',\n            auto_switch: false,\n            created_at: '',\n            updated_at: ''\n        };\n    }\n};\nconst saveUserThemePreference = async (userId, theme, autoSwitch = false)=>{\n    try {\n        const { error } = await supabase.from('theme_preferences').upsert({\n            user_id: userId,\n            theme,\n            auto_switch: autoSwitch,\n            updated_at: new Date().toISOString()\n        });\n        if (error) {\n            console.log('Theme preferences table not available, skipping save');\n            return true // Return true to not break the app\n            ;\n        }\n        return true;\n    } catch (error) {\n        console.log('Theme preferences save error, continuing without saving:', error);\n        return true // Return true to not break the app\n        ;\n    }\n};\n// Contact form functions\nconst submitContactForm = async (formData)=>{\n    const { error } = await supabase.from('contact_submissions').insert([\n        formData\n    ]);\n    if (error) {\n        console.error('Error submitting contact form:', error);\n        return false;\n    }\n    return true;\n};\n// System settings functions\nconst getSystemSetting = async (key)=>{\n    const { data, error } = await supabase.from('system_settings').select('setting_value').eq('setting_key', key).single();\n    if (error) {\n        console.error('Error fetching system setting:', error);\n        return null;\n    }\n    return data?.setting_value;\n};\nconst getPublicSystemSettings = async ()=>{\n    const { data, error } = await supabase.from('system_settings').select('setting_key, setting_value').eq('is_public', true);\n    if (error) {\n        console.error('Error fetching public system settings:', error);\n        return {};\n    }\n    const settings = {};\n    data?.forEach((setting)=>{\n        settings[setting.setting_key] = setting.setting_value;\n    });\n    return settings;\n};\nconst updateSystemSetting = async (key, value)=>{\n    const { error } = await supabase.from('system_settings').update({\n        setting_value: value,\n        updated_at: new Date().toISOString()\n    }).eq('setting_key', key);\n    if (error) {\n        console.error('Error updating system setting:', error);\n        return false;\n    }\n    return true;\n};\n// Chart annotation functions\nconst getChartAnnotations = async (chartId)=>{\n    const { data, error } = await supabase.from('chart_annotations').select('*').eq('chart_id', chartId).order('created_at', {\n        ascending: true\n    });\n    if (error) {\n        console.error('Error fetching chart annotations:', error);\n        return [];\n    }\n    return data || [];\n};\nconst saveChartAnnotation = async (annotation)=>{\n    const { error } = await supabase.from('chart_annotations').insert([\n        annotation\n    ]);\n    if (error) {\n        console.error('Error saving chart annotation:', error);\n        return false;\n    }\n    return true;\n};\nconst updateChartAnnotation = async (id, updates)=>{\n    const { error } = await supabase.from('chart_annotations').update({\n        ...updates,\n        updated_at: new Date().toISOString()\n    }).eq('id', id);\n    if (error) {\n        console.error('Error updating chart annotation:', error);\n        return false;\n    }\n    return true;\n};\nconst deleteChartAnnotation = async (id)=>{\n    const { error } = await supabase.from('chart_annotations').delete().eq('id', id);\n    if (error) {\n        console.error('Error deleting chart annotation:', error);\n        return false;\n    }\n    return true;\n};\n// Inventory functions\nconst getInventoryItems = async (cafeteriaId)=>{\n    const { data, error } = await supabase.from('inventory_items').select('*').eq('cafeteria_id', cafeteriaId).order('name', {\n        ascending: true\n    });\n    if (error) {\n        console.error('Error fetching inventory items:', error);\n        return [];\n    }\n    return data || [];\n};\nconst saveInventoryItem = async (item)=>{\n    const { error } = await supabase.from('inventory_items').insert([\n        item\n    ]);\n    if (error) {\n        console.error('Error saving inventory item:', error);\n        return false;\n    }\n    return true;\n};\nconst updateInventoryItem = async (id, updates)=>{\n    try {\n        console.log('🔥 INVENTORY UPDATE FUNCTION CALLED 🔥');\n        console.log('Updating inventory item:', {\n            id,\n            updates\n        });\n        // Check if user is authenticated\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        if (authError || !user) {\n            console.error('User not authenticated for inventory update:', authError);\n            return false;\n        }\n        console.log('Authenticated user for inventory update:', user.id);\n        const updateData = {\n            ...updates,\n            updated_at: new Date().toISOString()\n        };\n        // DETAILED DEBUGGING: Log everything about the status\n        console.log('=== INVENTORY UPDATE DEBUG ===');\n        console.log('Full updateData object:', JSON.stringify(updateData, null, 2));\n        console.log('updateData.status type:', typeof updateData.status);\n        console.log('updateData.status value:', updateData.status);\n        // Map status values to match database constraint exactly\n        if (updateData.status) {\n            console.log('Original status value:', updateData.status);\n            // Ensure we only use the exact values allowed by the constraint\n            const allowedStatuses = [\n                'in_stock',\n                'low_stock',\n                'out_of_stock',\n                'expired'\n            ];\n            if (!allowedStatuses.includes(updateData.status)) {\n                console.log('Invalid status value, mapping to valid one');\n                // Map common variations to valid values\n                const statusMapping = {\n                    'in-stock': 'in_stock',\n                    'low': 'low_stock',\n                    'out-of-stock': 'out_of_stock',\n                    'available': 'in_stock',\n                    'unavailable': 'out_of_stock'\n                };\n                updateData.status = statusMapping[updateData.status] || 'in_stock';\n            }\n            console.log('Final status value:', updateData.status);\n            console.log('Final status type:', typeof updateData.status);\n        }\n        console.log('Final update data being sent:', JSON.stringify(updateData, null, 2));\n        console.log('=== END DEBUG ===');\n        // Try to update the inventory item\n        // If there's an ambiguous column reference error, it's likely due to a missing inventory_alerts table\n        const { data, error } = await supabase.from('inventory_items').update(updateData).eq('id', id).select();\n        if (error) {\n            console.error('Error updating inventory item:', {\n                error,\n                message: error.message,\n                details: error.details,\n                hint: error.hint,\n                code: error.code\n            });\n            console.error('Error details JSON:', JSON.stringify(error, null, 2));\n            // Handle specific RLS policy error\n            if (error.code === '42501' && error.message?.includes('row-level security policy')) {\n                console.error('❌ RLS POLICY ERROR: The trigger cannot create alerts due to row-level security.');\n                console.error('💡 SOLUTION: We\\'ll handle alert creation in the application instead of the trigger.');\n            }\n            return false;\n        }\n        if (!data || data.length === 0) {\n            console.error('No inventory item found with ID or access denied:', id);\n            return false;\n        }\n        console.log('Inventory item updated successfully:', data);\n        // Handle alert creation manually since triggers have RLS issues\n        const updatedItem = data[0];\n        if (updatedItem.status === 'low_stock' || updatedItem.status === 'out_of_stock') {\n            console.log('Creating inventory alert for status:', updatedItem.status);\n            // Check if alert already exists\n            const { data: existingAlert } = await supabase.from('inventory_alerts').select('id').eq('inventory_item_id', updatedItem.id).eq('alert_type', updatedItem.status).eq('is_resolved', false).single();\n            if (!existingAlert) {\n                // Create new alert\n                const alertMessage = updatedItem.status === 'out_of_stock' ? `${updatedItem.name} is out of stock` : `${updatedItem.name} is running low (${updatedItem.quantity} ${updatedItem.unit} remaining)`;\n                const { error: alertError } = await supabase.from('inventory_alerts').insert({\n                    cafeteria_id: updatedItem.cafeteria_id,\n                    inventory_item_id: updatedItem.id,\n                    alert_type: updatedItem.status,\n                    message: alertMessage,\n                    is_resolved: false\n                });\n                if (alertError) {\n                    console.error('Error creating inventory alert:', alertError);\n                } else {\n                    console.log('✅ Inventory alert created successfully');\n                }\n            }\n        }\n        // Resolve alerts when status improves\n        if (updatedItem.status === 'in_stock') {\n            console.log('Resolving inventory alerts for improved status');\n            const { error: resolveError } = await supabase.from('inventory_alerts').update({\n                is_resolved: true,\n                resolved_at: new Date().toISOString()\n            }).eq('inventory_item_id', updatedItem.id).in('alert_type', [\n                'low_stock',\n                'out_of_stock'\n            ]).eq('is_resolved', false);\n            if (resolveError) {\n                console.error('Error resolving inventory alerts:', resolveError);\n            } else {\n                console.log('✅ Inventory alerts resolved successfully');\n            }\n        }\n        return true;\n    } catch (err) {\n        console.error('Unexpected error updating inventory item:', err);\n        return false;\n    }\n};\nconst deleteInventoryItem = async (id)=>{\n    const { error } = await supabase.from('inventory_items').delete().eq('id', id);\n    if (error) {\n        console.error('Error deleting inventory item:', error);\n        return false;\n    }\n    return true;\n};\n// Export log functions\nconst createExportLog = async (exportData)=>{\n    const { data, error } = await supabase.from('export_logs').insert([\n        exportData\n    ]).select('id').single();\n    if (error) {\n        console.error('Error creating export log:', error);\n        return null;\n    }\n    return data?.id || null;\n};\nconst updateExportLog = async (id, updates)=>{\n    const { error } = await supabase.from('export_logs').update(updates).eq('id', id);\n    if (error) {\n        console.error('Error updating export log:', error);\n        return false;\n    }\n    return true;\n};\nconst getUserExportLogs = async (userId)=>{\n    const { data, error } = await supabase.from('export_logs').select('*').eq('user_id', userId).order('created_at', {\n        ascending: false\n    });\n    if (error) {\n        console.error('Error fetching export logs:', error);\n        return [];\n    }\n    return data || [];\n};\n// Analytics data functions\nconst getAnalyticsData = async (cafeteriaId, metricType, startDate, endDate)=>{\n    const { data, error } = await supabase.from('analytics_data').select('date_recorded, metric_value').eq('cafeteria_id', cafeteriaId).eq('metric_type', metricType).gte('date_recorded', startDate).lte('date_recorded', endDate).order('date_recorded', {\n        ascending: true\n    });\n    if (error) {\n        console.error('Error fetching analytics data:', error);\n        return [];\n    }\n    return data?.map((item)=>({\n            date: item.date_recorded,\n            value: Number(item.metric_value)\n        })) || [];\n};\nconst insertAnalyticsData = async (cafeteriaId, metricType, value, date)=>{\n    const { error } = await supabase.from('analytics_data').insert([\n        {\n            cafeteria_id: cafeteriaId,\n            metric_type: metricType,\n            metric_value: value,\n            date_recorded: date || new Date().toISOString().split('T')[0]\n        }\n    ]);\n    if (error) {\n        console.error('Error inserting analytics data:', error);\n        return false;\n    }\n    return true;\n};\n// User analytics preferences functions\nconst getUserAnalyticsPreferences = async (userId)=>{\n    const { data, error } = await supabase.from('user_analytics_preferences').select('*').eq('user_id', userId).single();\n    if (error && error.code !== 'PGRST116') {\n        console.error('Error fetching user analytics preferences:', error);\n        return null;\n    }\n    return data;\n};\nconst saveUserAnalyticsPreferences = async (userId, preferences)=>{\n    const { error } = await supabase.from('user_analytics_preferences').upsert({\n        user_id: userId,\n        ...preferences,\n        updated_at: new Date().toISOString()\n    });\n    if (error) {\n        console.error('Error saving user analytics preferences:', error);\n        return false;\n    }\n    return true;\n};\n// Navigation tracking functions\nconst trackNavigation = async (userId, fromPage, toPage, timeSpent)=>{\n    const { error } = await supabase.from('navigation_logs').insert([\n        {\n            user_id: userId,\n            from_page: fromPage,\n            to_page: toPage,\n            time_spent_seconds: timeSpent\n        }\n    ]);\n    if (error) {\n        console.error('Error tracking navigation:', error);\n        return false;\n    }\n    return true;\n};\n// Notification functions\nconst createNotification = async (userId, title, message, type = 'info', relatedOrderId)=>{\n    const { error } = await supabase.from('notifications').insert([\n        {\n            user_id: userId,\n            title,\n            message,\n            type,\n            related_order_id: relatedOrderId,\n            is_read: false,\n            created_at: new Date().toISOString()\n        }\n    ]);\n    if (error) {\n        console.error('Error creating notification:', error);\n        return false;\n    }\n    return true;\n};\n// Create order notification for cafeteria owners\nconst createOrderNotification = async (cafeteriaId, orderNumber, customerName)=>{\n    try {\n        // Get cafeteria owner\n        const { data: cafeteria, error: cafeteriaError } = await supabase.from('cafeterias').select('owner_id, name').eq('id', cafeteriaId).single();\n        if (cafeteriaError || !cafeteria) {\n            console.error('Error fetching cafeteria:', cafeteriaError);\n            return false;\n        }\n        const title = 'New Order Received';\n        const message = `New order #${orderNumber} received${customerName ? ` from ${customerName}` : ''} at ${cafeteria.name}`;\n        return await createNotification(cafeteria.owner_id, title, message, 'order', orderNumber);\n    } catch (error) {\n        console.error('Error creating order notification:', error);\n        return false;\n    }\n};\n// Analytics events tracking\nconst trackAnalyticsEvent = async (userId, eventType, eventData, pageUrl)=>{\n    const { error } = await supabase.from('analytics_events').insert([\n        {\n            user_id: userId,\n            event_type: eventType,\n            event_data: eventData,\n            page_url: pageUrl,\n            user_agent: typeof navigator !== 'undefined' ? navigator.userAgent : null\n        }\n    ]);\n    if (error) {\n        console.error('Error tracking analytics event:', error);\n        return false;\n    }\n    return true;\n};\n// Chart configuration functions\nconst getChartConfigurations = async (userId)=>{\n    const { data, error } = await supabase.from('chart_configurations').select('*').eq('user_id', userId).order('created_at', {\n        ascending: false\n    });\n    if (error) {\n        console.error('Error fetching chart configurations:', error);\n        return [];\n    }\n    return data || [];\n};\nconst saveChartConfiguration = async (userId, chartType, title, configuration, isDefault = false)=>{\n    const { error } = await supabase.from('chart_configurations').insert([\n        {\n            user_id: userId,\n            chart_type: chartType,\n            chart_title: title,\n            configuration,\n            is_default: isDefault\n        }\n    ]);\n    if (error) {\n        console.error('Error saving chart configuration:', error);\n        return false;\n    }\n    return true;\n};\n// User activity logging\nconst logUserActivity = async (userId, activityType, description, entityType, entityId, metadata)=>{\n    const { error } = await supabase.from('user_activity_logs').insert([\n        {\n            user_id: userId,\n            activity_type: activityType,\n            activity_description: description,\n            entity_type: entityType,\n            entity_id: entityId,\n            metadata: metadata || {}\n        }\n    ]);\n    if (error) {\n        console.error('Error logging user activity:', error);\n        return false;\n    }\n    return true;\n};\nconst getCareers = async ()=>{\n    const { data, error } = await supabase.from('careers').select('*').eq('status', 'active').order('created_at', {\n        ascending: false\n    });\n    if (error) {\n        console.error('Error fetching careers:', error);\n        return [];\n    }\n    return data || [];\n};\nconst createCareer = async (career)=>{\n    const { error } = await supabase.from('careers').insert([\n        career\n    ]);\n    if (error) {\n        console.error('Error creating career:', error);\n        return false;\n    }\n    return true;\n};\nconst updateCareer = async (id, updates)=>{\n    const { error } = await supabase.from('careers').update({\n        ...updates,\n        updated_at: new Date().toISOString()\n    }).eq('id', id);\n    if (error) {\n        console.error('Error updating career:', error);\n        return false;\n    }\n    return true;\n};\nconst deleteCareer = async (id)=>{\n    const { error } = await supabase.from('careers').update({\n        status: 'inactive'\n    }).eq('id', id);\n    if (error) {\n        console.error('Error deleting career:', error);\n        return false;\n    }\n    return true;\n};\n// Menu Items functions\nconst getMenuItems = async (cafeteriaId)=>{\n    let query = supabase.from('menu_items').select('*').order('name', {\n        ascending: true\n    });\n    if (cafeteriaId) {\n        query = query.eq('cafeteria_id', cafeteriaId);\n    }\n    const { data, error } = await query;\n    if (error) {\n        console.error('Error fetching menu items:', error);\n        return [];\n    }\n    return data || [];\n};\nconst addMenuItem = async (menuItem)=>{\n    const { error } = await supabase.from('menu_items').insert([\n        menuItem\n    ]);\n    if (error) {\n        console.error('Error adding menu item:', error);\n        return false;\n    }\n    return true;\n};\nconst updateMenuItem = async (id, updates)=>{\n    const { error } = await supabase.from('menu_items').update({\n        ...updates,\n        updated_at: new Date().toISOString()\n    }).eq('id', id);\n    if (error) {\n        console.error('Error updating menu item:', error);\n        return false;\n    }\n    return true;\n};\nconst deleteMenuItem = async (id)=>{\n    const { error } = await supabase.from('menu_items').delete().eq('id', id);\n    if (error) {\n        console.error('Error deleting menu item:', error);\n        return false;\n    }\n    return true;\n};\n// Cafeterias functions\nconst getCafeterias = async ()=>{\n    const { data, error } = await supabase.from('cafeterias').select('*').order('name', {\n        ascending: true\n    });\n    if (error) {\n        console.error('Error fetching cafeterias:', error);\n        return [];\n    }\n    return data || [];\n};\n// Support Tickets functions\nconst submitSupportTicket = async (ticket)=>{\n    // Generate a unique ticket number\n    const ticketNumber = `TKT-${Date.now()}-${Math.random().toString(36).substr(2, 5).toUpperCase()}`;\n    // Get user role if user_type not provided\n    let userType = ticket.user_type;\n    if (!userType) {\n        const { data: profile } = await supabase.from('profiles').select('role').eq('id', ticket.user_id).single();\n        if (profile?.role === 'cafeteria_manager') {\n            userType = 'cafeteria';\n        } else if (profile?.role === 'admin') {\n            userType = 'admin';\n        } else {\n            userType = 'student';\n        }\n    }\n    const ticketData = {\n        ticket_number: ticketNumber,\n        user_id: ticket.user_id,\n        title: ticket.title,\n        description: ticket.description,\n        category: ticket.category || 'general_inquiry',\n        priority: ticket.priority,\n        status: ticket.status || 'open',\n        user_type: userType,\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString()\n    };\n    const { error } = await supabase.from('support_tickets').insert([\n        ticketData\n    ]);\n    if (error) {\n        console.error('Error submitting support ticket:', error);\n        return false;\n    }\n    return true;\n};\nconst fetchSupportTickets = async (userId)=>{\n    try {\n        // First try with the join\n        let query = supabase.from('support_tickets').select(`\n        *,\n        profiles!user_id(\n          full_name,\n          role,\n          phone\n        )\n      `).order('created_at', {\n            ascending: false\n        });\n        if (userId) {\n            query = query.eq('user_id', userId);\n        }\n        const { data, error } = await query;\n        if (error) {\n            console.error('Error fetching support tickets with profiles join:', error);\n            console.error('Error details:', {\n                message: error.message,\n                details: error.details,\n                hint: error.hint,\n                code: error.code\n            });\n            // Fallback: try without the join\n            console.log('Trying fallback query without profiles join...');\n            let fallbackQuery = supabase.from('support_tickets').select('*').order('created_at', {\n                ascending: false\n            });\n            if (userId) {\n                fallbackQuery = fallbackQuery.eq('user_id', userId);\n            }\n            const { data: fallbackData, error: fallbackError } = await fallbackQuery;\n            if (fallbackError) {\n                console.error('Fallback query also failed:', fallbackError);\n                return [];\n            }\n            console.log('Fallback query successful, returning data without profile info');\n            return fallbackData || [];\n        }\n        return data || [];\n    } catch (error) {\n        console.error('Unexpected error in fetchSupportTickets:', error);\n        return [];\n    }\n};\n// Admin function to fetch all tickets with user information\nconst fetchAllSupportTicketsForAdmin = async ()=>{\n    try {\n        // First get support tickets with profiles\n        const { data: tickets, error: ticketsError } = await supabase.from('support_tickets').select(`\n        *,\n        profiles(\n          full_name,\n          role,\n          phone\n        )\n      `).order('created_at', {\n            ascending: false\n        });\n        if (ticketsError) {\n            console.error('Error fetching support tickets:', ticketsError);\n            return [];\n        }\n        // Then get user emails from auth.users\n        const userIds = tickets?.map((ticket)=>ticket.user_id).filter(Boolean) || [];\n        if (userIds.length === 0) {\n            return tickets || [];\n        }\n        const { data: authUsers, error: authError } = await supabase.auth.admin.listUsers();\n        if (authError) {\n            console.error('Error fetching auth users:', authError);\n            return tickets || [];\n        }\n        // Combine the data\n        const ticketsWithEmails = tickets?.map((ticket)=>({\n                ...ticket,\n                user_email: authUsers.users.find((user)=>user.id === ticket.user_id)?.email || 'No email'\n            })) || [];\n        return ticketsWithEmails;\n    } catch (error) {\n        console.error('Error in fetchAllSupportTicketsForAdmin:', error);\n        return [];\n    }\n};\n// Fetch tickets by user type (for admin dashboard)\nconst fetchSupportTicketsByType = async (userType)=>{\n    const { data, error } = await supabase.from('support_tickets').select(`\n      *,\n      profiles(\n        full_name,\n        role,\n        phone\n      )\n    `).eq('user_type', userType).order('created_at', {\n        ascending: false\n    });\n    if (error) {\n        console.error(`Error fetching ${userType} support tickets:`, error);\n        return [];\n    }\n    return data || [];\n};\n// Legacy functions for backward compatibility\nconst submitStudentMessage = async (message)=>{\n    return await submitSupportTicket({\n        user_id: message.user_id,\n        title: message.subject,\n        description: message.content,\n        category: message.user_type,\n        priority: message.priority,\n        status: message.status\n    });\n};\nconst fetchStudentMessages = async ()=>{\n    try {\n        console.log('Fetching student messages...');\n        const tickets = await fetchSupportTickets();\n        console.log('Successfully fetched tickets:', tickets.length);\n        return tickets;\n    } catch (error) {\n        console.error('Error in fetchStudentMessages:', error);\n        return [];\n    }\n};\n// Chat System Functions\nconst createChatConversation = async (conversation)=>{\n    // Get user role if user_type not provided\n    let userType = conversation.user_type;\n    if (!userType) {\n        const { data: profile } = await supabase.from('profiles').select('role').eq('id', conversation.user_id).single();\n        if (profile?.role === 'cafeteria_manager') {\n            userType = 'cafeteria';\n        } else if (profile?.role === 'admin') {\n            userType = 'admin';\n        } else {\n            userType = 'student';\n        }\n    }\n    const { data, error } = await supabase.from('chat_conversations').insert([\n        {\n            user_id: conversation.user_id,\n            subject: conversation.subject,\n            category: conversation.category || 'general_inquiry',\n            priority: conversation.priority || 'medium',\n            user_type: userType,\n            order_id: conversation.order_id || null,\n            ticket_id: conversation.ticket_id || null,\n            status: 'open'\n        }\n    ]).select('id').single();\n    if (error) {\n        console.error('Error creating chat conversation:', error);\n        return null;\n    }\n    return data?.id || null;\n};\nconst sendChatMessage = async (message)=>{\n    const { error } = await supabase.from('chat_messages').insert([\n        {\n            conversation_id: message.conversation_id,\n            sender_id: message.sender_id,\n            content: message.content,\n            message_type: message.message_type || 'text',\n            file_url: message.file_url || null,\n            file_name: message.file_name || null,\n            file_size: message.file_size || null,\n            is_read: false\n        }\n    ]);\n    if (error) {\n        console.error('Error sending chat message:', error);\n        return false;\n    }\n    // Update conversation updated_at\n    await supabase.from('chat_conversations').update({\n        updated_at: new Date().toISOString()\n    }).eq('id', message.conversation_id);\n    return true;\n};\nconst getChatConversations = async (userId, userType)=>{\n    let query = supabase.from('chat_conversations').select(`\n      *,\n      profiles!chat_conversations_user_id_fkey(\n        full_name,\n        email,\n        role\n      ),\n      support_agent:profiles!chat_conversations_support_agent_id_fkey(\n        full_name,\n        email\n      )\n    `).order('updated_at', {\n        ascending: false\n    });\n    if (userId) {\n        query = query.eq('user_id', userId);\n    }\n    if (userType) {\n        query = query.eq('user_type', userType);\n    }\n    const { data, error } = await query;\n    if (error) {\n        console.error('Error fetching chat conversations:', error);\n        return [];\n    }\n    return data || [];\n};\nconst getChatMessages = async (conversationId)=>{\n    const { data, error } = await supabase.from('chat_messages').select(`\n      *,\n      sender:profiles!chat_messages_sender_id_fkey(\n        full_name,\n        email,\n        role\n      )\n    `).eq('conversation_id', conversationId).order('created_at', {\n        ascending: true\n    });\n    if (error) {\n        console.error('Error fetching chat messages:', error);\n        return [];\n    }\n    return data || [];\n};\nconst markMessagesAsRead = async (conversationId, userId)=>{\n    const { error } = await supabase.from('chat_messages').update({\n        is_read: true\n    }).eq('conversation_id', conversationId).neq('sender_id', userId);\n    if (error) {\n        console.error('Error marking messages as read:', error);\n        return false;\n    }\n    return true;\n};\nconst assignChatToAgent = async (conversationId, agentId)=>{\n    const { error } = await supabase.from('chat_conversations').update({\n        support_agent_id: agentId,\n        status: 'in_progress',\n        updated_at: new Date().toISOString()\n    }).eq('id', conversationId);\n    if (error) {\n        console.error('Error assigning chat to agent:', error);\n        return false;\n    }\n    return true;\n};\nconst closeChatConversation = async (conversationId, rating, feedback)=>{\n    const updateData = {\n        status: 'closed',\n        closed_at: new Date().toISOString(),\n        updated_at: new Date().toISOString()\n    };\n    if (rating) updateData.rating = rating;\n    if (feedback) updateData.feedback = feedback;\n    const { error } = await supabase.from('chat_conversations').update(updateData).eq('id', conversationId);\n    if (error) {\n        console.error('Error closing chat conversation:', error);\n        return false;\n    }\n    return true;\n};\n// Convert support ticket to chat conversation\nconst convertTicketToChat = async (ticketId)=>{\n    // Get ticket details\n    const { data: ticket, error: ticketError } = await supabase.from('support_tickets').select('*').eq('id', ticketId).single();\n    if (ticketError || !ticket) {\n        console.error('Error fetching ticket for conversion:', ticketError);\n        return null;\n    }\n    // Create chat conversation linked to ticket\n    const conversationId = await createChatConversation({\n        user_id: ticket.user_id,\n        subject: ticket.title,\n        category: ticket.category,\n        priority: ticket.priority,\n        user_type: ticket.user_type,\n        order_id: ticket.order_id,\n        ticket_id: ticketId\n    });\n    if (conversationId) {\n        // Add initial message with ticket description\n        await sendChatMessage({\n            conversation_id: conversationId,\n            sender_id: ticket.user_id,\n            content: `Original ticket: ${ticket.description}`,\n            message_type: 'text'\n        });\n        // Update ticket status to indicate it has a chat\n        await supabase.from('support_tickets').update({\n            status: 'in_progress',\n            updated_at: new Date().toISOString()\n        }).eq('id', ticketId);\n    }\n    return conversationId;\n};\n// Get chat conversation for a ticket\nconst getChatForTicket = async (ticketId)=>{\n    const { data, error } = await supabase.from('chat_conversations').select(`\n      *,\n      profiles!chat_conversations_user_id_fkey(\n        full_name,\n        email,\n        role\n      ),\n      support_agent:profiles!chat_conversations_support_agent_id_fkey(\n        full_name,\n        email\n      )\n    `).eq('ticket_id', ticketId).single();\n    if (error) {\n        console.error('Error fetching chat for ticket:', error);\n        return null;\n    }\n    return data;\n};\n// Orders functions\nconst getOrders = async (cafeteriaId)=>{\n    let query = supabase.from('orders').select('*').order('created_at', {\n        ascending: false\n    });\n    if (cafeteriaId) {\n        query = query.eq('cafeteria_id', cafeteriaId);\n    }\n    const { data, error } = await query;\n    if (error) {\n        console.error('Error fetching orders:', error);\n        return [];\n    }\n    return data || [];\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./lib/supabase.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fsupport-tickets%2Froute&page=%2Fapi%2Fsupport-tickets%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsupport-tickets%2Froute.ts&appDir=C%3A%5CUsers%5Clkmd2%5CDesktop%5Cweb%20and%20mob%5Cgradproject%20v5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Clkmd2%5CDesktop%5Cweb%20and%20mob%5Cgradproject%20v5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fsupport-tickets%2Froute&page=%2Fapi%2Fsupport-tickets%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsupport-tickets%2Froute.ts&appDir=C%3A%5CUsers%5Clkmd2%5CDesktop%5Cweb%20and%20mob%5Cgradproject%20v5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Clkmd2%5CDesktop%5Cweb%20and%20mob%5Cgradproject%20v5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_lkmd2_Desktop_web_and_mob_gradproject_v5_app_api_support_tickets_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/support-tickets/route.ts */ \"(rsc)/./app/api/support-tickets/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/support-tickets/route\",\n        pathname: \"/api/support-tickets\",\n        filename: \"route\",\n        bundlePath: \"app/api/support-tickets/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\web and mob\\\\gradproject v5\\\\app\\\\api\\\\support-tickets\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_lkmd2_Desktop_web_and_mob_gradproject_v5_app_api_support_tickets_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fsupport-tickets%2Froute&page=%2Fapi%2Fsupport-tickets%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsupport-tickets%2Froute.ts&appDir=C%3A%5CUsers%5Clkmd2%5CDesktop%5Cweb%20and%20mob%5Cgradproject%20v5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Clkmd2%5CDesktop%5Cweb%20and%20mob%5Cgradproject%20v5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fsupport-tickets%2Froute&page=%2Fapi%2Fsupport-tickets%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsupport-tickets%2Froute.ts&appDir=C%3A%5CUsers%5Clkmd2%5CDesktop%5Cweb%20and%20mob%5Cgradproject%20v5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Clkmd2%5CDesktop%5Cweb%20and%20mob%5Cgradproject%20v5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();