"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/cafeteria/menu/page",{

/***/ "(app-pages-browser)/./lib/settings-service.ts":
/*!*********************************!*\
  !*** ./lib/settings-service.ts ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SettingsService: () => (/* binding */ SettingsService),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(app-pages-browser)/./lib/supabase.ts\");\n// Dynamic Settings Service - Uses existing tables to store configuration\n\n// Default configuration values\nconst DEFAULT_SETTINGS = {\n    // Platform Settings\n    platformName: \"UniEats\",\n    platformUrl: \"https://unieats.com\",\n    supportEmail: \"<EMAIL>\",\n    timezone: \"Africa/Cairo\",\n    dateFormat: \"YYYY-MM-DD\",\n    maintenanceMode: false,\n    newRegistrations: true,\n    cafeteriaApplications: true,\n    autoApprove: true,\n    maintenanceMessage: \"We're currently performing scheduled maintenance. Please check back soon.\",\n    // Financial Settings\n    serviceFeeRate: 0.04,\n    serviceFeeCap: 20.00,\n    commissionRate: 0.10,\n    minimumOrderAmount: 0.00,\n    // Performance Settings\n    apiTimeout: 10000,\n    retryAttempts: 3,\n    retryDelay: 1000,\n    cacheTtl: 300000,\n    // Rate Limiting\n    apiRateLimit: 100,\n    authRateLimit: 5,\n    uploadRateLimit: 10,\n    // UI Settings\n    defaultPreparationTime: 15,\n    dataRetentionDays: 90,\n    imageQuality: 75,\n    // Categories\n    menuCategories: [\n        \"Breakfast\",\n        \"Lunch\",\n        \"Dinner\",\n        \"Snacks\",\n        \"Beverages\",\n        \"Desserts\",\n        \"Vegan\",\n        \"Vegetarian\",\n        \"Gluten-Free\",\n        \"Keto-Friendly\",\n        \"Low-Calorie\",\n        \"Protein-Rich\"\n    ],\n    inventoryCategories: [\n        \"produce\",\n        \"meat\",\n        \"dairy\",\n        \"bakery\",\n        \"grains\",\n        \"beverages\",\n        \"condiments\",\n        \"frozen\",\n        \"other\"\n    ],\n    cafeteriaCategories: [\n        \"All\",\n        \"Fast Food\",\n        \"Healthy\",\n        \"Desserts\",\n        \"Beverages\",\n        \"Asian\"\n    ]\n};\n// Settings cache\nlet settingsCache = {};\nlet cacheTimestamp = 0;\nconst CACHE_DURATION = 5 * 60 * 1000 // 5 minutes\n;\nclass SettingsService {\n    // Get a setting value with fallback to default\n    static async getSetting(key, defaultValue) {\n        try {\n            // Check cache first\n            const now = Date.now();\n            if (now - cacheTimestamp < CACHE_DURATION && settingsCache[key] !== undefined) {\n                return settingsCache[key];\n            }\n            // Try to get from settings table\n            const { data: setting } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('settings').select('value').eq('key', key).single();\n            if (setting && setting.value !== undefined) {\n                settingsCache[key] = setting.value;\n                cacheTimestamp = now;\n                return setting.value;\n            }\n            // Fallback to default value\n            const fallbackValue = defaultValue !== undefined ? defaultValue : DEFAULT_SETTINGS[key];\n            settingsCache[key] = fallbackValue;\n            cacheTimestamp = now;\n            return fallbackValue;\n        } catch (error) {\n            console.error(\"Error getting setting \".concat(key, \":\"), error);\n            return defaultValue !== undefined ? defaultValue : DEFAULT_SETTINGS[key];\n        }\n    }\n    // Get multiple settings at once\n    static async getSettings(keys) {\n        const settings = {};\n        for (const key of keys){\n            settings[key] = await this.getSetting(key);\n        }\n        return settings;\n    }\n    // Get all platform settings\n    static async getPlatformSettings() {\n        return await this.getSettings([\n            'platformName',\n            'platformUrl',\n            'supportEmail',\n            'timezone',\n            'dateFormat',\n            'maintenanceMode',\n            'newRegistrations',\n            'cafeteriaApplications',\n            'autoApprove',\n            'maintenanceMessage'\n        ]);\n    }\n    // Get financial settings\n    static async getFinancialSettings() {\n        return await this.getSettings([\n            'serviceFeeRate',\n            'serviceFeeCap',\n            'commissionRate',\n            'minimumOrderAmount'\n        ]);\n    }\n    // Get performance settings\n    static async getPerformanceSettings() {\n        return await this.getSettings([\n            'apiTimeout',\n            'retryAttempts',\n            'retryDelay',\n            'cacheTtl',\n            'apiRateLimit',\n            'authRateLimit',\n            'uploadRateLimit'\n        ]);\n    }\n    // Get categories\n    static async getMenuCategories() {\n        return await this.getSetting('menuCategories', DEFAULT_SETTINGS.menuCategories);\n    }\n    static async getInventoryCategories() {\n        return await this.getSetting('inventoryCategories', DEFAULT_SETTINGS.inventoryCategories);\n    }\n    static async getCafeteriaCategories() {\n        return await this.getSetting('cafeteriaCategories', DEFAULT_SETTINGS.cafeteriaCategories);\n    }\n    // Specific getters for commonly used settings\n    static async getServiceFeeRate() {\n        return await this.getSetting('serviceFeeRate', 0.04);\n    }\n    static async getServiceFeeCap() {\n        return await this.getSetting('serviceFeeCap', 20.00);\n    }\n    static async getCommissionRate() {\n        return await this.getSetting('commissionRate', 0.10);\n    }\n    static async getDefaultPreparationTime() {\n        return await this.getSetting('defaultPreparationTime', 15);\n    }\n    // Update a setting (stores in settings table)\n    static async updateSetting(key, value) {\n        try {\n            // Clear cache\n            delete settingsCache[key];\n            cacheTimestamp = 0;\n            // Store in the settings table\n            const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('settings').upsert({\n                key: key,\n                value: value,\n                updated_at: new Date().toISOString()\n            });\n            if (error) {\n                console.error(\"Error updating setting \".concat(key, \":\"), error);\n                return false;\n            }\n            return true;\n        } catch (error) {\n            console.error(\"Error updating setting \".concat(key, \":\"), error);\n            return false;\n        }\n    }\n    // Clear settings cache\n    static clearCache() {\n        settingsCache = {};\n        cacheTimestamp = 0;\n    }\n    // Calculate order fees using dynamic rates\n    static async calculateOrderFees(subtotal) {\n        const serviceFeeRate = await this.getServiceFeeRate();\n        const serviceFeeCap = await this.getServiceFeeCap();\n        const commissionRate = await this.getCommissionRate();\n        const serviceFee = Math.min(subtotal * serviceFeeRate, serviceFeeCap);\n        const commission = subtotal * commissionRate;\n        const totalAmount = subtotal + serviceFee;\n        const cafeteriaRevenue = subtotal - commission;\n        const adminRevenue = serviceFee + commission;\n        return {\n            subtotal,\n            serviceFee,\n            commission,\n            totalAmount,\n            cafeteriaRevenue,\n            adminRevenue\n        };\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SettingsService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/settings-service.ts\n"));

/***/ })

});