"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./lib/supabase.ts":
/*!*************************!*\
  !*** ./lib/supabase.ts ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addMenuItem: () => (/* binding */ addMenuItem),\n/* harmony export */   assignChatToAgent: () => (/* binding */ assignChatToAgent),\n/* harmony export */   closeChatConversation: () => (/* binding */ closeChatConversation),\n/* harmony export */   convertTicketToChat: () => (/* binding */ convertTicketToChat),\n/* harmony export */   createCareer: () => (/* binding */ createCareer),\n/* harmony export */   createChatConversation: () => (/* binding */ createChatConversation),\n/* harmony export */   createExportLog: () => (/* binding */ createExportLog),\n/* harmony export */   createNotification: () => (/* binding */ createNotification),\n/* harmony export */   createOrderNotification: () => (/* binding */ createOrderNotification),\n/* harmony export */   createSupabaseAdmin: () => (/* binding */ createSupabaseAdmin),\n/* harmony export */   deleteCareer: () => (/* binding */ deleteCareer),\n/* harmony export */   deleteChartAnnotation: () => (/* binding */ deleteChartAnnotation),\n/* harmony export */   deleteInventoryItem: () => (/* binding */ deleteInventoryItem),\n/* harmony export */   deleteMenuItem: () => (/* binding */ deleteMenuItem),\n/* harmony export */   fetchAllSupportTicketsForAdmin: () => (/* binding */ fetchAllSupportTicketsForAdmin),\n/* harmony export */   fetchStudentMessages: () => (/* binding */ fetchStudentMessages),\n/* harmony export */   fetchSupportTickets: () => (/* binding */ fetchSupportTickets),\n/* harmony export */   fetchSupportTicketsByType: () => (/* binding */ fetchSupportTicketsByType),\n/* harmony export */   getAnalyticsData: () => (/* binding */ getAnalyticsData),\n/* harmony export */   getCafeterias: () => (/* binding */ getCafeterias),\n/* harmony export */   getCareers: () => (/* binding */ getCareers),\n/* harmony export */   getChartAnnotations: () => (/* binding */ getChartAnnotations),\n/* harmony export */   getChartConfigurations: () => (/* binding */ getChartConfigurations),\n/* harmony export */   getChatConversations: () => (/* binding */ getChatConversations),\n/* harmony export */   getChatForTicket: () => (/* binding */ getChatForTicket),\n/* harmony export */   getChatMessages: () => (/* binding */ getChatMessages),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   getInventoryItems: () => (/* binding */ getInventoryItems),\n/* harmony export */   getMenuItems: () => (/* binding */ getMenuItems),\n/* harmony export */   getOrders: () => (/* binding */ getOrders),\n/* harmony export */   getPublicSystemSettings: () => (/* binding */ getPublicSystemSettings),\n/* harmony export */   getSystemSetting: () => (/* binding */ getSystemSetting),\n/* harmony export */   getUserAnalyticsPreferences: () => (/* binding */ getUserAnalyticsPreferences),\n/* harmony export */   getUserExportLogs: () => (/* binding */ getUserExportLogs),\n/* harmony export */   getUserThemePreference: () => (/* binding */ getUserThemePreference),\n/* harmony export */   insertAnalyticsData: () => (/* binding */ insertAnalyticsData),\n/* harmony export */   logUserActivity: () => (/* binding */ logUserActivity),\n/* harmony export */   markMessagesAsRead: () => (/* binding */ markMessagesAsRead),\n/* harmony export */   saveChartAnnotation: () => (/* binding */ saveChartAnnotation),\n/* harmony export */   saveChartConfiguration: () => (/* binding */ saveChartConfiguration),\n/* harmony export */   saveInventoryItem: () => (/* binding */ saveInventoryItem),\n/* harmony export */   saveUserAnalyticsPreferences: () => (/* binding */ saveUserAnalyticsPreferences),\n/* harmony export */   saveUserThemePreference: () => (/* binding */ saveUserThemePreference),\n/* harmony export */   sendChatMessage: () => (/* binding */ sendChatMessage),\n/* harmony export */   signOut: () => (/* binding */ signOut),\n/* harmony export */   submitContactForm: () => (/* binding */ submitContactForm),\n/* harmony export */   submitStudentMessage: () => (/* binding */ submitStudentMessage),\n/* harmony export */   submitSupportTicket: () => (/* binding */ submitSupportTicket),\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   testSupabaseConnection: () => (/* binding */ testSupabaseConnection),\n/* harmony export */   trackAnalyticsEvent: () => (/* binding */ trackAnalyticsEvent),\n/* harmony export */   trackNavigation: () => (/* binding */ trackNavigation),\n/* harmony export */   updateCareer: () => (/* binding */ updateCareer),\n/* harmony export */   updateChartAnnotation: () => (/* binding */ updateChartAnnotation),\n/* harmony export */   updateExportLog: () => (/* binding */ updateExportLog),\n/* harmony export */   updateInventoryItem: () => (/* binding */ updateInventoryItem),\n/* harmony export */   updateMenuItem: () => (/* binding */ updateMenuItem),\n/* harmony export */   updateSystemSetting: () => (/* binding */ updateSystemSetting)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(app-pages-browser)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n\nconst supabaseUrl = \"https://lqtnaxvqkoynaziiinqh.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImxxdG5heHZxa295bmF6aWlpbnFoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc1ODUzMjcsImV4cCI6MjA2MzE2MTMyN30.MEMp-4fuLCMKaW-E_g56vsYFNKqzrftjhYfD_w1u0PA\";\nif (!supabaseUrl || !supabaseAnonKey) {\n    throw new Error('Missing Supabase environment variables');\n}\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey, {\n    auth: {\n        autoRefreshToken: true,\n        persistSession: true,\n        detectSessionInUrl: true\n    }\n});\n// Service role client for admin operations (bypasses RLS) - SERVER SIDE ONLY\nconst createSupabaseAdmin = ()=>{\n    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n    if (!supabaseServiceKey) {\n        throw new Error('SUPABASE_SERVICE_ROLE_KEY is required for admin operations');\n    }\n    return (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseServiceKey, {\n        auth: {\n            autoRefreshToken: false,\n            persistSession: false\n        }\n    });\n};\n// Connection test function\nconst testSupabaseConnection = async ()=>{\n    try {\n        const { data, error } = await supabase.from('cafeterias').select('id, name').limit(1);\n        if (error) {\n            console.error('Supabase connection test failed:', error);\n            return {\n                success: false,\n                error: error.message\n            };\n        }\n        return {\n            success: true,\n            data\n        };\n    } catch (error) {\n        console.error('Supabase connection test error:', error);\n        return {\n            success: false,\n            error: 'Connection failed'\n        };\n    }\n};\n// Auth helper functions\nconst getCurrentUser = async ()=>{\n    try {\n        const { data: { user }, error } = await supabase.auth.getUser();\n        if (error) {\n            console.error('Error getting current user:', error);\n            return null;\n        }\n        if (!user) {\n            return null;\n        }\n        // Get user profile from profiles table\n        const { data: profile, error: profileError } = await supabase.from('profiles').select('*').eq('id', user.id).single();\n        if (profileError) {\n            var _user_email, _user_user_metadata, _user_user_metadata1, _user_email1, _user_user_metadata2;\n            console.error('Error getting user profile:', profileError);\n            // Check if user is admin based on email\n            const isAdmin = user.email === '<EMAIL>' || ((_user_email = user.email) === null || _user_email === void 0 ? void 0 : _user_email.includes('admin')) || ((_user_user_metadata = user.user_metadata) === null || _user_user_metadata === void 0 ? void 0 : _user_user_metadata.role) === 'admin';\n            // Return basic user info if profile fetch fails\n            return {\n                ...user,\n                full_name: ((_user_user_metadata1 = user.user_metadata) === null || _user_user_metadata1 === void 0 ? void 0 : _user_user_metadata1.full_name) || ((_user_email1 = user.email) === null || _user_email1 === void 0 ? void 0 : _user_email1.split('@')[0]) || 'User',\n                role: isAdmin ? 'admin' : ((_user_user_metadata2 = user.user_metadata) === null || _user_user_metadata2 === void 0 ? void 0 : _user_user_metadata2.role) || 'student',\n                avatar_url: null,\n                phone: null,\n                is_suspended: false,\n                suspension_reason: null,\n                status: 'active',\n                is_active: true\n            };\n        }\n        // Combine auth user with profile data\n        return {\n            ...user,\n            ...profile\n        };\n    } catch (error) {\n        console.error('Error getting current user:', error);\n        return null;\n    }\n};\nconst signOut = async ()=>{\n    try {\n        const { error } = await supabase.auth.signOut();\n        return {\n            error\n        };\n    } catch (error) {\n        console.error('Error signing out:', error);\n        return {\n            error: 'Sign out failed'\n        };\n    }\n};\n// Theme preference functions\nconst getUserThemePreference = async (userId)=>{\n    try {\n        const { data, error } = await supabase.from('theme_preferences').select('*').eq('user_id', userId).single();\n        if (error) {\n            // If table doesn't exist or no data, return default dark theme\n            console.log('Theme preferences not found, using default dark theme');\n            return {\n                id: '',\n                user_id: userId,\n                theme: 'dark',\n                auto_switch: false,\n                created_at: '',\n                updated_at: ''\n            };\n        }\n        return data;\n    } catch (error) {\n        console.log('Theme preferences error, using default dark theme:', error);\n        return {\n            id: '',\n            user_id: userId,\n            theme: 'dark',\n            auto_switch: false,\n            created_at: '',\n            updated_at: ''\n        };\n    }\n};\nconst saveUserThemePreference = async function(userId, theme) {\n    let autoSwitch = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;\n    try {\n        const { error } = await supabase.from('theme_preferences').upsert({\n            user_id: userId,\n            theme,\n            auto_switch: autoSwitch,\n            updated_at: new Date().toISOString()\n        });\n        if (error) {\n            console.log('Theme preferences table not available, skipping save');\n            return true // Return true to not break the app\n            ;\n        }\n        return true;\n    } catch (error) {\n        console.log('Theme preferences save error, continuing without saving:', error);\n        return true // Return true to not break the app\n        ;\n    }\n};\n// Contact form functions\nconst submitContactForm = async (formData)=>{\n    const { error } = await supabase.from('contact_submissions').insert([\n        formData\n    ]);\n    if (error) {\n        console.error('Error submitting contact form:', error);\n        return false;\n    }\n    return true;\n};\n// System settings functions\nconst getSystemSetting = async (key)=>{\n    const { data, error } = await supabase.from('system_settings').select('setting_value').eq('setting_key', key).single();\n    if (error) {\n        console.error('Error fetching system setting:', error);\n        return null;\n    }\n    return data === null || data === void 0 ? void 0 : data.setting_value;\n};\nconst getPublicSystemSettings = async ()=>{\n    const { data, error } = await supabase.from('system_settings').select('setting_key, setting_value').eq('is_public', true);\n    if (error) {\n        console.error('Error fetching public system settings:', error);\n        return {};\n    }\n    const settings = {};\n    data === null || data === void 0 ? void 0 : data.forEach((setting)=>{\n        settings[setting.setting_key] = setting.setting_value;\n    });\n    return settings;\n};\nconst updateSystemSetting = async (key, value)=>{\n    const { error } = await supabase.from('system_settings').update({\n        setting_value: value,\n        updated_at: new Date().toISOString()\n    }).eq('setting_key', key);\n    if (error) {\n        console.error('Error updating system setting:', error);\n        return false;\n    }\n    return true;\n};\n// Chart annotation functions\nconst getChartAnnotations = async (chartId)=>{\n    const { data, error } = await supabase.from('chart_annotations').select('*').eq('chart_id', chartId).order('created_at', {\n        ascending: true\n    });\n    if (error) {\n        console.error('Error fetching chart annotations:', error);\n        return [];\n    }\n    return data || [];\n};\nconst saveChartAnnotation = async (annotation)=>{\n    const { error } = await supabase.from('chart_annotations').insert([\n        annotation\n    ]);\n    if (error) {\n        console.error('Error saving chart annotation:', error);\n        return false;\n    }\n    return true;\n};\nconst updateChartAnnotation = async (id, updates)=>{\n    const { error } = await supabase.from('chart_annotations').update({\n        ...updates,\n        updated_at: new Date().toISOString()\n    }).eq('id', id);\n    if (error) {\n        console.error('Error updating chart annotation:', error);\n        return false;\n    }\n    return true;\n};\nconst deleteChartAnnotation = async (id)=>{\n    const { error } = await supabase.from('chart_annotations').delete().eq('id', id);\n    if (error) {\n        console.error('Error deleting chart annotation:', error);\n        return false;\n    }\n    return true;\n};\n// Inventory functions\nconst getInventoryItems = async (cafeteriaId)=>{\n    const { data, error } = await supabase.from('inventory_items').select('*').eq('cafeteria_id', cafeteriaId).order('name', {\n        ascending: true\n    });\n    if (error) {\n        console.error('Error fetching inventory items:', error);\n        return [];\n    }\n    return data || [];\n};\nconst saveInventoryItem = async (item)=>{\n    const { error } = await supabase.from('inventory_items').insert([\n        item\n    ]);\n    if (error) {\n        console.error('Error saving inventory item:', error);\n        return false;\n    }\n    return true;\n};\nconst updateInventoryItem = async (id, updates)=>{\n    try {\n        console.log('🔥 INVENTORY UPDATE FUNCTION CALLED 🔥');\n        console.log('Updating inventory item:', {\n            id,\n            updates\n        });\n        // Check if user is authenticated\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        if (authError || !user) {\n            console.error('User not authenticated for inventory update:', authError);\n            return false;\n        }\n        console.log('Authenticated user for inventory update:', user.id);\n        const updateData = {\n            ...updates,\n            updated_at: new Date().toISOString()\n        };\n        // DETAILED DEBUGGING: Log everything about the status\n        console.log('=== INVENTORY UPDATE DEBUG ===');\n        console.log('Full updateData object:', JSON.stringify(updateData, null, 2));\n        console.log('updateData.status type:', typeof updateData.status);\n        console.log('updateData.status value:', updateData.status);\n        // Map status values to match database constraint exactly\n        if (updateData.status) {\n            console.log('Original status value:', updateData.status);\n            // Ensure we only use the exact values allowed by the constraint\n            const allowedStatuses = [\n                'in_stock',\n                'low_stock',\n                'out_of_stock',\n                'expired'\n            ];\n            if (!allowedStatuses.includes(updateData.status)) {\n                console.log('Invalid status value, mapping to valid one');\n                // Map common variations to valid values\n                const statusMapping = {\n                    'in-stock': 'in_stock',\n                    'low': 'low_stock',\n                    'out-of-stock': 'out_of_stock',\n                    'available': 'in_stock',\n                    'unavailable': 'out_of_stock'\n                };\n                updateData.status = statusMapping[updateData.status] || 'in_stock';\n            }\n            console.log('Final status value:', updateData.status);\n            console.log('Final status type:', typeof updateData.status);\n        }\n        console.log('Final update data being sent:', JSON.stringify(updateData, null, 2));\n        console.log('=== END DEBUG ===');\n        // Try to update the inventory item\n        // If there's an ambiguous column reference error, it's likely due to a missing inventory_alerts table\n        const { data, error } = await supabase.from('inventory_items').update(updateData).eq('id', id).select();\n        if (error) {\n            var _error_message;\n            console.error('Error updating inventory item:', {\n                error,\n                message: error.message,\n                details: error.details,\n                hint: error.hint,\n                code: error.code\n            });\n            console.error('Error details JSON:', JSON.stringify(error, null, 2));\n            // Handle specific RLS policy error\n            if (error.code === '42501' && ((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('row-level security policy'))) {\n                console.error('❌ RLS POLICY ERROR: The trigger cannot create alerts due to row-level security.');\n                console.error('💡 SOLUTION: We\\'ll handle alert creation in the application instead of the trigger.');\n            }\n            return false;\n        }\n        if (!data || data.length === 0) {\n            console.error('No inventory item found with ID or access denied:', id);\n            return false;\n        }\n        console.log('Inventory item updated successfully:', data);\n        // Handle alert creation manually since triggers have RLS issues\n        const updatedItem = data[0];\n        if (updatedItem.status === 'low_stock' || updatedItem.status === 'out_of_stock') {\n            console.log('Creating inventory alert for status:', updatedItem.status);\n            // Check if alert already exists\n            const { data: existingAlert } = await supabase.from('inventory_alerts').select('id').eq('inventory_item_id', updatedItem.id).eq('alert_type', updatedItem.status).eq('is_resolved', false).single();\n            if (!existingAlert) {\n                // Create new alert\n                const alertMessage = updatedItem.status === 'out_of_stock' ? \"\".concat(updatedItem.name, \" is out of stock\") : \"\".concat(updatedItem.name, \" is running low (\").concat(updatedItem.quantity, \" \").concat(updatedItem.unit, \" remaining)\");\n                const { error: alertError } = await supabase.from('inventory_alerts').insert({\n                    cafeteria_id: updatedItem.cafeteria_id,\n                    inventory_item_id: updatedItem.id,\n                    alert_type: updatedItem.status,\n                    message: alertMessage,\n                    is_resolved: false\n                });\n                if (alertError) {\n                    console.error('Error creating inventory alert:', alertError);\n                } else {\n                    console.log('✅ Inventory alert created successfully');\n                }\n            }\n        }\n        // Resolve alerts when status improves\n        if (updatedItem.status === 'in_stock') {\n            console.log('Resolving inventory alerts for improved status');\n            const { error: resolveError } = await supabase.from('inventory_alerts').update({\n                is_resolved: true,\n                resolved_at: new Date().toISOString()\n            }).eq('inventory_item_id', updatedItem.id).in('alert_type', [\n                'low_stock',\n                'out_of_stock'\n            ]).eq('is_resolved', false);\n            if (resolveError) {\n                console.error('Error resolving inventory alerts:', resolveError);\n            } else {\n                console.log('✅ Inventory alerts resolved successfully');\n            }\n        }\n        return true;\n    } catch (err) {\n        console.error('Unexpected error updating inventory item:', err);\n        return false;\n    }\n};\nconst deleteInventoryItem = async (id)=>{\n    const { error } = await supabase.from('inventory_items').delete().eq('id', id);\n    if (error) {\n        console.error('Error deleting inventory item:', error);\n        return false;\n    }\n    return true;\n};\n// Export log functions\nconst createExportLog = async (exportData)=>{\n    const { data, error } = await supabase.from('export_logs').insert([\n        exportData\n    ]).select('id').single();\n    if (error) {\n        console.error('Error creating export log:', error);\n        return null;\n    }\n    return (data === null || data === void 0 ? void 0 : data.id) || null;\n};\nconst updateExportLog = async (id, updates)=>{\n    const { error } = await supabase.from('export_logs').update(updates).eq('id', id);\n    if (error) {\n        console.error('Error updating export log:', error);\n        return false;\n    }\n    return true;\n};\nconst getUserExportLogs = async (userId)=>{\n    const { data, error } = await supabase.from('export_logs').select('*').eq('user_id', userId).order('created_at', {\n        ascending: false\n    });\n    if (error) {\n        console.error('Error fetching export logs:', error);\n        return [];\n    }\n    return data || [];\n};\n// Analytics data functions\nconst getAnalyticsData = async (cafeteriaId, metricType, startDate, endDate)=>{\n    const { data, error } = await supabase.from('analytics_data').select('date_recorded, metric_value').eq('cafeteria_id', cafeteriaId).eq('metric_type', metricType).gte('date_recorded', startDate).lte('date_recorded', endDate).order('date_recorded', {\n        ascending: true\n    });\n    if (error) {\n        console.error('Error fetching analytics data:', error);\n        return [];\n    }\n    return (data === null || data === void 0 ? void 0 : data.map((item)=>({\n            date: item.date_recorded,\n            value: Number(item.metric_value)\n        }))) || [];\n};\nconst insertAnalyticsData = async (cafeteriaId, metricType, value, date)=>{\n    const { error } = await supabase.from('analytics_data').insert([\n        {\n            cafeteria_id: cafeteriaId,\n            metric_type: metricType,\n            metric_value: value,\n            date_recorded: date || new Date().toISOString().split('T')[0]\n        }\n    ]);\n    if (error) {\n        console.error('Error inserting analytics data:', error);\n        return false;\n    }\n    return true;\n};\n// User analytics preferences functions\nconst getUserAnalyticsPreferences = async (userId)=>{\n    const { data, error } = await supabase.from('user_analytics_preferences').select('*').eq('user_id', userId).single();\n    if (error && error.code !== 'PGRST116') {\n        console.error('Error fetching user analytics preferences:', error);\n        return null;\n    }\n    return data;\n};\nconst saveUserAnalyticsPreferences = async (userId, preferences)=>{\n    const { error } = await supabase.from('user_analytics_preferences').upsert({\n        user_id: userId,\n        ...preferences,\n        updated_at: new Date().toISOString()\n    });\n    if (error) {\n        console.error('Error saving user analytics preferences:', error);\n        return false;\n    }\n    return true;\n};\n// Navigation tracking functions\nconst trackNavigation = async (userId, fromPage, toPage, timeSpent)=>{\n    const { error } = await supabase.from('navigation_logs').insert([\n        {\n            user_id: userId,\n            from_page: fromPage,\n            to_page: toPage,\n            time_spent_seconds: timeSpent\n        }\n    ]);\n    if (error) {\n        console.error('Error tracking navigation:', error);\n        return false;\n    }\n    return true;\n};\n// Notification functions\nconst createNotification = async function(userId, title, message) {\n    let type = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 'info', relatedOrderId = arguments.length > 4 ? arguments[4] : void 0;\n    const { error } = await supabase.from('notifications').insert([\n        {\n            user_id: userId,\n            title,\n            message,\n            type,\n            related_order_id: relatedOrderId,\n            is_read: false,\n            created_at: new Date().toISOString()\n        }\n    ]);\n    if (error) {\n        console.error('Error creating notification:', error);\n        return false;\n    }\n    return true;\n};\n// Create order notification for cafeteria owners\nconst createOrderNotification = async (cafeteriaId, orderNumber, customerName)=>{\n    try {\n        // Get cafeteria owner\n        const { data: cafeteria, error: cafeteriaError } = await supabase.from('cafeterias').select('owner_id, name').eq('id', cafeteriaId).single();\n        if (cafeteriaError || !cafeteria) {\n            console.error('Error fetching cafeteria:', cafeteriaError);\n            return false;\n        }\n        const title = 'New Order Received';\n        const message = \"New order #\".concat(orderNumber, \" received\").concat(customerName ? \" from \".concat(customerName) : '', \" at \").concat(cafeteria.name);\n        return await createNotification(cafeteria.owner_id, title, message, 'order', orderNumber);\n    } catch (error) {\n        console.error('Error creating order notification:', error);\n        return false;\n    }\n};\n// Analytics events tracking\nconst trackAnalyticsEvent = async (userId, eventType, eventData, pageUrl)=>{\n    const { error } = await supabase.from('analytics_events').insert([\n        {\n            user_id: userId,\n            event_type: eventType,\n            event_data: eventData,\n            page_url: pageUrl,\n            user_agent: typeof navigator !== 'undefined' ? navigator.userAgent : null\n        }\n    ]);\n    if (error) {\n        console.error('Error tracking analytics event:', error);\n        return false;\n    }\n    return true;\n};\n// Chart configuration functions\nconst getChartConfigurations = async (userId)=>{\n    const { data, error } = await supabase.from('chart_configurations').select('*').eq('user_id', userId).order('created_at', {\n        ascending: false\n    });\n    if (error) {\n        console.error('Error fetching chart configurations:', error);\n        return [];\n    }\n    return data || [];\n};\nconst saveChartConfiguration = async function(userId, chartType, title, configuration) {\n    let isDefault = arguments.length > 4 && arguments[4] !== void 0 ? arguments[4] : false;\n    const { error } = await supabase.from('chart_configurations').insert([\n        {\n            user_id: userId,\n            chart_type: chartType,\n            chart_title: title,\n            configuration,\n            is_default: isDefault\n        }\n    ]);\n    if (error) {\n        console.error('Error saving chart configuration:', error);\n        return false;\n    }\n    return true;\n};\n// User activity logging\nconst logUserActivity = async (userId, activityType, description, entityType, entityId, metadata)=>{\n    const { error } = await supabase.from('user_activity_logs').insert([\n        {\n            user_id: userId,\n            activity_type: activityType,\n            activity_description: description,\n            entity_type: entityType,\n            entity_id: entityId,\n            metadata: metadata || {}\n        }\n    ]);\n    if (error) {\n        console.error('Error logging user activity:', error);\n        return false;\n    }\n    return true;\n};\nconst getCareers = async ()=>{\n    const { data, error } = await supabase.from('careers').select('*').eq('status', 'active').order('created_at', {\n        ascending: false\n    });\n    if (error) {\n        console.error('Error fetching careers:', error);\n        return [];\n    }\n    return data || [];\n};\nconst createCareer = async (career)=>{\n    const { error } = await supabase.from('careers').insert([\n        career\n    ]);\n    if (error) {\n        console.error('Error creating career:', error);\n        return false;\n    }\n    return true;\n};\nconst updateCareer = async (id, updates)=>{\n    const { error } = await supabase.from('careers').update({\n        ...updates,\n        updated_at: new Date().toISOString()\n    }).eq('id', id);\n    if (error) {\n        console.error('Error updating career:', error);\n        return false;\n    }\n    return true;\n};\nconst deleteCareer = async (id)=>{\n    const { error } = await supabase.from('careers').update({\n        status: 'inactive'\n    }).eq('id', id);\n    if (error) {\n        console.error('Error deleting career:', error);\n        return false;\n    }\n    return true;\n};\n// Menu Items functions\nconst getMenuItems = async (cafeteriaId)=>{\n    let query = supabase.from('menu_items').select('*').order('name', {\n        ascending: true\n    });\n    if (cafeteriaId) {\n        query = query.eq('cafeteria_id', cafeteriaId);\n    }\n    const { data, error } = await query;\n    if (error) {\n        console.error('Error fetching menu items:', error);\n        return [];\n    }\n    return data || [];\n};\nconst addMenuItem = async (menuItem)=>{\n    const { error } = await supabase.from('menu_items').insert([\n        menuItem\n    ]);\n    if (error) {\n        console.error('Error adding menu item:', error);\n        return false;\n    }\n    return true;\n};\nconst updateMenuItem = async (id, updates)=>{\n    const { error } = await supabase.from('menu_items').update({\n        ...updates,\n        updated_at: new Date().toISOString()\n    }).eq('id', id);\n    if (error) {\n        console.error('Error updating menu item:', error);\n        return false;\n    }\n    return true;\n};\nconst deleteMenuItem = async (id)=>{\n    const { error } = await supabase.from('menu_items').delete().eq('id', id);\n    if (error) {\n        console.error('Error deleting menu item:', error);\n        return false;\n    }\n    return true;\n};\n// Cafeterias functions\nconst getCafeterias = async ()=>{\n    const { data, error } = await supabase.from('cafeterias').select('*').order('name', {\n        ascending: true\n    });\n    if (error) {\n        console.error('Error fetching cafeterias:', error);\n        return [];\n    }\n    return data || [];\n};\n// Support Tickets functions\nconst submitSupportTicket = async (ticket)=>{\n    // Generate a unique ticket number\n    const ticketNumber = \"TKT-\".concat(Date.now(), \"-\").concat(Math.random().toString(36).substr(2, 5).toUpperCase());\n    // Get user role if user_type not provided\n    let userType = ticket.user_type;\n    if (!userType) {\n        const { data: profile } = await supabase.from('profiles').select('role').eq('id', ticket.user_id).single();\n        if ((profile === null || profile === void 0 ? void 0 : profile.role) === 'cafeteria_manager') {\n            userType = 'cafeteria';\n        } else if ((profile === null || profile === void 0 ? void 0 : profile.role) === 'admin') {\n            userType = 'admin';\n        } else {\n            userType = 'student';\n        }\n    }\n    const ticketData = {\n        ticket_number: ticketNumber,\n        user_id: ticket.user_id,\n        title: ticket.title,\n        description: ticket.description,\n        category: ticket.category || 'general_inquiry',\n        priority: ticket.priority,\n        status: ticket.status || 'open',\n        user_type: userType,\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString()\n    };\n    const { error } = await supabase.from('support_tickets').insert([\n        ticketData\n    ]);\n    if (error) {\n        console.error('Error submitting support ticket:', error);\n        return false;\n    }\n    return true;\n};\nconst fetchSupportTickets = async (userId)=>{\n    try {\n        // First try with the join\n        let query = supabase.from('support_tickets').select(\"\\n        *,\\n        profiles!user_id(\\n          full_name,\\n          role,\\n          phone\\n        )\\n      \").order('created_at', {\n            ascending: false\n        });\n        if (userId) {\n            query = query.eq('user_id', userId);\n        }\n        const { data, error } = await query;\n        if (error) {\n            console.error('Error fetching support tickets with profiles join:', error);\n            console.error('Error details:', {\n                message: error.message,\n                details: error.details,\n                hint: error.hint,\n                code: error.code\n            });\n            // Fallback: try without the join\n            console.log('Trying fallback query without profiles join...');\n            let fallbackQuery = supabase.from('support_tickets').select('*').order('created_at', {\n                ascending: false\n            });\n            if (userId) {\n                fallbackQuery = fallbackQuery.eq('user_id', userId);\n            }\n            const { data: fallbackData, error: fallbackError } = await fallbackQuery;\n            if (fallbackError) {\n                console.error('Fallback query also failed:', fallbackError);\n                return [];\n            }\n            console.log('Fallback query successful, returning data without profile info');\n            return fallbackData || [];\n        }\n        return data || [];\n    } catch (error) {\n        console.error('Unexpected error in fetchSupportTickets:', error);\n        return [];\n    }\n};\n// Admin function to fetch all tickets with user information\nconst fetchAllSupportTicketsForAdmin = async ()=>{\n    try {\n        // First get support tickets with profiles\n        const { data: tickets, error: ticketsError } = await supabase.from('support_tickets').select(\"\\n        *,\\n        profiles(\\n          full_name,\\n          role,\\n          phone\\n        )\\n      \").order('created_at', {\n            ascending: false\n        });\n        if (ticketsError) {\n            console.error('Error fetching support tickets:', ticketsError);\n            return [];\n        }\n        // Then get user emails from auth.users\n        const userIds = (tickets === null || tickets === void 0 ? void 0 : tickets.map((ticket)=>ticket.user_id).filter(Boolean)) || [];\n        if (userIds.length === 0) {\n            return tickets || [];\n        }\n        const { data: authUsers, error: authError } = await supabase.auth.admin.listUsers();\n        if (authError) {\n            console.error('Error fetching auth users:', authError);\n            return tickets || [];\n        }\n        // Combine the data\n        const ticketsWithEmails = (tickets === null || tickets === void 0 ? void 0 : tickets.map((ticket)=>{\n            var _authUsers_users_find;\n            return {\n                ...ticket,\n                user_email: ((_authUsers_users_find = authUsers.users.find((user)=>user.id === ticket.user_id)) === null || _authUsers_users_find === void 0 ? void 0 : _authUsers_users_find.email) || 'No email'\n            };\n        })) || [];\n        return ticketsWithEmails;\n    } catch (error) {\n        console.error('Error in fetchAllSupportTicketsForAdmin:', error);\n        return [];\n    }\n};\n// Fetch tickets by user type (for admin dashboard)\nconst fetchSupportTicketsByType = async (userType)=>{\n    const { data, error } = await supabase.from('support_tickets').select(\"\\n      *,\\n      profiles(\\n        full_name,\\n        role,\\n        phone\\n      )\\n    \").eq('user_type', userType).order('created_at', {\n        ascending: false\n    });\n    if (error) {\n        console.error(\"Error fetching \".concat(userType, \" support tickets:\"), error);\n        return [];\n    }\n    return data || [];\n};\n// Legacy functions for backward compatibility\nconst submitStudentMessage = async (message)=>{\n    return await submitSupportTicket({\n        user_id: message.user_id,\n        title: message.subject,\n        description: message.content,\n        category: message.user_type,\n        priority: message.priority,\n        status: message.status\n    });\n};\nconst fetchStudentMessages = async ()=>{\n    try {\n        console.log('Fetching student messages...');\n        const tickets = await fetchSupportTickets();\n        console.log('Successfully fetched tickets:', tickets.length);\n        return tickets;\n    } catch (error) {\n        console.error('Error in fetchStudentMessages:', error);\n        return [];\n    }\n};\n// Chat System Functions\nconst createChatConversation = async (conversation)=>{\n    // Get user role if user_type not provided\n    let userType = conversation.user_type;\n    if (!userType) {\n        const { data: profile } = await supabase.from('profiles').select('role').eq('id', conversation.user_id).single();\n        if ((profile === null || profile === void 0 ? void 0 : profile.role) === 'cafeteria_manager') {\n            userType = 'cafeteria';\n        } else if ((profile === null || profile === void 0 ? void 0 : profile.role) === 'admin') {\n            userType = 'admin';\n        } else {\n            userType = 'student';\n        }\n    }\n    const { data, error } = await supabase.from('chat_conversations').insert([\n        {\n            user_id: conversation.user_id,\n            subject: conversation.subject,\n            category: conversation.category || 'general_inquiry',\n            priority: conversation.priority || 'medium',\n            user_type: userType,\n            order_id: conversation.order_id || null,\n            ticket_id: conversation.ticket_id || null,\n            status: 'open'\n        }\n    ]).select('id').single();\n    if (error) {\n        console.error('Error creating chat conversation:', error);\n        return null;\n    }\n    return (data === null || data === void 0 ? void 0 : data.id) || null;\n};\nconst sendChatMessage = async (message)=>{\n    const { error } = await supabase.from('chat_messages').insert([\n        {\n            conversation_id: message.conversation_id,\n            sender_id: message.sender_id,\n            content: message.content,\n            message_type: message.message_type || 'text',\n            file_url: message.file_url || null,\n            file_name: message.file_name || null,\n            file_size: message.file_size || null,\n            is_read: false\n        }\n    ]);\n    if (error) {\n        console.error('Error sending chat message:', error);\n        return false;\n    }\n    // Update conversation updated_at\n    await supabase.from('chat_conversations').update({\n        updated_at: new Date().toISOString()\n    }).eq('id', message.conversation_id);\n    return true;\n};\nconst getChatConversations = async (userId, userType)=>{\n    let query = supabase.from('chat_conversations').select(\"\\n      *,\\n      profiles!chat_conversations_user_id_fkey(\\n        full_name,\\n        email,\\n        role\\n      ),\\n      support_agent:profiles!chat_conversations_support_agent_id_fkey(\\n        full_name,\\n        email\\n      )\\n    \").order('updated_at', {\n        ascending: false\n    });\n    if (userId) {\n        query = query.eq('user_id', userId);\n    }\n    if (userType) {\n        query = query.eq('user_type', userType);\n    }\n    const { data, error } = await query;\n    if (error) {\n        console.error('Error fetching chat conversations:', error);\n        return [];\n    }\n    return data || [];\n};\nconst getChatMessages = async (conversationId)=>{\n    const { data, error } = await supabase.from('chat_messages').select(\"\\n      *,\\n      sender:profiles!chat_messages_sender_id_fkey(\\n        full_name,\\n        email,\\n        role\\n      )\\n    \").eq('conversation_id', conversationId).order('created_at', {\n        ascending: true\n    });\n    if (error) {\n        console.error('Error fetching chat messages:', error);\n        return [];\n    }\n    return data || [];\n};\nconst markMessagesAsRead = async (conversationId, userId)=>{\n    const { error } = await supabase.from('chat_messages').update({\n        is_read: true\n    }).eq('conversation_id', conversationId).neq('sender_id', userId);\n    if (error) {\n        console.error('Error marking messages as read:', error);\n        return false;\n    }\n    return true;\n};\nconst assignChatToAgent = async (conversationId, agentId)=>{\n    const { error } = await supabase.from('chat_conversations').update({\n        support_agent_id: agentId,\n        status: 'in_progress',\n        updated_at: new Date().toISOString()\n    }).eq('id', conversationId);\n    if (error) {\n        console.error('Error assigning chat to agent:', error);\n        return false;\n    }\n    return true;\n};\nconst closeChatConversation = async (conversationId, rating, feedback)=>{\n    const updateData = {\n        status: 'closed',\n        closed_at: new Date().toISOString(),\n        updated_at: new Date().toISOString()\n    };\n    if (rating) updateData.rating = rating;\n    if (feedback) updateData.feedback = feedback;\n    const { error } = await supabase.from('chat_conversations').update(updateData).eq('id', conversationId);\n    if (error) {\n        console.error('Error closing chat conversation:', error);\n        return false;\n    }\n    return true;\n};\n// Convert support ticket to chat conversation\nconst convertTicketToChat = async (ticketId)=>{\n    // Get ticket details\n    const { data: ticket, error: ticketError } = await supabase.from('support_tickets').select('*').eq('id', ticketId).single();\n    if (ticketError || !ticket) {\n        console.error('Error fetching ticket for conversion:', ticketError);\n        return null;\n    }\n    // Create chat conversation linked to ticket\n    const conversationId = await createChatConversation({\n        user_id: ticket.user_id,\n        subject: ticket.title,\n        category: ticket.category,\n        priority: ticket.priority,\n        user_type: ticket.user_type,\n        order_id: ticket.order_id,\n        ticket_id: ticketId\n    });\n    if (conversationId) {\n        // Add initial message with ticket description\n        await sendChatMessage({\n            conversation_id: conversationId,\n            sender_id: ticket.user_id,\n            content: \"Original ticket: \".concat(ticket.description),\n            message_type: 'text'\n        });\n        // Update ticket status to indicate it has a chat\n        await supabase.from('support_tickets').update({\n            status: 'in_progress',\n            updated_at: new Date().toISOString()\n        }).eq('id', ticketId);\n    }\n    return conversationId;\n};\n// Get chat conversation for a ticket\nconst getChatForTicket = async (ticketId)=>{\n    const { data, error } = await supabase.from('chat_conversations').select(\"\\n      *,\\n      profiles!chat_conversations_user_id_fkey(\\n        full_name,\\n        email,\\n        role\\n      ),\\n      support_agent:profiles!chat_conversations_support_agent_id_fkey(\\n        full_name,\\n        email\\n      )\\n    \").eq('ticket_id', ticketId).single();\n    if (error) {\n        console.error('Error fetching chat for ticket:', error);\n        return null;\n    }\n    return data;\n};\n// Orders functions\nconst getOrders = async (cafeteriaId)=>{\n    let query = supabase.from('orders').select('*').order('created_at', {\n        ascending: false\n    });\n    if (cafeteriaId) {\n        query = query.eq('cafeteria_id', cafeteriaId);\n    }\n    const { data, error } = await query;\n    if (error) {\n        console.error('Error fetching orders:', error);\n        return [];\n    }\n    return data || [];\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/supabase.ts\n"));

/***/ })

});