{"version": 3, "sources": ["../../../../../../src/build/webpack/plugins/minify-webpack-plugin/src/index.ts"], "sourcesContent": ["import {\n  webpack,\n  ModuleFilenameHelpers,\n  sources,\n  WebpackError,\n  type CacheFacade,\n  type Compilation,\n} from 'next/dist/compiled/webpack/webpack'\nimport pLimit from 'next/dist/compiled/p-limit'\nimport { getCompilationSpan } from '../../../utils'\n\nfunction buildError(error: any, file: string) {\n  if (error.line) {\n    return new WebpackError(\n      `${file} from Minifier\\n${error.message} [${file}:${error.line},${\n        error.col\n      }]${\n        error.stack ? `\\n${error.stack.split('\\n').slice(1).join('\\n')}` : ''\n      }`\n    )\n  }\n\n  if (error.stack) {\n    return new WebpackError(\n      `${file} from Minifier\\n${error.message}\\n${error.stack}`\n    )\n  }\n\n  return new WebpackError(`${file} from Minifier\\n${error.message}`)\n}\n\nconst debugMinify = process.env.NEXT_DEBUG_MINIFY\n\nexport class MinifyPlugin {\n  constructor(private options: { noMangling?: boolean }) {}\n\n  async optimize(\n    compiler: any,\n    compilation: Compilation,\n    assets: any,\n    cache: CacheFacade,\n    {\n      SourceMapSource,\n      RawSource,\n    }: {\n      SourceMapSource: typeof sources.SourceMapSource\n      RawSource: typeof sources.RawSource\n    }\n  ) {\n    const mangle = !this.options.noMangling\n    const compilationSpan =\n      getCompilationSpan(compilation)! || getCompilationSpan(compiler)\n\n    const MinifierSpan = compilationSpan.traceChild(\n      'minify-webpack-plugin-optimize'\n    )\n\n    if (compilation.name) {\n      MinifierSpan.setAttribute('compilationName', compilation.name)\n    }\n\n    MinifierSpan.setAttribute('mangle', String(mangle))\n\n    return MinifierSpan.traceAsyncFn(async () => {\n      const assetsList = Object.keys(assets)\n\n      const assetsForMinify = await Promise.all(\n        assetsList\n          .filter((name) => {\n            if (\n              !ModuleFilenameHelpers.matchObject.bind(\n                // eslint-disable-next-line no-undefined\n                undefined,\n                { test: /\\.[cm]?js(\\?.*)?$/i }\n              )(name)\n            ) {\n              return false\n            }\n\n            const res = compilation.getAsset(name)\n            if (!res) {\n              console.log(name)\n              return false\n            }\n\n            const { info } = res\n\n            // Skip double minimize assets from child compilation\n            if (info.minimized) {\n              return false\n            }\n\n            return true\n          })\n          .map(async (name) => {\n            const { info, source } = compilation.getAsset(name)!\n\n            const eTag = cache.mergeEtags(\n              cache.getLazyHashedEtag(source),\n              JSON.stringify(this.options)\n            )\n\n            const output = await cache.getPromise<\n              { source: sources.Source } | undefined\n            >(name, eTag)\n\n            if (debugMinify && debugMinify === '1') {\n              console.log(\n                JSON.stringify({ name, source: source.source().toString() }),\n                { breakLength: Infinity, maxStringLength: Infinity }\n              )\n            }\n            return { name, info, inputSource: source, output, eTag }\n          })\n      )\n\n      let initializedWorker: any\n\n      // eslint-disable-next-line consistent-return\n      const getWorker = () => {\n        return {\n          minify: async (options: {\n            input: string\n            inputSourceMap: Object\n          }) => {\n            const result = await (\n              require('../../../../swc') as typeof import('../../../../swc')\n            ).minify(options.input, {\n              ...(options.inputSourceMap\n                ? {\n                    sourceMap: {\n                      content: JSON.stringify(options.inputSourceMap),\n                    },\n                  }\n                : {}),\n              // Compress options are defined in crates/napi/src/minify.rs.\n              compress: false,\n              // Mangle options may be amended in crates/napi/src/minify.rs.\n              mangle,\n              module: 'unknown',\n              output: {\n                comments: false,\n              },\n            })\n\n            return result\n          },\n        }\n      }\n\n      // The limit in the SWC minifier will be handled by Node.js\n      const limit = pLimit(Infinity)\n      const scheduledTasks = []\n\n      for (const asset of assetsForMinify) {\n        scheduledTasks.push(\n          limit(async () => {\n            const { name, inputSource, eTag } = asset\n            let { output } = asset\n\n            const minifySpan = MinifierSpan.traceChild('minify-js')\n            minifySpan.setAttribute('name', name)\n            minifySpan.setAttribute(\n              'cache',\n              typeof output === 'undefined' ? 'MISS' : 'HIT'\n            )\n\n            return minifySpan.traceAsyncFn(async () => {\n              if (!output) {\n                const { source: sourceFromInputSource, map: inputSourceMap } =\n                  inputSource.sourceAndMap()\n\n                const input = Buffer.isBuffer(sourceFromInputSource)\n                  ? sourceFromInputSource.toString()\n                  : sourceFromInputSource\n\n                let minifiedOutput: { code: string; map: any } | undefined\n\n                try {\n                  minifiedOutput = await getWorker().minify({\n                    input,\n                    inputSourceMap,\n                  })\n                } catch (error) {\n                  compilation.errors.push(buildError(error, name))\n\n                  return\n                }\n\n                const source = minifiedOutput.map\n                  ? new SourceMapSource(\n                      minifiedOutput.code,\n                      name,\n                      minifiedOutput.map,\n                      input,\n                      inputSourceMap,\n                      true\n                    )\n                  : new RawSource(minifiedOutput.code)\n\n                await cache.storePromise(name, eTag, { source })\n\n                output = { source }\n              }\n\n              const newInfo = { minimized: true }\n\n              compilation.updateAsset(name, output.source, newInfo)\n            })\n          })\n        )\n      }\n\n      await Promise.all(scheduledTasks)\n\n      if (initializedWorker) {\n        await initializedWorker.end()\n      }\n    })\n  }\n\n  apply(compiler: any) {\n    const { SourceMapSource, RawSource } = (compiler?.webpack?.sources ||\n      sources) as typeof sources\n\n    const pluginName = this.constructor.name\n\n    compiler.hooks.thisCompilation.tap(\n      pluginName,\n      (compilation: Compilation) => {\n        const cache = compilation.getCache('MinifierWebpackPlugin')\n\n        const handleHashForChunk = (hash: any, _chunk: any) => {\n          // increment 'c' to invalidate cache\n          hash.update('c')\n        }\n\n        const JSModulesHooks =\n          webpack.javascript.JavascriptModulesPlugin.getCompilationHooks(\n            compilation\n          )\n        JSModulesHooks.chunkHash.tap(pluginName, (chunk, hash) => {\n          if (!chunk.hasRuntime()) return\n          return handleHashForChunk(hash, chunk)\n        })\n\n        compilation.hooks.processAssets.tapPromise(\n          {\n            name: pluginName,\n            stage: webpack.Compilation.PROCESS_ASSETS_STAGE_OPTIMIZE_SIZE,\n          },\n          (assets: any) =>\n            this.optimize(compiler, compilation, assets, cache, {\n              SourceMapSource,\n              RawSource,\n            })\n        )\n\n        compilation.hooks.statsPrinter.tap(pluginName, (stats: any) => {\n          stats.hooks.print\n            .for('asset.info.minimized')\n            .tap(\n              'minify-webpack-plugin',\n              (minimized: any, { green, formatFlag }: any) =>\n                // eslint-disable-next-line no-undefined\n                minimized ? green(formatFlag('minimized')) : undefined\n            )\n        })\n      }\n    )\n  }\n}\n"], "names": ["MinifyPlugin", "buildError", "error", "file", "line", "WebpackError", "message", "col", "stack", "split", "slice", "join", "debugMinify", "process", "env", "NEXT_DEBUG_MINIFY", "constructor", "options", "optimize", "compiler", "compilation", "assets", "cache", "SourceMapSource", "RawSource", "mangle", "noMangling", "compilationSpan", "getCompilationSpan", "MinifierSpan", "<PERSON><PERSON><PERSON><PERSON>", "name", "setAttribute", "String", "traceAsyncFn", "assetsList", "Object", "keys", "assetsForMinify", "Promise", "all", "filter", "ModuleFilenameHelpers", "matchObject", "bind", "undefined", "test", "res", "getAsset", "console", "log", "info", "minimized", "map", "source", "eTag", "mergeEtags", "getLazyHashedEtag", "JSON", "stringify", "output", "getPromise", "toString", "<PERSON><PERSON><PERSON><PERSON>", "Infinity", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "inputSource", "initializedWorker", "getWorker", "minify", "result", "require", "input", "inputSourceMap", "sourceMap", "content", "compress", "module", "comments", "limit", "pLimit", "scheduledTasks", "asset", "push", "minifySpan", "sourceFromInputSource", "sourceAndMap", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "minifiedOutput", "errors", "code", "storePromise", "newInfo", "updateAsset", "end", "apply", "webpack", "sources", "pluginName", "hooks", "thisCompilation", "tap", "getCache", "handleHashForChunk", "hash", "_chunk", "update", "JSModulesHooks", "javascript", "JavascriptModulesPlugin", "getCompilationHooks", "chunkHash", "chunk", "hasRuntime", "processAssets", "tapPromise", "stage", "Compilation", "PROCESS_ASSETS_STAGE_OPTIMIZE_SIZE", "statsPrinter", "stats", "print", "for", "green", "formatFlag"], "mappings": ";;;;+BAiCaA;;;eAAAA;;;yBA1BN;+DACY;uBACgB;;;;;;AAEnC,SAASC,WAAWC,KAAU,EAAEC,IAAY;IAC1C,IAAID,MAAME,IAAI,EAAE;QACd,OAAO,IAAIC,qBAAY,CACrB,GAAGF,KAAK,gBAAgB,EAAED,MAAMI,OAAO,CAAC,EAAE,EAAEH,KAAK,CAAC,EAAED,MAAME,IAAI,CAAC,CAAC,EAC9DF,MAAMK,GAAG,CACV,CAAC,EACAL,MAAMM,KAAK,GAAG,CAAC,EAAE,EAAEN,MAAMM,KAAK,CAACC,KAAK,CAAC,MAAMC,KAAK,CAAC,GAAGC,IAAI,CAAC,OAAO,GAAG,IACnE;IAEN;IAEA,IAAIT,MAAMM,KAAK,EAAE;QACf,OAAO,IAAIH,qBAAY,CACrB,GAAGF,KAAK,gBAAgB,EAAED,MAAMI,OAAO,CAAC,EAAE,EAAEJ,MAAMM,KAAK,EAAE;IAE7D;IAEA,OAAO,IAAIH,qBAAY,CAAC,GAAGF,KAAK,gBAAgB,EAAED,MAAMI,OAAO,EAAE;AACnE;AAEA,MAAMM,cAAcC,QAAQC,GAAG,CAACC,iBAAiB;AAE1C,MAAMf;IACXgB,YAAY,AAAQC,OAAiC,CAAE;aAAnCA,UAAAA;IAAoC;IAExD,MAAMC,SACJC,QAAa,EACbC,WAAwB,EACxBC,MAAW,EACXC,KAAkB,EAClB,EACEC,eAAe,EACfC,SAAS,EAIV,EACD;QACA,MAAMC,SAAS,CAAC,IAAI,CAACR,OAAO,CAACS,UAAU;QACvC,MAAMC,kBACJC,IAAAA,yBAAkB,EAACR,gBAAiBQ,IAAAA,yBAAkB,EAACT;QAEzD,MAAMU,eAAeF,gBAAgBG,UAAU,CAC7C;QAGF,IAAIV,YAAYW,IAAI,EAAE;YACpBF,aAAaG,YAAY,CAAC,mBAAmBZ,YAAYW,IAAI;QAC/D;QAEAF,aAAaG,YAAY,CAAC,UAAUC,OAAOR;QAE3C,OAAOI,aAAaK,YAAY,CAAC;YAC/B,MAAMC,aAAaC,OAAOC,IAAI,CAAChB;YAE/B,MAAMiB,kBAAkB,MAAMC,QAAQC,GAAG,CACvCL,WACGM,MAAM,CAAC,CAACV;gBACP,IACE,CAACW,8BAAqB,CAACC,WAAW,CAACC,IAAI,CACrC,wCAAwC;gBACxCC,WACA;oBAAEC,MAAM;gBAAqB,GAC7Bf,OACF;oBACA,OAAO;gBACT;gBAEA,MAAMgB,MAAM3B,YAAY4B,QAAQ,CAACjB;gBACjC,IAAI,CAACgB,KAAK;oBACRE,QAAQC,GAAG,CAACnB;oBACZ,OAAO;gBACT;gBAEA,MAAM,EAAEoB,IAAI,EAAE,GAAGJ;gBAEjB,qDAAqD;gBACrD,IAAII,KAAKC,SAAS,EAAE;oBAClB,OAAO;gBACT;gBAEA,OAAO;YACT,GACCC,GAAG,CAAC,OAAOtB;gBACV,MAAM,EAAEoB,IAAI,EAAEG,MAAM,EAAE,GAAGlC,YAAY4B,QAAQ,CAACjB;gBAE9C,MAAMwB,OAAOjC,MAAMkC,UAAU,CAC3BlC,MAAMmC,iBAAiB,CAACH,SACxBI,KAAKC,SAAS,CAAC,IAAI,CAAC1C,OAAO;gBAG7B,MAAM2C,SAAS,MAAMtC,MAAMuC,UAAU,CAEnC9B,MAAMwB;gBAER,IAAI3C,eAAeA,gBAAgB,KAAK;oBACtCqC,QAAQC,GAAG,CACTQ,KAAKC,SAAS,CAAC;wBAAE5B;wBAAMuB,QAAQA,OAAOA,MAAM,GAAGQ,QAAQ;oBAAG,IAC1D;wBAAEC,aAAaC;wBAAUC,iBAAiBD;oBAAS;gBAEvD;gBACA,OAAO;oBAAEjC;oBAAMoB;oBAAMe,aAAaZ;oBAAQM;oBAAQL;gBAAK;YACzD;YAGJ,IAAIY;YAEJ,6CAA6C;YAC7C,MAAMC,YAAY;gBAChB,OAAO;oBACLC,QAAQ,OAAOpD;wBAIb,MAAMqD,SAAS,MAAM,AACnBC,QAAQ,mBACRF,MAAM,CAACpD,QAAQuD,KAAK,EAAE;4BACtB,GAAIvD,QAAQwD,cAAc,GACtB;gCACEC,WAAW;oCACTC,SAASjB,KAAKC,SAAS,CAAC1C,QAAQwD,cAAc;gCAChD;4BACF,IACA,CAAC,CAAC;4BACN,6DAA6D;4BAC7DG,UAAU;4BACV,8DAA8D;4BAC9DnD;4BACAoD,QAAQ;4BACRjB,QAAQ;gCACNkB,UAAU;4BACZ;wBACF;wBAEA,OAAOR;oBACT;gBACF;YACF;YAEA,2DAA2D;YAC3D,MAAMS,QAAQC,IAAAA,eAAM,EAAChB;YACrB,MAAMiB,iBAAiB,EAAE;YAEzB,KAAK,MAAMC,SAAS5C,gBAAiB;gBACnC2C,eAAeE,IAAI,CACjBJ,MAAM;oBACJ,MAAM,EAAEhD,IAAI,EAAEmC,WAAW,EAAEX,IAAI,EAAE,GAAG2B;oBACpC,IAAI,EAAEtB,MAAM,EAAE,GAAGsB;oBAEjB,MAAME,aAAavD,aAAaC,UAAU,CAAC;oBAC3CsD,WAAWpD,YAAY,CAAC,QAAQD;oBAChCqD,WAAWpD,YAAY,CACrB,SACA,OAAO4B,WAAW,cAAc,SAAS;oBAG3C,OAAOwB,WAAWlD,YAAY,CAAC;wBAC7B,IAAI,CAAC0B,QAAQ;4BACX,MAAM,EAAEN,QAAQ+B,qBAAqB,EAAEhC,KAAKoB,cAAc,EAAE,GAC1DP,YAAYoB,YAAY;4BAE1B,MAAMd,QAAQe,OAAOC,QAAQ,CAACH,yBAC1BA,sBAAsBvB,QAAQ,KAC9BuB;4BAEJ,IAAII;4BAEJ,IAAI;gCACFA,iBAAiB,MAAMrB,YAAYC,MAAM,CAAC;oCACxCG;oCACAC;gCACF;4BACF,EAAE,OAAOvE,OAAO;gCACdkB,YAAYsE,MAAM,CAACP,IAAI,CAAClF,WAAWC,OAAO6B;gCAE1C;4BACF;4BAEA,MAAMuB,SAASmC,eAAepC,GAAG,GAC7B,IAAI9B,gBACFkE,eAAeE,IAAI,EACnB5D,MACA0D,eAAepC,GAAG,EAClBmB,OACAC,gBACA,QAEF,IAAIjD,UAAUiE,eAAeE,IAAI;4BAErC,MAAMrE,MAAMsE,YAAY,CAAC7D,MAAMwB,MAAM;gCAAED;4BAAO;4BAE9CM,SAAS;gCAAEN;4BAAO;wBACpB;wBAEA,MAAMuC,UAAU;4BAAEzC,WAAW;wBAAK;wBAElChC,YAAY0E,WAAW,CAAC/D,MAAM6B,OAAON,MAAM,EAAEuC;oBAC/C;gBACF;YAEJ;YAEA,MAAMtD,QAAQC,GAAG,CAACyC;YAElB,IAAId,mBAAmB;gBACrB,MAAMA,kBAAkB4B,GAAG;YAC7B;QACF;IACF;IAEAC,MAAM7E,QAAa,EAAE;YACqBA;QAAxC,MAAM,EAAEI,eAAe,EAAEC,SAAS,EAAE,GAAIL,CAAAA,6BAAAA,oBAAAA,SAAU8E,OAAO,qBAAjB9E,kBAAmB+E,OAAO,KAChEA,gBAAO;QAET,MAAMC,aAAa,IAAI,CAACnF,WAAW,CAACe,IAAI;QAExCZ,SAASiF,KAAK,CAACC,eAAe,CAACC,GAAG,CAChCH,YACA,CAAC/E;YACC,MAAME,QAAQF,YAAYmF,QAAQ,CAAC;YAEnC,MAAMC,qBAAqB,CAACC,MAAWC;gBACrC,oCAAoC;gBACpCD,KAAKE,MAAM,CAAC;YACd;YAEA,MAAMC,iBACJX,gBAAO,CAACY,UAAU,CAACC,uBAAuB,CAACC,mBAAmB,CAC5D3F;YAEJwF,eAAeI,SAAS,CAACV,GAAG,CAACH,YAAY,CAACc,OAAOR;gBAC/C,IAAI,CAACQ,MAAMC,UAAU,IAAI;gBACzB,OAAOV,mBAAmBC,MAAMQ;YAClC;YAEA7F,YAAYgF,KAAK,CAACe,aAAa,CAACC,UAAU,CACxC;gBACErF,MAAMoE;gBACNkB,OAAOpB,gBAAO,CAACqB,WAAW,CAACC,kCAAkC;YAC/D,GACA,CAAClG,SACC,IAAI,CAACH,QAAQ,CAACC,UAAUC,aAAaC,QAAQC,OAAO;oBAClDC;oBACAC;gBACF;YAGJJ,YAAYgF,KAAK,CAACoB,YAAY,CAAClB,GAAG,CAACH,YAAY,CAACsB;gBAC9CA,MAAMrB,KAAK,CAACsB,KAAK,CACdC,GAAG,CAAC,wBACJrB,GAAG,CACF,yBACA,CAAClD,WAAgB,EAAEwE,KAAK,EAAEC,UAAU,EAAO,GACzC,wCAAwC;oBACxCzE,YAAYwE,MAAMC,WAAW,gBAAgBhF;YAErD;QACF;IAEJ;AACF"}