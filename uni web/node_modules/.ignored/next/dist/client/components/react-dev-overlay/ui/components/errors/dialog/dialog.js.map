{"version": 3, "sources": ["../../../../../../../../src/client/components/react-dev-overlay/ui/components/errors/dialog/dialog.tsx"], "sourcesContent": ["import { Dialog } from '../../dialog/dialog'\n\ntype ErrorOverlayDialogProps = {\n  children?: React.ReactNode\n  onClose?: () => void\n  dialogResizerRef?: React.RefObject<HTMLDivElement | null>\n}\n\nexport function ErrorOverlayDialog({\n  children,\n  onClose,\n  ...props\n}: ErrorOverlayDialogProps) {\n  return (\n    <Dialog\n      type=\"error\"\n      aria-labelledby=\"nextjs__container_errors_label\"\n      aria-describedby=\"nextjs__container_errors_desc\"\n      onClose={onClose}\n      className=\"error-overlay-dialog\"\n      {...props}\n    >\n      {children}\n    </Dialog>\n  )\n}\n\nexport const DIALOG_STYLES = `\n  .error-overlay-dialog {\n    overflow-y: auto;\n    -webkit-font-smoothing: antialiased;\n    background: var(--color-background-100);\n    background-clip: padding-box;\n    border: 1px solid var(--color-gray-400);\n    border-radius: var(--rounded-xl);\n    box-shadow: var(--shadow-menu);\n    position: relative;\n\n    &:has(\n        ~ [data-nextjs-error-overlay-nav] .error-overlay-notch[data-side='left']\n      ) {\n      border-top-left-radius: 0;\n    }\n\n    &:has(\n        ~ [data-nextjs-error-overlay-nav]\n          .error-overlay-notch[data-side='right']\n      ) {\n      border-top-right-radius: 0;\n    }\n  }\n`\n"], "names": ["DIALOG_STYLES", "ErrorOverlayDialog", "children", "onClose", "props", "Dialog", "type", "aria-<PERSON>by", "aria-<PERSON><PERSON>", "className"], "mappings": ";;;;;;;;;;;;;;;IA2BaA,aAAa;eAAbA;;IAnBGC,kBAAkB;eAAlBA;;;;wBARO;AAQhB,SAASA,mBAAmB,KAIT;IAJS,IAAA,EACjCC,QAAQ,EACRC,OAAO,EACP,GAAGC,OACqB,GAJS;IAKjC,qBACE,qBAACC,cAAM;QACLC,MAAK;QACLC,mBAAgB;QAChBC,oBAAiB;QACjBL,SAASA;QACTM,WAAU;QACT,GAAGL,KAAK;kBAERF;;AAGP;AAEO,MAAMF,gBAAiB"}