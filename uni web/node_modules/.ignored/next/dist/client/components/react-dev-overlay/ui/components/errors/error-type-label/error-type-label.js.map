{"version": 3, "sources": ["../../../../../../../../src/client/components/react-dev-overlay/ui/components/errors/error-type-label/error-type-label.tsx"], "sourcesContent": ["export type ErrorType =\n  | 'Build Error'\n  | 'Runtime Error'\n  | 'Console Error'\n  | 'Unhandled Runtime Error'\n  | 'Missing Required HTML Tag'\n\ntype ErrorTypeLabelProps = {\n  errorType: ErrorType\n}\n\nexport function ErrorTypeLabel({ errorType }: ErrorTypeLabelProps) {\n  return (\n    <span\n      id=\"nextjs__container_errors_label\"\n      className=\"nextjs__container_errors_label\"\n    >\n      {errorType}\n    </span>\n  )\n}\n\nexport const styles = `\n  .nextjs__container_errors_label {\n    padding: 2px 6px;\n    margin: 0;\n    border-radius: var(--rounded-md-2);\n    background: var(--color-red-100);\n    font-weight: 600;\n    font-size: var(--size-12);\n    color: var(--color-red-900);\n    font-family: var(--font-stack-monospace);\n    line-height: var(--size-20);\n  }\n`\n"], "names": ["ErrorTypeLabel", "styles", "errorType", "span", "id", "className"], "mappings": ";;;;;;;;;;;;;;;IAWgBA,cAAc;eAAdA;;IAWHC,MAAM;eAANA;;;;AAXN,SAASD,eAAe,KAAkC;IAAlC,IAAA,EAAEE,SAAS,EAAuB,GAAlC;IAC7B,qBACE,qBAACC;QACCC,IAAG;QACHC,WAAU;kBAETH;;AAGP;AAEO,MAAMD,SAAU"}