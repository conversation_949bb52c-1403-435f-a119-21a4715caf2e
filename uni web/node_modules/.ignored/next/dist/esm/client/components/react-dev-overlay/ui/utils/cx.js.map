{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/ui/utils/cx.ts"], "sourcesContent": ["/**\n * Merge multiple args to a single string with spaces. Useful for merging class names.\n * @example\n * cx('foo', 'bar') // 'foo bar'\n * cx('foo', null, 'bar', undefined, 'baz', false) // 'foo bar baz'\n */\nexport function cx(...args: (string | undefined | null | false)[]): string {\n  return args.filter(Boolean).join(' ')\n}\n"], "names": ["cx", "args", "filter", "Boolean", "join"], "mappings": "AAAA;;;;;CAKC,GACD,OAAO,SAASA;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAGC,OAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAGA,KAAH,QAAA,SAAA,CAAA,KAA8C;;IAC/D,OAAOA,KAAKC,MAAM,CAACC,SAASC,IAAI,CAAC;AACnC"}