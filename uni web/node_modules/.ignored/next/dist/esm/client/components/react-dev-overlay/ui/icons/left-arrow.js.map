{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/ui/icons/left-arrow.tsx"], "sourcesContent": ["export function LeftArrow({\n  title,\n  className,\n}: {\n  title?: string\n  className?: string\n}) {\n  return (\n    <svg\n      width=\"16\"\n      height=\"16\"\n      viewBox=\"0 0 16 16\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      aria-label={title}\n      className={className}\n    >\n      <path\n        fillRule=\"evenodd\"\n        clipRule=\"evenodd\"\n        d=\"M9.24996 12.0608L8.71963 11.5304L5.89641 8.70722C5.50588 8.3167 5.50588 7.68353 5.89641 7.29301L8.71963 4.46978L9.24996 3.93945L10.3106 5.00011L9.78029 5.53044L7.31062 8.00011L9.78029 10.4698L10.3106 11.0001L9.24996 12.0608Z\"\n        fill=\"currentColor\"\n      />\n    </svg>\n  )\n}\n"], "names": ["LeftArrow", "title", "className", "svg", "width", "height", "viewBox", "fill", "xmlns", "aria-label", "path", "fillRule", "clipRule", "d"], "mappings": ";AAAA,OAAO,SAASA,UAAU,KAMzB;IANyB,IAAA,EACxBC,KAAK,EACLC,SAAS,EAIV,GANyB;IAOxB,qBACE,KAACC;QACCC,OAAM;QACNC,QAAO;QACPC,SAAQ;QACRC,MAAK;QACLC,OAAM;QACNC,cAAYR;QACZC,WAAWA;kBAEX,cAAA,KAACQ;YACCC,UAAS;YACTC,UAAS;YACTC,GAAE;YACFN,MAAK;;;AAIb"}