{"version": 3, "sources": ["../../../../../src/build/webpack/loaders/next-image-loader/blur.ts"], "sourcesContent": ["import isAnimated from 'next/dist/compiled/is-animated'\nimport { optimizeImage } from '../../../../server/image-optimizer'\n\nconst BLUR_IMG_SIZE = 8\nconst BLUR_QUALITY = 70\nconst VALID_BLUR_EXT = ['jpeg', 'png', 'webp', 'avif'] // should match other usages\n\nexport async function getBlurImage(\n  content: Buffer,\n  extension: string,\n  imageSize: { width: number; height: number },\n  {\n    basePath,\n    outputPath,\n    isDev,\n    tracing = () => ({\n      traceFn:\n        (fn) =>\n        (...args: any) =>\n          fn(...args),\n      traceAsyncFn:\n        (fn) =>\n        (...args: any) =>\n          fn(...args),\n    }),\n  }: {\n    basePath: string\n    outputPath: string\n    isDev: boolean\n    tracing: (name?: string) => {\n      traceFn(fn: Function): any\n      traceAsyncFn(fn: Function): any\n    }\n  }\n) {\n  let blurDataURL: string | undefined\n  let blurWidth: number = 0\n  let blurHeight: number = 0\n\n  if (VALID_BLUR_EXT.includes(extension) && !isAnimated(content)) {\n    // Shrink the image's largest dimension\n    if (imageSize.width >= imageSize.height) {\n      blurWidth = BLUR_IMG_SIZE\n      blurHeight = Math.max(\n        Math.round((imageSize.height / imageSize.width) * BLUR_IMG_SIZE),\n        1\n      )\n    } else {\n      blurWidth = Math.max(\n        Math.round((imageSize.width / imageSize.height) * BLUR_IMG_SIZE),\n        1\n      )\n      blurHeight = BLUR_IMG_SIZE\n    }\n\n    if (isDev) {\n      // During `next dev`, we don't want to generate blur placeholders with webpack\n      // because it can delay starting the dev server. Instead, we inline a\n      // special url to lazily generate the blur placeholder at request time.\n      const prefix = 'http://localhost'\n      const url = new URL(`${basePath || ''}/_next/image`, prefix)\n      url.searchParams.set('url', outputPath)\n      url.searchParams.set('w', String(blurWidth))\n      url.searchParams.set('q', String(BLUR_QUALITY))\n      blurDataURL = url.href.slice(prefix.length)\n    } else {\n      const resizeImageSpan = tracing('image-resize')\n      const resizedImage = await resizeImageSpan.traceAsyncFn(() =>\n        optimizeImage({\n          buffer: content,\n          width: blurWidth,\n          height: blurHeight,\n          contentType: `image/${extension}`,\n          quality: BLUR_QUALITY,\n        })\n      )\n      const blurDataURLSpan = tracing('image-base64-tostring')\n      blurDataURL = blurDataURLSpan.traceFn(\n        () =>\n          `data:image/${extension};base64,${resizedImage.toString('base64')}`\n      )\n    }\n  }\n  return {\n    dataURL: blurDataURL,\n    width: blurWidth,\n    height: blurHeight,\n  }\n}\n"], "names": ["isAnimated", "optimizeImage", "BLUR_IMG_SIZE", "BLUR_QUALITY", "VALID_BLUR_EXT", "getBlurImage", "content", "extension", "imageSize", "basePath", "outputPath", "isDev", "tracing", "traceFn", "fn", "args", "traceAsyncFn", "blurDataURL", "blur<PERSON>idth", "blurHeight", "includes", "width", "height", "Math", "max", "round", "prefix", "url", "URL", "searchParams", "set", "String", "href", "slice", "length", "resizeImageSpan", "resizedImage", "buffer", "contentType", "quality", "blurDataURLSpan", "toString", "dataURL"], "mappings": "AAAA,OAAOA,gBAAgB,iCAAgC;AACvD,SAASC,aAAa,QAAQ,qCAAoC;AAElE,MAAMC,gBAAgB;AACtB,MAAMC,eAAe;AACrB,MAAMC,iBAAiB;IAAC;IAAQ;IAAO;IAAQ;CAAO,CAAC,4BAA4B;;AAEnF,OAAO,eAAeC,aACpBC,OAAe,EACfC,SAAiB,EACjBC,SAA4C,EAC5C,EACEC,QAAQ,EACRC,UAAU,EACVC,KAAK,EACLC,UAAU,IAAO,CAAA;QACfC,SACE,CAACC,KACD,CAAC,GAAGC,OACFD,MAAMC;QACVC,cACE,CAACF,KACD,CAAC,GAAGC,OACFD,MAAMC;IACZ,CAAA,CAAE,EASH;IAED,IAAIE;IACJ,IAAIC,YAAoB;IACxB,IAAIC,aAAqB;IAEzB,IAAIf,eAAegB,QAAQ,CAACb,cAAc,CAACP,WAAWM,UAAU;QAC9D,uCAAuC;QACvC,IAAIE,UAAUa,KAAK,IAAIb,UAAUc,MAAM,EAAE;YACvCJ,YAAYhB;YACZiB,aAAaI,KAAKC,GAAG,CACnBD,KAAKE,KAAK,CAAC,AAACjB,UAAUc,MAAM,GAAGd,UAAUa,KAAK,GAAInB,gBAClD;QAEJ,OAAO;YACLgB,YAAYK,KAAKC,GAAG,CAClBD,KAAKE,KAAK,CAAC,AAACjB,UAAUa,KAAK,GAAGb,UAAUc,MAAM,GAAIpB,gBAClD;YAEFiB,aAAajB;QACf;QAEA,IAAIS,OAAO;YACT,8EAA8E;YAC9E,qEAAqE;YACrE,uEAAuE;YACvE,MAAMe,SAAS;YACf,MAAMC,MAAM,IAAIC,IAAI,GAAGnB,YAAY,GAAG,YAAY,CAAC,EAAEiB;YACrDC,IAAIE,YAAY,CAACC,GAAG,CAAC,OAAOpB;YAC5BiB,IAAIE,YAAY,CAACC,GAAG,CAAC,KAAKC,OAAOb;YACjCS,IAAIE,YAAY,CAACC,GAAG,CAAC,KAAKC,OAAO5B;YACjCc,cAAcU,IAAIK,IAAI,CAACC,KAAK,CAACP,OAAOQ,MAAM;QAC5C,OAAO;YACL,MAAMC,kBAAkBvB,QAAQ;YAChC,MAAMwB,eAAe,MAAMD,gBAAgBnB,YAAY,CAAC,IACtDf,cAAc;oBACZoC,QAAQ/B;oBACRe,OAAOH;oBACPI,QAAQH;oBACRmB,aAAa,CAAC,MAAM,EAAE/B,WAAW;oBACjCgC,SAASpC;gBACX;YAEF,MAAMqC,kBAAkB5B,QAAQ;YAChCK,cAAcuB,gBAAgB3B,OAAO,CACnC,IACE,CAAC,WAAW,EAAEN,UAAU,QAAQ,EAAE6B,aAAaK,QAAQ,CAAC,WAAW;QAEzE;IACF;IACA,OAAO;QACLC,SAASzB;QACTI,OAAOH;QACPI,QAAQH;IACV;AACF"}