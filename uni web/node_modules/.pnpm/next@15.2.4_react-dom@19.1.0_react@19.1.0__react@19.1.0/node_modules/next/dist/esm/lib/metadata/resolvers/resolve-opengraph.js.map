{"version": 3, "sources": ["../../../../src/lib/metadata/resolvers/resolve-opengraph.ts"], "sourcesContent": ["import type { ResolvedMetadata } from '../types/metadata-interface'\nimport type {\n  OpenGraphType,\n  OpenGraph,\n  ResolvedOpenGraph,\n} from '../types/opengraph-types'\nimport type {\n  FieldResolverExtraArgs,\n  MetadataContext,\n} from '../types/resolvers'\nimport type { ResolvedTwitterMetadata, Twitter } from '../types/twitter-types'\nimport { resolveArray, resolveAsArrayOrUndefined } from '../generate/utils'\nimport {\n  getSocialImageMetadataBaseFallback,\n  isStringOrURL,\n  resolveUrl,\n  resolveAbsoluteUrlWithPathname,\n} from './resolve-url'\nimport { resolveTitle } from './resolve-title'\nimport { isFullStringUrl } from '../../url'\nimport { warnOnce } from '../../../build/output/log'\n\ntype FlattenArray<T> = T extends (infer U)[] ? U : T\ntype ResolvedMetadataBase = ResolvedMetadata['metadataBase']\n\nconst OgTypeFields = {\n  article: ['authors', 'tags'],\n  song: ['albums', 'musicians'],\n  playlist: ['albums', 'musicians'],\n  radio: ['creators'],\n  video: ['actors', 'directors', 'writers', 'tags'],\n  basic: [\n    'emails',\n    'phoneNumbers',\n    'faxNumbers',\n    'alternateLocale',\n    'audio',\n    'videos',\n  ],\n} as const\n\nfunction resolveAndValidateImage(\n  item: FlattenArray<OpenGraph['images'] | Twitter['images']>,\n  metadataBase: ResolvedMetadataBase,\n  isStaticMetadataRouteFile: boolean | undefined\n) {\n  if (!item) return undefined\n  const isItemUrl = isStringOrURL(item)\n  const inputUrl = isItemUrl ? item : item.url\n  if (!inputUrl) return undefined\n\n  // process.env.VERCEL is set to \"1\" when System Environment Variables are\n  // exposed. When exposed, validation is not necessary since we are falling back to\n  // process.env.VERCEL_PROJECT_PRODUCTION_URL, process.env.VERCEL_BRANCH_URL, or\n  // process.env.VERCEL_URL for the `metadataBase`. process.env.VERCEL is undefined\n  // when System Environment Variables are not exposed. When not exposed, we cannot\n  // detect in the build environment if the deployment is a Vercel deployment or not.\n  //\n  // x-ref: https://vercel.com/docs/projects/environment-variables/system-environment-variables#system-environment-variables\n  const isUsingVercelSystemEnvironmentVariables = Boolean(process.env.VERCEL)\n\n  const isRelativeUrl =\n    typeof inputUrl === 'string' && !isFullStringUrl(inputUrl)\n\n  // When no explicit metadataBase is specified by the user, we'll override it with the fallback metadata\n  // under the following conditions:\n  // - The provided URL is relative (ie ./og-image).\n  // - The image is statically generated by Next.js (such as the special `opengraph-image` route)\n  // In both cases, we want to ensure that across all environments, the ogImage is a fully qualified URL.\n  // In the `opengraph-image` case, since the user isn't explicitly passing a relative path, this ensures\n  // the ogImage will be properly discovered across different environments without the user needing to\n  // have a bunch of `process.env` checks when defining their `metadataBase`.\n  if (isRelativeUrl && (!metadataBase || isStaticMetadataRouteFile)) {\n    const fallbackMetadataBase =\n      getSocialImageMetadataBaseFallback(metadataBase)\n\n    // When not using Vercel environment variables for URL injection, we aren't able to determine\n    // a fallback value for `metadataBase`. For self-hosted setups, we want to warn\n    // about this since the only fallback we'll be able to generate is `localhost`.\n    // In development, we'll only warn for relative metadata that isn't part of the static\n    // metadata conventions (eg `opengraph-image`), as otherwise it's currently very noisy\n    // for common cases. Eventually we should remove this warning all together in favor of\n    // devtools.\n    const shouldWarn =\n      !isUsingVercelSystemEnvironmentVariables &&\n      !metadataBase &&\n      (process.env.NODE_ENV === 'production' || !isStaticMetadataRouteFile)\n\n    if (shouldWarn) {\n      warnOnce(\n        `metadataBase property in metadata export is not set for resolving social open graph or twitter images, using \"${fallbackMetadataBase.origin}\". See https://nextjs.org/docs/app/api-reference/functions/generate-metadata#metadatabase`\n      )\n    }\n\n    metadataBase = fallbackMetadataBase\n  }\n\n  return isItemUrl\n    ? {\n        url: resolveUrl(inputUrl, metadataBase),\n      }\n    : {\n        ...item,\n        // Update image descriptor url\n        url: resolveUrl(inputUrl, metadataBase),\n      }\n}\n\nexport function resolveImages(\n  images: Twitter['images'],\n  metadataBase: ResolvedMetadataBase,\n  isStaticMetadataRouteFile: boolean\n): NonNullable<ResolvedMetadata['twitter']>['images']\nexport function resolveImages(\n  images: OpenGraph['images'],\n  metadataBase: ResolvedMetadataBase,\n  isStaticMetadataRouteFile: boolean\n): NonNullable<ResolvedMetadata['openGraph']>['images']\nexport function resolveImages(\n  images: OpenGraph['images'] | Twitter['images'],\n  metadataBase: ResolvedMetadataBase,\n  isStaticMetadataRouteFile: boolean\n):\n  | NonNullable<ResolvedMetadata['twitter']>['images']\n  | NonNullable<ResolvedMetadata['openGraph']>['images'] {\n  const resolvedImages = resolveAsArrayOrUndefined(images)\n  if (!resolvedImages) return resolvedImages\n\n  const nonNullableImages = []\n  for (const item of resolvedImages) {\n    const resolvedItem = resolveAndValidateImage(\n      item,\n      metadataBase,\n      isStaticMetadataRouteFile\n    )\n    if (!resolvedItem) continue\n\n    nonNullableImages.push(resolvedItem)\n  }\n\n  return nonNullableImages\n}\n\nconst ogTypeToFields: Record<string, readonly string[]> = {\n  article: OgTypeFields.article,\n  book: OgTypeFields.article,\n  'music.song': OgTypeFields.song,\n  'music.album': OgTypeFields.song,\n  'music.playlist': OgTypeFields.playlist,\n  'music.radio_station': OgTypeFields.radio,\n  'video.movie': OgTypeFields.video,\n  'video.episode': OgTypeFields.video,\n}\n\nfunction getFieldsByOgType(ogType: OpenGraphType | undefined) {\n  if (!ogType || !(ogType in ogTypeToFields)) return OgTypeFields.basic\n  return ogTypeToFields[ogType].concat(OgTypeFields.basic)\n}\n\nexport const resolveOpenGraph: FieldResolverExtraArgs<\n  'openGraph',\n  [ResolvedMetadataBase, MetadataContext, string | null]\n> = (openGraph, metadataBase, metadataContext, titleTemplate) => {\n  if (!openGraph) return null\n\n  function resolveProps(target: ResolvedOpenGraph, og: OpenGraph) {\n    const ogType = og && 'type' in og ? og.type : undefined\n    const keys = getFieldsByOgType(ogType)\n    for (const k of keys) {\n      const key = k as keyof ResolvedOpenGraph\n      if (key in og && key !== 'url') {\n        const value = og[key]\n        // TODO: improve typing inferring\n        ;(target as any)[key] = value ? resolveArray(value) : null\n      }\n    }\n    target.images = resolveImages(\n      og.images,\n      metadataBase,\n      metadataContext.isStaticMetadataRouteFile\n    )\n  }\n\n  const resolved = {\n    ...openGraph,\n    title: resolveTitle(openGraph.title, titleTemplate),\n  } as ResolvedOpenGraph\n  resolveProps(resolved, openGraph)\n\n  resolved.url = openGraph.url\n    ? resolveAbsoluteUrlWithPathname(\n        openGraph.url,\n        metadataBase,\n        metadataContext\n      )\n    : null\n\n  return resolved\n}\n\nconst TwitterBasicInfoKeys = [\n  'site',\n  'siteId',\n  'creator',\n  'creatorId',\n  'description',\n] as const\n\nexport const resolveTwitter: FieldResolverExtraArgs<\n  'twitter',\n  [ResolvedMetadataBase, MetadataContext, string | null]\n> = (twitter, metadataBase, metadataContext, titleTemplate) => {\n  if (!twitter) return null\n  let card = 'card' in twitter ? twitter.card : undefined\n  const resolved = {\n    ...twitter,\n    title: resolveTitle(twitter.title, titleTemplate),\n  } as ResolvedTwitterMetadata\n  for (const infoKey of TwitterBasicInfoKeys) {\n    resolved[infoKey] = twitter[infoKey] || null\n  }\n\n  resolved.images = resolveImages(\n    twitter.images,\n    metadataBase,\n    metadataContext.isStaticMetadataRouteFile\n  )\n\n  card = card || (resolved.images?.length ? 'summary_large_image' : 'summary')\n  resolved.card = card\n\n  if ('card' in resolved) {\n    switch (resolved.card) {\n      case 'player': {\n        resolved.players = resolveAsArrayOrUndefined(resolved.players) || []\n        break\n      }\n      case 'app': {\n        resolved.app = resolved.app || {}\n        break\n      }\n      default:\n        break\n    }\n  }\n\n  return resolved\n}\n"], "names": ["resolveArray", "resolveAsArrayOrUndefined", "getSocialImageMetadataBaseFallback", "isStringOrURL", "resolveUrl", "resolveAbsoluteUrlWithPathname", "resolveTitle", "isFullStringUrl", "warnOnce", "Og<PERSON><PERSON><PERSON><PERSON>s", "article", "song", "playlist", "radio", "video", "basic", "resolveAndValidateImage", "item", "metadataBase", "isStaticMetadataRouteFile", "undefined", "isItemUrl", "inputUrl", "url", "isUsingVercelSystemEnvironmentVariables", "Boolean", "process", "env", "VERCEL", "isRelativeUrl", "fallbackMetadataBase", "<PERSON><PERSON><PERSON><PERSON>", "NODE_ENV", "origin", "resolveImages", "images", "resolvedImages", "nonNullableImages", "resolvedItem", "push", "ogTypeToFields", "book", "getFieldsByOgType", "ogType", "concat", "resolveOpenGraph", "openGraph", "metadataContext", "titleTemplate", "resolveProps", "target", "og", "type", "keys", "k", "key", "value", "resolved", "title", "TwitterBasicInfoKeys", "resolveTwitter", "twitter", "card", "infoKey", "length", "players", "app"], "mappings": "AAWA,SAASA,YAAY,EAAEC,yBAAyB,QAAQ,oBAAmB;AAC3E,SACEC,kCAAkC,EAClCC,aAAa,EACbC,UAAU,EACVC,8BAA8B,QACzB,gBAAe;AACtB,SAASC,YAAY,QAAQ,kBAAiB;AAC9C,SAASC,eAAe,QAAQ,YAAW;AAC3C,SAASC,QAAQ,QAAQ,4BAA2B;AAKpD,MAAMC,eAAe;IACnBC,SAAS;QAAC;QAAW;KAAO;IAC5BC,MAAM;QAAC;QAAU;KAAY;IAC7BC,UAAU;QAAC;QAAU;KAAY;IACjCC,OAAO;QAAC;KAAW;IACnBC,OAAO;QAAC;QAAU;QAAa;QAAW;KAAO;IACjDC,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;KACD;AACH;AAEA,SAASC,wBACPC,IAA2D,EAC3DC,YAAkC,EAClCC,yBAA8C;IAE9C,IAAI,CAACF,MAAM,OAAOG;IAClB,MAAMC,YAAYlB,cAAcc;IAChC,MAAMK,WAAWD,YAAYJ,OAAOA,KAAKM,GAAG;IAC5C,IAAI,CAACD,UAAU,OAAOF;IAEtB,yEAAyE;IACzE,kFAAkF;IAClF,+EAA+E;IAC/E,iFAAiF;IACjF,iFAAiF;IACjF,mFAAmF;IACnF,EAAE;IACF,0HAA0H;IAC1H,MAAMI,0CAA0CC,QAAQC,QAAQC,GAAG,CAACC,MAAM;IAE1E,MAAMC,gBACJ,OAAOP,aAAa,YAAY,CAACf,gBAAgBe;IAEnD,uGAAuG;IACvG,kCAAkC;IAClC,kDAAkD;IAClD,+FAA+F;IAC/F,uGAAuG;IACvG,uGAAuG;IACvG,oGAAoG;IACpG,2EAA2E;IAC3E,IAAIO,iBAAkB,CAAA,CAACX,gBAAgBC,yBAAwB,GAAI;QACjE,MAAMW,uBACJ5B,mCAAmCgB;QAErC,6FAA6F;QAC7F,+EAA+E;QAC/E,+EAA+E;QAC/E,sFAAsF;QACtF,sFAAsF;QACtF,sFAAsF;QACtF,YAAY;QACZ,MAAMa,aACJ,CAACP,2CACD,CAACN,gBACAQ,CAAAA,QAAQC,GAAG,CAACK,QAAQ,KAAK,gBAAgB,CAACb,yBAAwB;QAErE,IAAIY,YAAY;YACdvB,SACE,CAAC,8GAA8G,EAAEsB,qBAAqBG,MAAM,CAAC,yFAAyF,CAAC;QAE3O;QAEAf,eAAeY;IACjB;IAEA,OAAOT,YACH;QACEE,KAAKnB,WAAWkB,UAAUJ;IAC5B,IACA;QACE,GAAGD,IAAI;QACP,8BAA8B;QAC9BM,KAAKnB,WAAWkB,UAAUJ;IAC5B;AACN;AAYA,OAAO,SAASgB,cACdC,MAA+C,EAC/CjB,YAAkC,EAClCC,yBAAkC;IAIlC,MAAMiB,iBAAiBnC,0BAA0BkC;IACjD,IAAI,CAACC,gBAAgB,OAAOA;IAE5B,MAAMC,oBAAoB,EAAE;IAC5B,KAAK,MAAMpB,QAAQmB,eAAgB;QACjC,MAAME,eAAetB,wBACnBC,MACAC,cACAC;QAEF,IAAI,CAACmB,cAAc;QAEnBD,kBAAkBE,IAAI,CAACD;IACzB;IAEA,OAAOD;AACT;AAEA,MAAMG,iBAAoD;IACxD9B,SAASD,aAAaC,OAAO;IAC7B+B,MAAMhC,aAAaC,OAAO;IAC1B,cAAcD,aAAaE,IAAI;IAC/B,eAAeF,aAAaE,IAAI;IAChC,kBAAkBF,aAAaG,QAAQ;IACvC,uBAAuBH,aAAaI,KAAK;IACzC,eAAeJ,aAAaK,KAAK;IACjC,iBAAiBL,aAAaK,KAAK;AACrC;AAEA,SAAS4B,kBAAkBC,MAAiC;IAC1D,IAAI,CAACA,UAAU,CAAEA,CAAAA,UAAUH,cAAa,GAAI,OAAO/B,aAAaM,KAAK;IACrE,OAAOyB,cAAc,CAACG,OAAO,CAACC,MAAM,CAACnC,aAAaM,KAAK;AACzD;AAEA,OAAO,MAAM8B,mBAGT,CAACC,WAAW5B,cAAc6B,iBAAiBC;IAC7C,IAAI,CAACF,WAAW,OAAO;IAEvB,SAASG,aAAaC,MAAyB,EAAEC,EAAa;QAC5D,MAAMR,SAASQ,MAAM,UAAUA,KAAKA,GAAGC,IAAI,GAAGhC;QAC9C,MAAMiC,OAAOX,kBAAkBC;QAC/B,KAAK,MAAMW,KAAKD,KAAM;YACpB,MAAME,MAAMD;YACZ,IAAIC,OAAOJ,MAAMI,QAAQ,OAAO;gBAC9B,MAAMC,QAAQL,EAAE,CAACI,IAAI;gBAEnBL,MAAc,CAACK,IAAI,GAAGC,QAAQxD,aAAawD,SAAS;YACxD;QACF;QACAN,OAAOf,MAAM,GAAGD,cACdiB,GAAGhB,MAAM,EACTjB,cACA6B,gBAAgB5B,yBAAyB;IAE7C;IAEA,MAAMsC,WAAW;QACf,GAAGX,SAAS;QACZY,OAAOpD,aAAawC,UAAUY,KAAK,EAAEV;IACvC;IACAC,aAAaQ,UAAUX;IAEvBW,SAASlC,GAAG,GAAGuB,UAAUvB,GAAG,GACxBlB,+BACEyC,UAAUvB,GAAG,EACbL,cACA6B,mBAEF;IAEJ,OAAOU;AACT,EAAC;AAED,MAAME,uBAAuB;IAC3B;IACA;IACA;IACA;IACA;CACD;AAED,OAAO,MAAMC,iBAGT,CAACC,SAAS3C,cAAc6B,iBAAiBC;QAiB3BS;IAhBhB,IAAI,CAACI,SAAS,OAAO;IACrB,IAAIC,OAAO,UAAUD,UAAUA,QAAQC,IAAI,GAAG1C;IAC9C,MAAMqC,WAAW;QACf,GAAGI,OAAO;QACVH,OAAOpD,aAAauD,QAAQH,KAAK,EAAEV;IACrC;IACA,KAAK,MAAMe,WAAWJ,qBAAsB;QAC1CF,QAAQ,CAACM,QAAQ,GAAGF,OAAO,CAACE,QAAQ,IAAI;IAC1C;IAEAN,SAAStB,MAAM,GAAGD,cAChB2B,QAAQ1B,MAAM,EACdjB,cACA6B,gBAAgB5B,yBAAyB;IAG3C2C,OAAOA,QAASL,CAAAA,EAAAA,mBAAAA,SAAStB,MAAM,qBAAfsB,iBAAiBO,MAAM,IAAG,wBAAwB,SAAQ;IAC1EP,SAASK,IAAI,GAAGA;IAEhB,IAAI,UAAUL,UAAU;QACtB,OAAQA,SAASK,IAAI;YACnB,KAAK;gBAAU;oBACbL,SAASQ,OAAO,GAAGhE,0BAA0BwD,SAASQ,OAAO,KAAK,EAAE;oBACpE;gBACF;YACA,KAAK;gBAAO;oBACVR,SAASS,GAAG,GAAGT,SAASS,GAAG,IAAI,CAAC;oBAChC;gBACF;YACA;gBACE;QACJ;IACF;IAEA,OAAOT;AACT,EAAC"}