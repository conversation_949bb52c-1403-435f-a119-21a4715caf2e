{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/ui/container/root-layout-missing-tags-error.tsx"], "sourcesContent": ["import { useCallback } from 'react'\nimport { HotlinkedText } from '../components/hot-linked-text'\nimport { ErrorOverlayLayout } from '../components/errors/error-overlay-layout/error-overlay-layout'\nimport type { ErrorBaseProps } from '../components/errors/error-overlay/error-overlay'\n\ninterface RootLayoutMissingTagsErrorProps extends ErrorBaseProps {\n  missingTags: string[]\n}\n\nexport function RootLayoutMissingTagsError({\n  missingTags,\n  ...props\n}: RootLayoutMissingTagsErrorProps) {\n  const noop = useCallback(() => {}, [])\n  const error = new Error(\n    `The following tags are missing in the Root Layout: ${missingTags\n      .map((tagName) => `<${tagName}>`)\n      .join(\n        ', '\n      )}.\\nRead more at https://nextjs.org/docs/messages/missing-root-layout-tags`\n  )\n  return (\n    <ErrorOverlayLayout\n      errorType=\"Missing Required HTML Tag\"\n      error={error}\n      errorMessage={<HotlinkedText text={error.message} />}\n      onClose={noop}\n      {...props}\n    />\n  )\n}\n"], "names": ["useCallback", "HotlinkedText", "ErrorOverlayLayout", "RootLayoutMissingTagsError", "missingTags", "props", "noop", "error", "Error", "map", "tagName", "join", "errorType", "errorMessage", "text", "message", "onClose"], "mappings": ";AAAA,SAASA,WAAW,QAAQ,QAAO;AACnC,SAASC,aAAa,QAAQ,gCAA+B;AAC7D,SAASC,kBAAkB,QAAQ,iEAAgE;AAOnG,OAAO,SAASC,2BAA2B,KAGT;IAHS,IAAA,EACzCC,WAAW,EACX,GAAGC,OAC6B,GAHS;IAIzC,MAAMC,OAAON,YAAY,KAAO,GAAG,EAAE;IACrC,MAAMO,QAAQ,qBAMb,CANa,IAAIC,MAChB,AAAC,wDAAqDJ,YACnDK,GAAG,CAAC,CAACC,UAAY,AAAC,MAAGA,UAAQ,KAC7BC,IAAI,CACH,QACA,8EALQ,qBAAA;eAAA;oBAAA;sBAAA;IAMd;IACA,qBACE,KAACT;QACCU,WAAU;QACVL,OAAOA;QACPM,4BAAc,KAACZ;YAAca,MAAMP,MAAMQ,OAAO;;QAChDC,SAASV;QACR,GAAGD,KAAK;;AAGf"}