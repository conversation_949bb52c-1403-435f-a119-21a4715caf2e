{"version": 3, "sources": ["../../../../../../src/build/webpack/loaders/lightningcss-loader/src/loader.ts"], "sourcesContent": ["import type { LoaderContext } from 'webpack'\nimport { getTargets } from './utils'\nimport {\n  getImportCode,\n  type ApiParam,\n  type ApiReplacement,\n  type CssExport,\n  type CssImport,\n  getModuleCode,\n  getExportCode,\n} from './codegen'\nimport {\n  getFilter,\n  getPreRequester,\n  isDataUrl,\n  isUrlRequestable,\n  requestify,\n  resolveRequests,\n} from '../../css-loader/src/utils'\nimport { stringifyRequest } from '../../../stringify-request'\nimport { ECacheKey } from './interface'\n\nconst encoder = new TextEncoder()\n\nfunction createUrlAndImportVisitor(\n  visitorOptions: any,\n  apis: ApiParam[],\n  imports: CssImport[],\n  replacements: ApiReplacement[],\n  replacedUrls: Map<number, string>,\n  replacedImportUrls: Map<number, string>\n) {\n  const importUrlToNameMap = new Map<string, string>()\n\n  let hasUrlImportHelper = false\n  const urlToNameMap = new Map()\n  const urlToReplacementMap = new Map()\n  let urlIndex = -1\n  let importUrlIndex = -1\n\n  function handleUrl(u: { url: string; loc: unknown }): unknown {\n    let url = u.url\n    const needKeep = visitorOptions.urlFilter(url)\n\n    if (!needKeep) {\n      return u\n    }\n\n    if (isDataUrl(url)) {\n      return u\n    }\n\n    urlIndex++\n\n    replacedUrls.set(urlIndex, url)\n    url = `__NEXT_LIGHTNINGCSS_LOADER_URL_REPLACE_${urlIndex}__`\n\n    const [, query, hashOrQuery] = url.split(/(\\?)?#/, 3)\n\n    const queryParts = url.split('!')\n    let prefix: string | undefined\n\n    if (queryParts.length > 1) {\n      url = queryParts.pop()!\n      prefix = queryParts.join('!')\n    }\n\n    let hash = query ? '?' : ''\n    hash += hashOrQuery ? `#${hashOrQuery}` : ''\n\n    if (!hasUrlImportHelper) {\n      imports.push({\n        type: 'get_url_import',\n        importName: '___CSS_LOADER_GET_URL_IMPORT___',\n        url: JSON.stringify(\n          require.resolve('../../css-loader/src/runtime/getUrl.js')\n        ),\n        index: -1,\n      })\n\n      hasUrlImportHelper = true\n    }\n\n    const newUrl = prefix ? `${prefix}!${url}` : url\n    let importName = urlToNameMap.get(newUrl)\n\n    if (!importName) {\n      importName = `___CSS_LOADER_URL_IMPORT_${urlToNameMap.size}___`\n      urlToNameMap.set(newUrl, importName)\n\n      imports.push({\n        type: 'url',\n        importName,\n        url: JSON.stringify(newUrl),\n        index: urlIndex,\n      })\n    }\n    // This should be true for string-urls in image-set\n    const needQuotes = false\n\n    const replacementKey = JSON.stringify({ newUrl, hash, needQuotes })\n    let replacementName = urlToReplacementMap.get(replacementKey)\n\n    if (!replacementName) {\n      replacementName = `___CSS_LOADER_URL_REPLACEMENT_${urlToReplacementMap.size}___`\n      urlToReplacementMap.set(replacementKey, replacementName)\n\n      replacements.push({\n        replacementName,\n        importName,\n        hash,\n        needQuotes,\n      })\n    }\n\n    return {\n      loc: u.loc,\n      url: replacementName,\n    }\n  }\n\n  return {\n    Rule: {\n      import(node: any) {\n        if (visitorOptions.importFilter) {\n          const needKeep = visitorOptions.importFilter(\n            node.value.url,\n            node.value.media\n          )\n\n          if (!needKeep) {\n            return node\n          }\n        }\n        let url = node.value.url\n\n        importUrlIndex++\n\n        replacedImportUrls.set(importUrlIndex, url)\n        url = `__NEXT_LIGHTNINGCSS_LOADER_IMPORT_URL_REPLACE_${importUrlIndex}__`\n\n        // TODO: Use identical logic as valueParser.stringify()\n        const media = node.value.media.mediaQueries.length\n          ? JSON.stringify(node.value.media.mediaQueries)\n          : undefined\n        const isRequestable = isUrlRequestable(url)\n        let prefix: string | undefined\n        if (isRequestable) {\n          const queryParts = url.split('!')\n          if (queryParts.length > 1) {\n            url = queryParts.pop()!\n            prefix = queryParts.join('!')\n          }\n        }\n        if (!isRequestable) {\n          apis.push({ url, media })\n          // Bug of lightningcss\n          return { type: 'ignored', value: '' }\n        }\n        const newUrl = prefix ? `${prefix}!${url}` : url\n        let importName = importUrlToNameMap.get(newUrl)\n        if (!importName) {\n          importName = `___CSS_LOADER_AT_RULE_IMPORT_${importUrlToNameMap.size}___`\n          importUrlToNameMap.set(newUrl, importName)\n\n          const importUrl = visitorOptions.urlHandler(newUrl)\n          imports.push({\n            type: 'rule_import',\n            importName,\n            url: importUrl,\n          })\n        }\n        apis.push({ importName, media })\n        // Bug of lightningcss\n        return { type: 'ignored', value: '' }\n      },\n    },\n    Url(node: any) {\n      return handleUrl(node)\n    },\n  }\n}\n\nfunction createIcssVisitor({\n  apis,\n  imports,\n  replacements,\n  replacedUrls,\n  urlHandler,\n}: {\n  apis: ApiParam[]\n  imports: CssImport[]\n  replacements: ApiReplacement[]\n  replacedUrls: Map<number, string>\n  urlHandler: (url: any) => string\n}) {\n  let index = -1\n  let replacementIndex = -1\n\n  return {\n    Declaration: {\n      composes(node: any) {\n        if (node.property === 'unparsed') {\n          return\n        }\n\n        const specifier = node.value.from\n\n        if (specifier?.type !== 'file') {\n          return\n        }\n\n        let url = specifier.value\n        if (!url) {\n          return\n        }\n\n        index++\n\n        replacedUrls.set(index, url)\n        url = `__NEXT_LIGHTNINGCSS_LOADER_ICSS_URL_REPLACE_${index}__`\n\n        const importName = `___CSS_LOADER_ICSS_IMPORT_${imports.length}___`\n        imports.push({\n          type: 'icss_import',\n          importName,\n          icss: true,\n          url: urlHandler(url),\n          index,\n        })\n\n        apis.push({ importName, dedupe: true, index })\n\n        const newNames: string[] = []\n\n        for (const localName of node.value.names) {\n          replacementIndex++\n          const replacementName = `___CSS_LOADER_ICSS_IMPORT_${index}_REPLACEMENT_${replacementIndex}___`\n\n          replacements.push({\n            replacementName,\n            importName,\n            localName,\n          })\n          newNames.push(replacementName)\n        }\n\n        return {\n          property: 'composes',\n          value: {\n            loc: node.value.loc,\n            names: newNames,\n            from: specifier,\n          },\n        }\n      },\n    },\n  }\n}\n\nconst LOADER_NAME = `lightningcss-loader`\nexport async function LightningCssLoader(\n  this: LoaderContext<any>,\n  source: string,\n  prevMap?: Record<string, any>\n): Promise<void> {\n  const done = this.async()\n  const options = this.getOptions()\n  const { implementation, targets: userTargets, ...opts } = options\n\n  options.modules ??= {}\n\n  if (implementation && typeof implementation.transformCss !== 'function') {\n    done(\n      new TypeError(\n        `[${LOADER_NAME}]: options.implementation.transformCss must be an 'lightningcss' transform function. Received ${typeof implementation.transformCss}`\n      )\n    )\n    return\n  }\n\n  if (options.postcss) {\n    const { postcssWithPlugins } = await options.postcss()\n\n    if (postcssWithPlugins?.plugins?.length > 0) {\n      throw new Error(\n        `[${LOADER_NAME}]: experimental.useLightningcss does not work with postcss plugins. Please remove 'useLightningcss: true' from your configuration.`\n      )\n    }\n  }\n\n  const exports: CssExport[] = []\n  const imports: CssImport[] = []\n  const icssImports: CssImport[] = []\n  const apis: ApiParam[] = []\n  const replacements: ApiReplacement[] = []\n\n  if (options.modules?.exportOnlyLocals !== true) {\n    imports.unshift({\n      type: 'api_import',\n      importName: '___CSS_LOADER_API_IMPORT___',\n      url: stringifyRequest(\n        this,\n        require.resolve('../../css-loader/src/runtime/api')\n      ),\n    })\n  }\n  const { loadBindings } = require('next/dist/build/swc')\n\n  const transform =\n    implementation?.transformCss ??\n    (await loadBindings()).css.lightning.transform\n\n  const replacedUrls = new Map<number, string>()\n  const icssReplacedUrls = new Map<number, string>()\n  const replacedImportUrls = new Map<number, string>()\n\n  const urlImportVisitor = createUrlAndImportVisitor(\n    {\n      urlHandler: (url: any) =>\n        stringifyRequest(\n          this,\n          getPreRequester(this)(options.importLoaders ?? 0) + url\n        ),\n      urlFilter: getFilter(options.url, this.resourcePath),\n      importFilter: getFilter(options.import, this.resourcePath),\n\n      context: this.context,\n    },\n    apis,\n    imports,\n    replacements,\n    replacedUrls,\n    replacedImportUrls\n  )\n\n  const icssVisitor = createIcssVisitor({\n    apis,\n    imports: icssImports,\n    replacements,\n    replacedUrls: icssReplacedUrls,\n    urlHandler: (url: string) =>\n      stringifyRequest(\n        this,\n        getPreRequester(this)(options.importLoaders) + url\n      ),\n  })\n\n  // This works by returned visitors are not conflicting.\n  // naive workaround for composeVisitors, as we do not directly depends on lightningcss's npm pkg\n  // but next-swc provides bindings\n  const visitor = {\n    ...urlImportVisitor,\n    ...icssVisitor,\n  }\n\n  try {\n    const {\n      code,\n      map,\n      exports: moduleExports,\n    } = transform({\n      ...opts,\n      visitor,\n      cssModules: options.modules\n        ? {\n            pattern: process.env.__NEXT_TEST_MODE\n              ? '[name]__[local]'\n              : '[name]__[hash]__[local]',\n          }\n        : undefined,\n      filename: this.resourcePath,\n      code: encoder.encode(source),\n      sourceMap: this.sourceMap,\n      targets: getTargets({ targets: userTargets, key: ECacheKey.loader }),\n      inputSourceMap:\n        this.sourceMap && prevMap ? JSON.stringify(prevMap) : undefined,\n      include: 1, // Features.Nesting\n    })\n    let cssCodeAsString = code.toString()\n\n    if (moduleExports) {\n      for (const name in moduleExports) {\n        if (Object.prototype.hasOwnProperty.call(moduleExports, name)) {\n          const v = moduleExports[name]\n          let value = v.name\n          for (const compose of v.composes) {\n            value += ` ${compose.name}`\n          }\n\n          exports.push({\n            name,\n            value,\n          })\n        }\n      }\n    }\n\n    if (replacedUrls.size !== 0) {\n      const urlResolver = this.getResolve({\n        conditionNames: ['asset'],\n        mainFields: ['asset'],\n        mainFiles: [],\n        extensions: [],\n      })\n\n      for (const [index, url] of replacedUrls.entries()) {\n        const [pathname, ,] = url.split(/(\\?)?#/, 3)\n\n        const request = requestify(pathname, this.rootContext)\n        const resolvedUrl = await resolveRequests(urlResolver, this.context, [\n          ...new Set([request, url]),\n        ])\n\n        for (const importItem of imports) {\n          importItem.url = importItem.url.replace(\n            `__NEXT_LIGHTNINGCSS_LOADER_URL_REPLACE_${index}__`,\n            resolvedUrl ?? url\n          )\n        }\n      }\n    }\n\n    if (replacedImportUrls.size !== 0) {\n      const importResolver = this.getResolve({\n        conditionNames: ['style'],\n        extensions: ['.css'],\n        mainFields: ['css', 'style', 'main', '...'],\n        mainFiles: ['index', '...'],\n        restrictions: [/\\.css$/i],\n      })\n\n      for (const [index, url] of replacedImportUrls.entries()) {\n        const [pathname, ,] = url.split(/(\\?)?#/, 3)\n\n        const request = requestify(pathname, this.rootContext)\n        const resolvedUrl = await resolveRequests(\n          importResolver,\n          this.context,\n          [...new Set([request, url])]\n        )\n\n        for (const importItem of imports) {\n          importItem.url = importItem.url.replace(\n            `__NEXT_LIGHTNINGCSS_LOADER_IMPORT_URL_REPLACE_${index}__`,\n            resolvedUrl ?? url\n          )\n        }\n      }\n    }\n    if (icssReplacedUrls.size !== 0) {\n      const icssResolver = this.getResolve({\n        conditionNames: ['style'],\n        extensions: [],\n        mainFields: ['css', 'style', 'main', '...'],\n        mainFiles: ['index', '...'],\n      })\n\n      for (const [index, url] of icssReplacedUrls.entries()) {\n        const [pathname, ,] = url.split(/(\\?)?#/, 3)\n\n        const request = requestify(pathname, this.rootContext)\n        const resolvedUrl = await resolveRequests(icssResolver, this.context, [\n          ...new Set([url, request]),\n        ])\n\n        for (const importItem of icssImports) {\n          importItem.url = importItem.url.replace(\n            `__NEXT_LIGHTNINGCSS_LOADER_ICSS_URL_REPLACE_${index}__`,\n            resolvedUrl ?? url\n          )\n        }\n      }\n    }\n\n    imports.push(...icssImports)\n\n    const importCode = getImportCode(imports, options)\n    const moduleCode = getModuleCode(\n      { css: cssCodeAsString, map },\n      apis,\n      replacements,\n      options,\n      this\n    )\n    const exportCode = getExportCode(exports, replacements, options)\n\n    const esCode = `${importCode}${moduleCode}${exportCode}`\n\n    done(null, esCode, map && JSON.parse(map.toString()))\n  } catch (error: unknown) {\n    console.error('lightningcss-loader error', error)\n    done(error as Error)\n  }\n}\n\nexport const raw = true\n"], "names": ["getTargets", "getImportCode", "getModuleCode", "getExportCode", "getFilter", "getPreRequester", "isDataUrl", "isUrlRequestable", "requestify", "resolveRequests", "stringifyRequest", "<PERSON><PERSON><PERSON><PERSON>", "encoder", "TextEncoder", "createUrlAndImportVisitor", "visitorOptions", "apis", "imports", "replacements", "replacedUrls", "replacedImportUrls", "importUrlToNameMap", "Map", "hasUrlImportHelper", "urlToNameMap", "urlToReplacementMap", "urlIndex", "importUrlIndex", "handleUrl", "u", "url", "<PERSON><PERSON><PERSON>", "url<PERSON><PERSON><PERSON>", "set", "query", "hash<PERSON><PERSON><PERSON><PERSON><PERSON>", "split", "queryParts", "prefix", "length", "pop", "join", "hash", "push", "type", "importName", "JSON", "stringify", "require", "resolve", "index", "newUrl", "get", "size", "needQuotes", "<PERSON><PERSON><PERSON>", "replacement<PERSON>ame", "loc", "Rule", "import", "node", "importFilter", "value", "media", "mediaQueries", "undefined", "isRequestable", "importUrl", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Url", "createIcssVisitor", "replacementIndex", "Declaration", "composes", "property", "specifier", "from", "icss", "dedupe", "newNames", "localName", "names", "LOADER_NAME", "Lightning<PERSON>s<PERSON><PERSON>der", "source", "prevMap", "options", "done", "async", "getOptions", "implementation", "targets", "userTargets", "opts", "modules", "transformCss", "TypeError", "postcss", "postcssWithPlugins", "plugins", "Error", "exports", "icssImports", "exportOnlyLocals", "unshift", "loadBindings", "transform", "css", "lightning", "icssReplacedUrls", "urlImportVisitor", "importLoaders", "resourcePath", "context", "icssVisitor", "visitor", "code", "map", "moduleExports", "cssModules", "pattern", "process", "env", "__NEXT_TEST_MODE", "filename", "encode", "sourceMap", "key", "loader", "inputSourceMap", "include", "cssCodeAsString", "toString", "name", "Object", "prototype", "hasOwnProperty", "call", "v", "compose", "urlResolver", "getResolve", "conditionNames", "mainFields", "mainFiles", "extensions", "entries", "pathname", "request", "rootContext", "resolvedUrl", "Set", "importItem", "replace", "importResolver", "restrictions", "icssResolver", "importCode", "moduleCode", "exportCode", "esCode", "parse", "error", "console", "raw"], "mappings": "AACA,SAASA,UAAU,QAAQ,UAAS;AACpC,SACEC,aAAa,EAKbC,aAAa,EACbC,aAAa,QACR,YAAW;AAClB,SACEC,SAAS,EACTC,eAAe,EACfC,SAAS,EACTC,gBAAgB,EAChBC,UAAU,EACVC,eAAe,QACV,6BAA4B;AACnC,SAASC,gBAAgB,QAAQ,6BAA4B;AAC7D,SAASC,SAAS,QAAQ,cAAa;AAEvC,MAAMC,UAAU,IAAIC;AAEpB,SAASC,0BACPC,cAAmB,EACnBC,IAAgB,EAChBC,OAAoB,EACpBC,YAA8B,EAC9BC,YAAiC,EACjCC,kBAAuC;IAEvC,MAAMC,qBAAqB,IAAIC;IAE/B,IAAIC,qBAAqB;IACzB,MAAMC,eAAe,IAAIF;IACzB,MAAMG,sBAAsB,IAAIH;IAChC,IAAII,WAAW,CAAC;IAChB,IAAIC,iBAAiB,CAAC;IAEtB,SAASC,UAAUC,CAAgC;QACjD,IAAIC,MAAMD,EAAEC,GAAG;QACf,MAAMC,WAAWhB,eAAeiB,SAAS,CAACF;QAE1C,IAAI,CAACC,UAAU;YACb,OAAOF;QACT;QAEA,IAAIvB,UAAUwB,MAAM;YAClB,OAAOD;QACT;QAEAH;QAEAP,aAAac,GAAG,CAACP,UAAUI;QAC3BA,MAAM,CAAC,uCAAuC,EAAEJ,SAAS,EAAE,CAAC;QAE5D,MAAM,GAAGQ,OAAOC,YAAY,GAAGL,IAAIM,KAAK,CAAC,UAAU;QAEnD,MAAMC,aAAaP,IAAIM,KAAK,CAAC;QAC7B,IAAIE;QAEJ,IAAID,WAAWE,MAAM,GAAG,GAAG;YACzBT,MAAMO,WAAWG,GAAG;YACpBF,SAASD,WAAWI,IAAI,CAAC;QAC3B;QAEA,IAAIC,OAAOR,QAAQ,MAAM;QACzBQ,QAAQP,cAAc,CAAC,CAAC,EAAEA,aAAa,GAAG;QAE1C,IAAI,CAACZ,oBAAoB;YACvBN,QAAQ0B,IAAI,CAAC;gBACXC,MAAM;gBACNC,YAAY;gBACZf,KAAKgB,KAAKC,SAAS,CACjBC,QAAQC,OAAO,CAAC;gBAElBC,OAAO,CAAC;YACV;YAEA3B,qBAAqB;QACvB;QAEA,MAAM4B,SAASb,SAAS,GAAGA,OAAO,CAAC,EAAER,KAAK,GAAGA;QAC7C,IAAIe,aAAarB,aAAa4B,GAAG,CAACD;QAElC,IAAI,CAACN,YAAY;YACfA,aAAa,CAAC,yBAAyB,EAAErB,aAAa6B,IAAI,CAAC,GAAG,CAAC;YAC/D7B,aAAaS,GAAG,CAACkB,QAAQN;YAEzB5B,QAAQ0B,IAAI,CAAC;gBACXC,MAAM;gBACNC;gBACAf,KAAKgB,KAAKC,SAAS,CAACI;gBACpBD,OAAOxB;YACT;QACF;QACA,mDAAmD;QACnD,MAAM4B,aAAa;QAEnB,MAAMC,iBAAiBT,KAAKC,SAAS,CAAC;YAAEI;YAAQT;YAAMY;QAAW;QACjE,IAAIE,kBAAkB/B,oBAAoB2B,GAAG,CAACG;QAE9C,IAAI,CAACC,iBAAiB;YACpBA,kBAAkB,CAAC,8BAA8B,EAAE/B,oBAAoB4B,IAAI,CAAC,GAAG,CAAC;YAChF5B,oBAAoBQ,GAAG,CAACsB,gBAAgBC;YAExCtC,aAAayB,IAAI,CAAC;gBAChBa;gBACAX;gBACAH;gBACAY;YACF;QACF;QAEA,OAAO;YACLG,KAAK5B,EAAE4B,GAAG;YACV3B,KAAK0B;QACP;IACF;IAEA,OAAO;QACLE,MAAM;YACJC,QAAOC,IAAS;gBACd,IAAI7C,eAAe8C,YAAY,EAAE;oBAC/B,MAAM9B,WAAWhB,eAAe8C,YAAY,CAC1CD,KAAKE,KAAK,CAAChC,GAAG,EACd8B,KAAKE,KAAK,CAACC,KAAK;oBAGlB,IAAI,CAAChC,UAAU;wBACb,OAAO6B;oBACT;gBACF;gBACA,IAAI9B,MAAM8B,KAAKE,KAAK,CAAChC,GAAG;gBAExBH;gBAEAP,mBAAmBa,GAAG,CAACN,gBAAgBG;gBACvCA,MAAM,CAAC,8CAA8C,EAAEH,eAAe,EAAE,CAAC;gBAEzE,uDAAuD;gBACvD,MAAMoC,QAAQH,KAAKE,KAAK,CAACC,KAAK,CAACC,YAAY,CAACzB,MAAM,GAC9CO,KAAKC,SAAS,CAACa,KAAKE,KAAK,CAACC,KAAK,CAACC,YAAY,IAC5CC;gBACJ,MAAMC,gBAAgB3D,iBAAiBuB;gBACvC,IAAIQ;gBACJ,IAAI4B,eAAe;oBACjB,MAAM7B,aAAaP,IAAIM,KAAK,CAAC;oBAC7B,IAAIC,WAAWE,MAAM,GAAG,GAAG;wBACzBT,MAAMO,WAAWG,GAAG;wBACpBF,SAASD,WAAWI,IAAI,CAAC;oBAC3B;gBACF;gBACA,IAAI,CAACyB,eAAe;oBAClBlD,KAAK2B,IAAI,CAAC;wBAAEb;wBAAKiC;oBAAM;oBACvB,sBAAsB;oBACtB,OAAO;wBAAEnB,MAAM;wBAAWkB,OAAO;oBAAG;gBACtC;gBACA,MAAMX,SAASb,SAAS,GAAGA,OAAO,CAAC,EAAER,KAAK,GAAGA;gBAC7C,IAAIe,aAAaxB,mBAAmB+B,GAAG,CAACD;gBACxC,IAAI,CAACN,YAAY;oBACfA,aAAa,CAAC,6BAA6B,EAAExB,mBAAmBgC,IAAI,CAAC,GAAG,CAAC;oBACzEhC,mBAAmBY,GAAG,CAACkB,QAAQN;oBAE/B,MAAMsB,YAAYpD,eAAeqD,UAAU,CAACjB;oBAC5ClC,QAAQ0B,IAAI,CAAC;wBACXC,MAAM;wBACNC;wBACAf,KAAKqC;oBACP;gBACF;gBACAnD,KAAK2B,IAAI,CAAC;oBAAEE;oBAAYkB;gBAAM;gBAC9B,sBAAsB;gBACtB,OAAO;oBAAEnB,MAAM;oBAAWkB,OAAO;gBAAG;YACtC;QACF;QACAO,KAAIT,IAAS;YACX,OAAOhC,UAAUgC;QACnB;IACF;AACF;AAEA,SAASU,kBAAkB,EACzBtD,IAAI,EACJC,OAAO,EACPC,YAAY,EACZC,YAAY,EACZiD,UAAU,EAOX;IACC,IAAIlB,QAAQ,CAAC;IACb,IAAIqB,mBAAmB,CAAC;IAExB,OAAO;QACLC,aAAa;YACXC,UAASb,IAAS;gBAChB,IAAIA,KAAKc,QAAQ,KAAK,YAAY;oBAChC;gBACF;gBAEA,MAAMC,YAAYf,KAAKE,KAAK,CAACc,IAAI;gBAEjC,IAAID,CAAAA,6BAAAA,UAAW/B,IAAI,MAAK,QAAQ;oBAC9B;gBACF;gBAEA,IAAId,MAAM6C,UAAUb,KAAK;gBACzB,IAAI,CAAChC,KAAK;oBACR;gBACF;gBAEAoB;gBAEA/B,aAAac,GAAG,CAACiB,OAAOpB;gBACxBA,MAAM,CAAC,4CAA4C,EAAEoB,MAAM,EAAE,CAAC;gBAE9D,MAAML,aAAa,CAAC,0BAA0B,EAAE5B,QAAQsB,MAAM,CAAC,GAAG,CAAC;gBACnEtB,QAAQ0B,IAAI,CAAC;oBACXC,MAAM;oBACNC;oBACAgC,MAAM;oBACN/C,KAAKsC,WAAWtC;oBAChBoB;gBACF;gBAEAlC,KAAK2B,IAAI,CAAC;oBAAEE;oBAAYiC,QAAQ;oBAAM5B;gBAAM;gBAE5C,MAAM6B,WAAqB,EAAE;gBAE7B,KAAK,MAAMC,aAAapB,KAAKE,KAAK,CAACmB,KAAK,CAAE;oBACxCV;oBACA,MAAMf,kBAAkB,CAAC,0BAA0B,EAAEN,MAAM,aAAa,EAAEqB,iBAAiB,GAAG,CAAC;oBAE/FrD,aAAayB,IAAI,CAAC;wBAChBa;wBACAX;wBACAmC;oBACF;oBACAD,SAASpC,IAAI,CAACa;gBAChB;gBAEA,OAAO;oBACLkB,UAAU;oBACVZ,OAAO;wBACLL,KAAKG,KAAKE,KAAK,CAACL,GAAG;wBACnBwB,OAAOF;wBACPH,MAAMD;oBACR;gBACF;YACF;QACF;IACF;AACF;AAEA,MAAMO,cAAc,CAAC,mBAAmB,CAAC;AACzC,OAAO,eAAeC,mBAEpBC,MAAc,EACdC,OAA6B;QAiCzBC;IA/BJ,MAAMC,OAAO,IAAI,CAACC,KAAK;IACvB,MAAMF,UAAU,IAAI,CAACG,UAAU;IAC/B,MAAM,EAAEC,cAAc,EAAEC,SAASC,WAAW,EAAE,GAAGC,MAAM,GAAGP;IAE1DA,QAAQQ,OAAO,KAAK,CAAC;IAErB,IAAIJ,kBAAkB,OAAOA,eAAeK,YAAY,KAAK,YAAY;QACvER,KACE,qBAEC,CAFD,IAAIS,UACF,CAAC,CAAC,EAAEd,YAAY,8FAA8F,EAAE,OAAOQ,eAAeK,YAAY,EAAE,GADtJ,qBAAA;mBAAA;wBAAA;0BAAA;QAEA;QAEF;IACF;IAEA,IAAIT,QAAQW,OAAO,EAAE;YAGfC;QAFJ,MAAM,EAAEA,kBAAkB,EAAE,GAAG,MAAMZ,QAAQW,OAAO;QAEpD,IAAIC,CAAAA,uCAAAA,8BAAAA,mBAAoBC,OAAO,qBAA3BD,4BAA6B3D,MAAM,IAAG,GAAG;YAC3C,MAAM,qBAEL,CAFK,IAAI6D,MACR,CAAC,CAAC,EAAElB,YAAY,kIAAkI,CAAC,GAD/I,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;IACF;IAEA,MAAMmB,UAAuB,EAAE;IAC/B,MAAMpF,UAAuB,EAAE;IAC/B,MAAMqF,cAA2B,EAAE;IACnC,MAAMtF,OAAmB,EAAE;IAC3B,MAAME,eAAiC,EAAE;IAEzC,IAAIoE,EAAAA,mBAAAA,QAAQQ,OAAO,qBAAfR,iBAAiBiB,gBAAgB,MAAK,MAAM;QAC9CtF,QAAQuF,OAAO,CAAC;YACd5D,MAAM;YACNC,YAAY;YACZf,KAAKpB,iBACH,IAAI,EACJsC,QAAQC,OAAO,CAAC;QAEpB;IACF;IACA,MAAM,EAAEwD,YAAY,EAAE,GAAGzD,QAAQ;IAEjC,MAAM0D,YACJhB,CAAAA,kCAAAA,eAAgBK,YAAY,KAC5B,AAAC,CAAA,MAAMU,cAAa,EAAGE,GAAG,CAACC,SAAS,CAACF,SAAS;IAEhD,MAAMvF,eAAe,IAAIG;IACzB,MAAMuF,mBAAmB,IAAIvF;IAC7B,MAAMF,qBAAqB,IAAIE;IAE/B,MAAMwF,mBAAmBhG,0BACvB;QACEsD,YAAY,CAACtC,MACXpB,iBACE,IAAI,EACJL,gBAAgB,IAAI,EAAEiF,QAAQyB,aAAa,IAAI,KAAKjF;QAExDE,WAAW5B,UAAUkF,QAAQxD,GAAG,EAAE,IAAI,CAACkF,YAAY;QACnDnD,cAAczD,UAAUkF,QAAQ3B,MAAM,EAAE,IAAI,CAACqD,YAAY;QAEzDC,SAAS,IAAI,CAACA,OAAO;IACvB,GACAjG,MACAC,SACAC,cACAC,cACAC;IAGF,MAAM8F,cAAc5C,kBAAkB;QACpCtD;QACAC,SAASqF;QACTpF;QACAC,cAAc0F;QACdzC,YAAY,CAACtC,MACXpB,iBACE,IAAI,EACJL,gBAAgB,IAAI,EAAEiF,QAAQyB,aAAa,IAAIjF;IAErD;IAEA,uDAAuD;IACvD,gGAAgG;IAChG,iCAAiC;IACjC,MAAMqF,UAAU;QACd,GAAGL,gBAAgB;QACnB,GAAGI,WAAW;IAChB;IAEA,IAAI;QACF,MAAM,EACJE,IAAI,EACJC,GAAG,EACHhB,SAASiB,aAAa,EACvB,GAAGZ,UAAU;YACZ,GAAGb,IAAI;YACPsB;YACAI,YAAYjC,QAAQQ,OAAO,GACvB;gBACE0B,SAASC,QAAQC,GAAG,CAACC,gBAAgB,GACjC,oBACA;YACN,IACA1D;YACJ2D,UAAU,IAAI,CAACZ,YAAY;YAC3BI,MAAMxG,QAAQiH,MAAM,CAACzC;YACrB0C,WAAW,IAAI,CAACA,SAAS;YACzBnC,SAAS3F,WAAW;gBAAE2F,SAASC;gBAAamC,KAAKpH,UAAUqH,MAAM;YAAC;YAClEC,gBACE,IAAI,CAACH,SAAS,IAAIzC,UAAUvC,KAAKC,SAAS,CAACsC,WAAWpB;YACxDiE,SAAS;QACX;QACA,IAAIC,kBAAkBf,KAAKgB,QAAQ;QAEnC,IAAId,eAAe;YACjB,IAAK,MAAMe,QAAQf,cAAe;gBAChC,IAAIgB,OAAOC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACnB,eAAee,OAAO;oBAC7D,MAAMK,IAAIpB,aAAa,CAACe,KAAK;oBAC7B,IAAIvE,QAAQ4E,EAAEL,IAAI;oBAClB,KAAK,MAAMM,WAAWD,EAAEjE,QAAQ,CAAE;wBAChCX,SAAS,CAAC,CAAC,EAAE6E,QAAQN,IAAI,EAAE;oBAC7B;oBAEAhC,QAAQ1D,IAAI,CAAC;wBACX0F;wBACAvE;oBACF;gBACF;YACF;QACF;QAEA,IAAI3C,aAAakC,IAAI,KAAK,GAAG;YAC3B,MAAMuF,cAAc,IAAI,CAACC,UAAU,CAAC;gBAClCC,gBAAgB;oBAAC;iBAAQ;gBACzBC,YAAY;oBAAC;iBAAQ;gBACrBC,WAAW,EAAE;gBACbC,YAAY,EAAE;YAChB;YAEA,KAAK,MAAM,CAAC/F,OAAOpB,IAAI,IAAIX,aAAa+H,OAAO,GAAI;gBACjD,MAAM,CAACC,SAAY,GAAGrH,IAAIM,KAAK,CAAC,UAAU;gBAE1C,MAAMgH,UAAU5I,WAAW2I,UAAU,IAAI,CAACE,WAAW;gBACrD,MAAMC,cAAc,MAAM7I,gBAAgBmI,aAAa,IAAI,CAAC3B,OAAO,EAAE;uBAChE,IAAIsC,IAAI;wBAACH;wBAAStH;qBAAI;iBAC1B;gBAED,KAAK,MAAM0H,cAAcvI,QAAS;oBAChCuI,WAAW1H,GAAG,GAAG0H,WAAW1H,GAAG,CAAC2H,OAAO,CACrC,CAAC,uCAAuC,EAAEvG,MAAM,EAAE,CAAC,EACnDoG,eAAexH;gBAEnB;YACF;QACF;QAEA,IAAIV,mBAAmBiC,IAAI,KAAK,GAAG;YACjC,MAAMqG,iBAAiB,IAAI,CAACb,UAAU,CAAC;gBACrCC,gBAAgB;oBAAC;iBAAQ;gBACzBG,YAAY;oBAAC;iBAAO;gBACpBF,YAAY;oBAAC;oBAAO;oBAAS;oBAAQ;iBAAM;gBAC3CC,WAAW;oBAAC;oBAAS;iBAAM;gBAC3BW,cAAc;oBAAC;iBAAU;YAC3B;YAEA,KAAK,MAAM,CAACzG,OAAOpB,IAAI,IAAIV,mBAAmB8H,OAAO,GAAI;gBACvD,MAAM,CAACC,SAAY,GAAGrH,IAAIM,KAAK,CAAC,UAAU;gBAE1C,MAAMgH,UAAU5I,WAAW2I,UAAU,IAAI,CAACE,WAAW;gBACrD,MAAMC,cAAc,MAAM7I,gBACxBiJ,gBACA,IAAI,CAACzC,OAAO,EACZ;uBAAI,IAAIsC,IAAI;wBAACH;wBAAStH;qBAAI;iBAAE;gBAG9B,KAAK,MAAM0H,cAAcvI,QAAS;oBAChCuI,WAAW1H,GAAG,GAAG0H,WAAW1H,GAAG,CAAC2H,OAAO,CACrC,CAAC,8CAA8C,EAAEvG,MAAM,EAAE,CAAC,EAC1DoG,eAAexH;gBAEnB;YACF;QACF;QACA,IAAI+E,iBAAiBxD,IAAI,KAAK,GAAG;YAC/B,MAAMuG,eAAe,IAAI,CAACf,UAAU,CAAC;gBACnCC,gBAAgB;oBAAC;iBAAQ;gBACzBG,YAAY,EAAE;gBACdF,YAAY;oBAAC;oBAAO;oBAAS;oBAAQ;iBAAM;gBAC3CC,WAAW;oBAAC;oBAAS;iBAAM;YAC7B;YAEA,KAAK,MAAM,CAAC9F,OAAOpB,IAAI,IAAI+E,iBAAiBqC,OAAO,GAAI;gBACrD,MAAM,CAACC,SAAY,GAAGrH,IAAIM,KAAK,CAAC,UAAU;gBAE1C,MAAMgH,UAAU5I,WAAW2I,UAAU,IAAI,CAACE,WAAW;gBACrD,MAAMC,cAAc,MAAM7I,gBAAgBmJ,cAAc,IAAI,CAAC3C,OAAO,EAAE;uBACjE,IAAIsC,IAAI;wBAACzH;wBAAKsH;qBAAQ;iBAC1B;gBAED,KAAK,MAAMI,cAAclD,YAAa;oBACpCkD,WAAW1H,GAAG,GAAG0H,WAAW1H,GAAG,CAAC2H,OAAO,CACrC,CAAC,4CAA4C,EAAEvG,MAAM,EAAE,CAAC,EACxDoG,eAAexH;gBAEnB;YACF;QACF;QAEAb,QAAQ0B,IAAI,IAAI2D;QAEhB,MAAMuD,aAAa5J,cAAcgB,SAASqE;QAC1C,MAAMwE,aAAa5J,cACjB;YAAEyG,KAAKwB;YAAiBd;QAAI,GAC5BrG,MACAE,cACAoE,SACA,IAAI;QAEN,MAAMyE,aAAa5J,cAAckG,SAASnF,cAAcoE;QAExD,MAAM0E,SAAS,GAAGH,aAAaC,aAAaC,YAAY;QAExDxE,KAAK,MAAMyE,QAAQ3C,OAAOvE,KAAKmH,KAAK,CAAC5C,IAAIe,QAAQ;IACnD,EAAE,OAAO8B,OAAgB;QACvBC,QAAQD,KAAK,CAAC,6BAA6BA;QAC3C3E,KAAK2E;IACP;AACF;AAEA,OAAO,MAAME,MAAM,KAAI"}