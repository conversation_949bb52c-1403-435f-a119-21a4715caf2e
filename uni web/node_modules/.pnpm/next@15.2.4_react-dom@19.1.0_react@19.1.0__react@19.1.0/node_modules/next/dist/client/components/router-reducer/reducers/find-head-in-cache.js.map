{"version": 3, "sources": ["../../../../../src/client/components/router-reducer/reducers/find-head-in-cache.ts"], "sourcesContent": ["import type { FlightRouterState } from '../../../../server/app-render/types'\nimport type { CacheNode } from '../../../../shared/lib/app-router-context.shared-runtime'\nimport { createRouterCacheKey } from '../create-router-cache-key'\n\nexport function findHeadInCache(\n  cache: CacheNode,\n  parallelRoutes: FlightRouterState[1]\n): [CacheNode, string] | null {\n  return findHeadInCacheImpl(cache, parallelRoutes, '')\n}\n\nfunction findHeadInCacheImpl(\n  cache: CacheNode,\n  parallelRoutes: FlightRouterState[1],\n  keyPrefix: string\n): [CacheNode, string] | null {\n  const isLastItem = Object.keys(parallelRoutes).length === 0\n  if (isLastItem) {\n    // Returns the entire Cache Node of the segment whose head we will render.\n    return [cache, keyPrefix]\n  }\n\n  // First try the 'children' parallel route if it exists\n  // when starting from the \"root\", this corresponds with the main page component\n  if (parallelRoutes.children) {\n    const [segment, childParallelRoutes] = parallelRoutes.children\n    const childSegmentMap = cache.parallelRoutes.get('children')\n    if (childSegmentMap) {\n      const cacheKey = createRouterCacheKey(segment)\n      const cacheNode = childSegmentMap.get(cacheKey)\n      if (cacheNode) {\n        const item = findHeadInCacheImpl(\n          cacheNode,\n          childParallelRoutes,\n          keyPrefix + '/' + cacheKey\n        )\n        if (item) return item\n      }\n    }\n  }\n\n  // if we didn't find metadata in the page slot, check the other parallel routes\n  for (const key in parallelRoutes) {\n    if (key === 'children') continue // already checked above\n\n    const [segment, childParallelRoutes] = parallelRoutes[key]\n    const childSegmentMap = cache.parallelRoutes.get(key)\n    if (!childSegmentMap) {\n      continue\n    }\n\n    const cacheKey = createRouterCacheKey(segment)\n\n    const cacheNode = childSegmentMap.get(cacheKey)\n    if (!cacheNode) {\n      continue\n    }\n\n    const item = findHeadInCacheImpl(\n      cacheNode,\n      childParallelRoutes,\n      keyPrefix + '/' + cacheKey\n    )\n    if (item) {\n      return item\n    }\n  }\n\n  return null\n}\n"], "names": ["findHeadInCache", "cache", "parallelRoutes", "findHeadInCacheImpl", "keyPrefix", "isLastItem", "Object", "keys", "length", "children", "segment", "childPara<PERSON>l<PERSON><PERSON><PERSON>", "childSegmentMap", "get", "cache<PERSON>ey", "createRouterCache<PERSON>ey", "cacheNode", "item", "key"], "mappings": ";;;;+BAIg<PERSON>;;;eAAAA;;;sCAFqB;AAE9B,SAASA,gBACdC,KAAgB,EAChBC,cAAoC;IAEpC,OAAOC,oBAAoBF,OAAOC,gBAAgB;AACpD;AAEA,SAASC,oBACPF,KAAgB,EAChBC,cAAoC,EACpCE,SAAiB;IAEjB,MAAMC,aAAaC,OAAOC,IAAI,CAACL,gBAAgBM,MAAM,KAAK;IAC1D,IAAIH,YAAY;QACd,0EAA0E;QAC1E,OAAO;YAACJ;YAAOG;SAAU;IAC3B;IAEA,uDAAuD;IACvD,+EAA+E;IAC/E,IAAIF,eAAeO,QAAQ,EAAE;QAC3B,MAAM,CAACC,SAASC,oBAAoB,GAAGT,eAAeO,QAAQ;QAC9D,MAAMG,kBAAkBX,MAAMC,cAAc,CAACW,GAAG,CAAC;QACjD,IAAID,iBAAiB;YACnB,MAAME,WAAWC,IAAAA,0CAAoB,EAACL;YACtC,MAAMM,YAAYJ,gBAAgBC,GAAG,CAACC;YACtC,IAAIE,WAAW;gBACb,MAAMC,OAAOd,oBACXa,WACAL,qBACAP,YAAY,MAAMU;gBAEpB,IAAIG,MAAM,OAAOA;YACnB;QACF;IACF;IAEA,+EAA+E;IAC/E,IAAK,MAAMC,OAAOhB,eAAgB;QAChC,IAAIgB,QAAQ,YAAY,UAAS,wBAAwB;QAEzD,MAAM,CAACR,SAASC,oBAAoB,GAAGT,cAAc,CAACgB,IAAI;QAC1D,MAAMN,kBAAkBX,MAAMC,cAAc,CAACW,GAAG,CAACK;QACjD,IAAI,CAACN,iBAAiB;YACpB;QACF;QAEA,MAAME,WAAWC,IAAAA,0CAAoB,EAACL;QAEtC,MAAMM,YAAYJ,gBAAgBC,GAAG,CAACC;QACtC,IAAI,CAACE,WAAW;YACd;QACF;QAEA,MAAMC,OAAOd,oBACXa,WACAL,qBACAP,YAAY,MAAMU;QAEpB,IAAIG,MAAM;YACR,OAAOA;QACT;IACF;IAEA,OAAO;AACT"}