type CursorState = "horizontal" | "intersection" | "vertical";
export declare function disableGlobalCursorStyles(): void;
export declare function enableGlobalCursorStyles(): void;
export declare function getCursorStyle(state: CursorState, constraintFlags: number): string;
export declare function resetGlobalCursorStyle(): void;
export declare function setGlobalCursorStyle(state: CursorState, constraintFlags: number): void;
export {};
