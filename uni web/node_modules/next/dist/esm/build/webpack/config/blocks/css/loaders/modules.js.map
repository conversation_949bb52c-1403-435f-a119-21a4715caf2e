{"version": 3, "sources": ["../../../../../../../src/build/webpack/config/blocks/css/loaders/modules.ts"], "sourcesContent": ["import type { webpack } from 'next/dist/compiled/webpack/webpack'\nimport type { ConfigurationContext } from '../../../utils'\nimport { getClientStyleLoader } from './client'\nimport { cssFileResolve } from './file-resolve'\nimport { getCssModuleLocalIdent } from './getCssModuleLocalIdent'\n\nexport function getCssModuleLoader(\n  ctx: ConfigurationContext,\n  postcss: any,\n  preProcessors: readonly webpack.RuleSetUseItem[] = []\n): webpack.RuleSetUseItem[] {\n  const loaders: webpack.RuleSetUseItem[] = []\n\n  if (ctx.isClient) {\n    // Add appropriate development more or production mode style\n    // loader\n    loaders.push(\n      getClientStyleLoader({\n        hasAppDir: ctx.hasAppDir,\n        isAppDir: ctx.isAppDir,\n        isDevelopment: ctx.isDevelopment,\n        assetPrefix: ctx.assetPrefix,\n      })\n    )\n  }\n\n  if (ctx.experimental.useLightningcss) {\n    loaders.push({\n      loader: require.resolve('../../../../loaders/lightningcss-loader/src'),\n      options: {\n        importLoaders: 1 + preProcessors.length,\n        url: (url: string, resourcePath: string) =>\n          cssFileResolve(url, resourcePath, ctx.experimental.urlImports),\n        import: (url: string, _: any, resourcePath: string) =>\n          cssFileResolve(url, resourcePath, ctx.experimental.urlImports),\n        modules: {\n          // Do not transform class names (CJS mode backwards compatibility):\n          exportLocalsConvention: 'asIs',\n          // Server-side (Node.js) rendering support:\n          exportOnlyLocals: ctx.isServer,\n        },\n        targets: ctx.supportedBrowsers,\n        postcss,\n      },\n    })\n  } else {\n    // Resolve CSS `@import`s and `url()`s\n    loaders.push({\n      loader: require.resolve('../../../../loaders/css-loader/src'),\n      options: {\n        postcss,\n        importLoaders: 1 + preProcessors.length,\n        // Use CJS mode for backwards compatibility:\n        esModule: false,\n        url: (url: string, resourcePath: string) =>\n          cssFileResolve(url, resourcePath, ctx.experimental.urlImports),\n        import: (url: string, _: any, resourcePath: string) =>\n          cssFileResolve(url, resourcePath, ctx.experimental.urlImports),\n        modules: {\n          // Do not transform class names (CJS mode backwards compatibility):\n          exportLocalsConvention: 'asIs',\n          // Server-side (Node.js) rendering support:\n          exportOnlyLocals: ctx.isServer,\n          // Disallow global style exports so we can code-split CSS and\n          // not worry about loading order.\n          mode: 'pure',\n          // Generate a friendly production-ready name so it's\n          // reasonably understandable. The same name is used for\n          // development.\n          // TODO: Consider making production reduce this to a single\n          // character?\n          getLocalIdent: getCssModuleLocalIdent,\n        },\n      },\n    })\n\n    // Compile CSS\n    loaders.push({\n      loader: require.resolve('../../../../loaders/postcss-loader/src'),\n      options: {\n        postcss,\n      },\n    })\n  }\n\n  loaders.push(\n    // Webpack loaders run like a stack, so we need to reverse the natural\n    // order of preprocessors.\n    ...preProcessors.slice().reverse()\n  )\n\n  return loaders\n}\n"], "names": ["getClientStyleLoader", "cssFileResolve", "getCssModuleLocalIdent", "getCssModuleLoader", "ctx", "postcss", "preProcessors", "loaders", "isClient", "push", "hasAppDir", "isAppDir", "isDevelopment", "assetPrefix", "experimental", "useLightningcss", "loader", "require", "resolve", "options", "importLoaders", "length", "url", "resourcePath", "urlImports", "import", "_", "modules", "exportLocalsConvention", "exportOnlyLocals", "isServer", "targets", "supportedBrowsers", "esModule", "mode", "getLocalIdent", "slice", "reverse"], "mappings": "AAEA,SAASA,oBAAoB,QAAQ,WAAU;AAC/C,SAASC,cAAc,QAAQ,iBAAgB;AAC/C,SAASC,sBAAsB,QAAQ,2BAA0B;AAEjE,OAAO,SAASC,mBACdC,GAAyB,EACzBC,OAAY,EACZC,gBAAmD,EAAE;IAErD,MAAMC,UAAoC,EAAE;IAE5C,IAAIH,IAAII,QAAQ,EAAE;QAChB,4DAA4D;QAC5D,SAAS;QACTD,QAAQE,IAAI,CACVT,qBAAqB;YACnBU,WAAWN,IAAIM,SAAS;YACxBC,UAAUP,IAAIO,QAAQ;YACtBC,eAAeR,IAAIQ,aAAa;YAChCC,aAAaT,IAAIS,WAAW;QAC9B;IAEJ;IAEA,IAAIT,IAAIU,YAAY,CAACC,eAAe,EAAE;QACpCR,QAAQE,IAAI,CAAC;YACXO,QAAQC,QAAQC,OAAO,CAAC;YACxBC,SAAS;gBACPC,eAAe,IAAId,cAAce,MAAM;gBACvCC,KAAK,CAACA,KAAaC,eACjBtB,eAAeqB,KAAKC,cAAcnB,IAAIU,YAAY,CAACU,UAAU;gBAC/DC,QAAQ,CAACH,KAAaI,GAAQH,eAC5BtB,eAAeqB,KAAKC,cAAcnB,IAAIU,YAAY,CAACU,UAAU;gBAC/DG,SAAS;oBACP,mEAAmE;oBACnEC,wBAAwB;oBACxB,2CAA2C;oBAC3CC,kBAAkBzB,IAAI0B,QAAQ;gBAChC;gBACAC,SAAS3B,IAAI4B,iBAAiB;gBAC9B3B;YACF;QACF;IACF,OAAO;QACL,sCAAsC;QACtCE,QAAQE,IAAI,CAAC;YACXO,QAAQC,QAAQC,OAAO,CAAC;YACxBC,SAAS;gBACPd;gBACAe,eAAe,IAAId,cAAce,MAAM;gBACvC,4CAA4C;gBAC5CY,UAAU;gBACVX,KAAK,CAACA,KAAaC,eACjBtB,eAAeqB,KAAKC,cAAcnB,IAAIU,YAAY,CAACU,UAAU;gBAC/DC,QAAQ,CAACH,KAAaI,GAAQH,eAC5BtB,eAAeqB,KAAKC,cAAcnB,IAAIU,YAAY,CAACU,UAAU;gBAC/DG,SAAS;oBACP,mEAAmE;oBACnEC,wBAAwB;oBACxB,2CAA2C;oBAC3CC,kBAAkBzB,IAAI0B,QAAQ;oBAC9B,6DAA6D;oBAC7D,iCAAiC;oBACjCI,MAAM;oBACN,oDAAoD;oBACpD,uDAAuD;oBACvD,eAAe;oBACf,2DAA2D;oBAC3D,aAAa;oBACbC,eAAejC;gBACjB;YACF;QACF;QAEA,cAAc;QACdK,QAAQE,IAAI,CAAC;YACXO,QAAQC,QAAQC,OAAO,CAAC;YACxBC,SAAS;gBACPd;YACF;QACF;IACF;IAEAE,QAAQE,IAAI,CACV,sEAAsE;IACtE,0BAA0B;OACvBH,cAAc8B,KAAK,GAAGC,OAAO;IAGlC,OAAO9B;AACT"}