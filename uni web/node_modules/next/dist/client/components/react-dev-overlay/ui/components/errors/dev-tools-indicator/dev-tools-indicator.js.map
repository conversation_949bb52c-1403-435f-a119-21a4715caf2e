{"version": 3, "sources": ["../../../../../../../../src/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-indicator.tsx"], "sourcesContent": ["import type { CSSProperties, Dispatch, SetStateAction } from 'react'\nimport { STORAGE_KEY_POSITION, type OverlayState } from '../../../../shared'\n\nimport { useState, useEffect, useRef, createContext, useContext } from 'react'\nimport { Toast } from '../../toast'\nimport { NextLogo } from './next-logo'\nimport { useIsDevBuilding } from '../../../../../../dev/dev-build-indicator/internal/initialize'\nimport { useIsDevRendering } from '../../../../utils/dev-indicator/dev-render-indicator'\nimport { useDelayedRender } from '../../../hooks/use-delayed-render'\nimport { TurbopackInfo } from './dev-tools-info/turbopack-info'\nimport { RouteInfo } from './dev-tools-info/route-info'\nimport GearIcon from '../../../icons/gear-icon'\nimport { UserPreferences } from './dev-tools-info/user-preferences'\nimport {\n  MENU_CURVE,\n  MENU_DURATION_MS,\n  useClickOutside,\n  useFocusTrap,\n} from './utils'\n\n// TODO: add E2E tests to cover different scenarios\n\nconst INDICATOR_POSITION =\n  (process.env\n    .__NEXT_DEV_INDICATOR_POSITION as typeof window.__NEXT_DEV_INDICATOR_POSITION) ||\n  'bottom-left'\n\nexport type DevToolsIndicatorPosition = typeof INDICATOR_POSITION\n\nexport function DevToolsIndicator({\n  state,\n  errorCount,\n  isBuildError,\n  setIsErrorOverlayOpen,\n}: {\n  state: OverlayState\n  errorCount: number\n  isBuildError: boolean\n  setIsErrorOverlayOpen: (\n    isErrorOverlayOpen: boolean | ((prev: boolean) => boolean)\n  ) => void\n}) {\n  const [isDevToolsIndicatorVisible, setIsDevToolsIndicatorVisible] =\n    useState(true)\n\n  return (\n    <DevToolsPopover\n      routerType={state.routerType}\n      semver={state.versionInfo.installed}\n      issueCount={errorCount}\n      isStaticRoute={state.staticIndicator}\n      hide={() => {\n        setIsDevToolsIndicatorVisible(false)\n        fetch('/__nextjs_disable_dev_indicator', {\n          method: 'POST',\n        })\n      }}\n      setIsErrorOverlayOpen={setIsErrorOverlayOpen}\n      isTurbopack={!!process.env.TURBOPACK}\n      disabled={state.disableDevIndicator || !isDevToolsIndicatorVisible}\n      isBuildError={isBuildError}\n    />\n  )\n}\n\n//////////////////////////////////////////////////////////////////////////////////////\n\ninterface C {\n  closeMenu: () => void\n  selectedIndex: number\n  setSelectedIndex: Dispatch<SetStateAction<number>>\n}\n\nconst Context = createContext({} as C)\n\nfunction getInitialPosition() {\n  if (\n    typeof localStorage !== 'undefined' &&\n    localStorage.getItem(STORAGE_KEY_POSITION)\n  ) {\n    return localStorage.getItem(\n      STORAGE_KEY_POSITION\n    ) as DevToolsIndicatorPosition\n  }\n\n  return INDICATOR_POSITION\n}\n\nconst OVERLAYS = {\n  Root: 'root',\n  Turbo: 'turbo',\n  Route: 'route',\n  Preferences: 'preferences',\n} as const\n\nexport type Overlays = (typeof OVERLAYS)[keyof typeof OVERLAYS]\n\nfunction DevToolsPopover({\n  routerType,\n  disabled,\n  issueCount,\n  isStaticRoute,\n  isTurbopack,\n  isBuildError,\n  hide,\n  setIsErrorOverlayOpen,\n}: {\n  routerType: 'pages' | 'app'\n  disabled: boolean\n  issueCount: number\n  isStaticRoute: boolean\n  semver: string | undefined\n  isTurbopack: boolean\n  isBuildError: boolean\n  hide: () => void\n  setIsErrorOverlayOpen: (\n    isOverlayOpen: boolean | ((prev: boolean) => boolean)\n  ) => void\n}) {\n  const menuRef = useRef<HTMLDivElement>(null)\n  const triggerRef = useRef<HTMLButtonElement | null>(null)\n\n  const [open, setOpen] = useState<Overlays | null>(null)\n  const [position, setPosition] = useState(getInitialPosition())\n  const [selectedIndex, setSelectedIndex] = useState(-1)\n\n  const isMenuOpen = open === OVERLAYS.Root\n  const isTurbopackInfoOpen = open === OVERLAYS.Turbo\n  const isRouteInfoOpen = open === OVERLAYS.Route\n  const isPreferencesOpen = open === OVERLAYS.Preferences\n\n  const { mounted: menuMounted, rendered: menuRendered } = useDelayedRender(\n    isMenuOpen,\n    {\n      // Intentionally no fade in, makes the UI feel more immediate\n      enterDelay: 0,\n      // Graceful fade out to confirm that the UI did not break\n      exitDelay: MENU_DURATION_MS,\n    }\n  )\n\n  // Features to make the menu accessible\n  useFocusTrap(menuRef, triggerRef, isMenuOpen)\n  useClickOutside(menuRef, triggerRef, isMenuOpen, closeMenu)\n\n  useEffect(() => {\n    if (open === null) {\n      // Avoid flashing selected state\n      const id = setTimeout(() => {\n        setSelectedIndex(-1)\n      }, MENU_DURATION_MS)\n      return () => clearTimeout(id)\n    }\n  }, [open])\n\n  function select(index: number | 'first' | 'last') {\n    if (index === 'first') {\n      setTimeout(() => {\n        const all = menuRef.current?.querySelectorAll('[role=\"menuitem\"]')\n        if (all) {\n          const firstIndex = all[0].getAttribute('data-index')\n          select(Number(firstIndex))\n        }\n      })\n      return\n    }\n\n    if (index === 'last') {\n      setTimeout(() => {\n        const all = menuRef.current?.querySelectorAll('[role=\"menuitem\"]')\n        if (all) {\n          const lastIndex = all.length - 1\n          select(lastIndex)\n        }\n      })\n      return\n    }\n\n    const el = menuRef.current?.querySelector(\n      `[data-index=\"${index}\"]`\n    ) as HTMLElement\n\n    if (el) {\n      setSelectedIndex(index)\n      el?.focus()\n    }\n  }\n\n  function onMenuKeydown(e: React.KeyboardEvent<HTMLDivElement>) {\n    e.preventDefault()\n\n    switch (e.key) {\n      case 'ArrowDown':\n        const next = selectedIndex + 1\n        select(next)\n        break\n      case 'ArrowUp':\n        const prev = selectedIndex - 1\n        select(prev)\n        break\n      case 'Home':\n        select('first')\n        break\n      case 'End':\n        select('last')\n        break\n      default:\n        break\n    }\n  }\n\n  function openErrorOverlay() {\n    setOpen(null)\n    if (issueCount > 0) {\n      setIsErrorOverlayOpen(true)\n    }\n  }\n\n  function toggleErrorOverlay() {\n    setIsErrorOverlayOpen((prev) => !prev)\n  }\n\n  function openRootMenu() {\n    setOpen((prevOpen) => {\n      if (prevOpen === null) select('first')\n      return OVERLAYS.Root\n    })\n  }\n\n  function onTriggerClick() {\n    if (open === OVERLAYS.Root) {\n      setOpen(null)\n    } else {\n      openRootMenu()\n      setTimeout(() => {\n        select('first')\n      })\n    }\n  }\n\n  function closeMenu() {\n    // Only close when we were on `Root`,\n    // otherwise it will close other overlays\n    setOpen((prevOpen) => {\n      if (prevOpen === OVERLAYS.Root) {\n        return null\n      }\n      return prevOpen\n    })\n  }\n\n  function handleHideDevtools() {\n    setOpen(null)\n    hide()\n  }\n\n  const [vertical, horizontal] = position.split('-', 2)\n  const popover = { [vertical]: 'calc(100% + 8px)', [horizontal]: 0 }\n\n  return (\n    <Toast\n      data-nextjs-toast\n      style={\n        {\n          '--animate-out-duration-ms': `${MENU_DURATION_MS}ms`,\n          '--animate-out-timing-function': MENU_CURVE,\n          boxShadow: 'none',\n          zIndex: 2147483647,\n          // Reset the toast component's default positions.\n          bottom: 'initial',\n          left: 'initial',\n          [vertical]: '20px',\n          [horizontal]: '20px',\n        } as CSSProperties\n      }\n    >\n      {/* Trigger */}\n      <NextLogo\n        ref={triggerRef}\n        aria-haspopup=\"menu\"\n        aria-expanded={isMenuOpen}\n        aria-controls=\"nextjs-dev-tools-menu\"\n        aria-label={`${isMenuOpen ? 'Close' : 'Open'} Next.js Dev Tools`}\n        data-nextjs-dev-tools-button\n        disabled={disabled}\n        issueCount={issueCount}\n        onTriggerClick={onTriggerClick}\n        toggleErrorOverlay={toggleErrorOverlay}\n        isDevBuilding={useIsDevBuilding()}\n        isDevRendering={useIsDevRendering()}\n        isBuildError={isBuildError}\n      />\n\n      {/* Route Info */}\n      <RouteInfo\n        isOpen={isRouteInfoOpen}\n        close={openRootMenu}\n        triggerRef={triggerRef}\n        style={popover}\n        routerType={routerType}\n        routeType={isStaticRoute ? 'Static' : 'Dynamic'}\n      />\n\n      {/* Turbopack Info */}\n      <TurbopackInfo\n        isOpen={isTurbopackInfoOpen}\n        close={openRootMenu}\n        triggerRef={triggerRef}\n        style={popover}\n      />\n\n      {/* Preferences */}\n      <UserPreferences\n        isOpen={isPreferencesOpen}\n        close={openRootMenu}\n        triggerRef={triggerRef}\n        style={popover}\n        hide={handleHideDevtools}\n        setPosition={setPosition}\n        position={position}\n      />\n\n      {/* Dropdown Menu */}\n      {menuMounted && (\n        <div\n          ref={menuRef}\n          id=\"nextjs-dev-tools-menu\"\n          role=\"menu\"\n          dir=\"ltr\"\n          aria-orientation=\"vertical\"\n          aria-label=\"Next.js Dev Tools Items\"\n          tabIndex={-1}\n          className=\"dev-tools-indicator-menu\"\n          onKeyDown={onMenuKeydown}\n          data-rendered={menuRendered}\n          style={popover}\n        >\n          <Context.Provider\n            value={{\n              closeMenu,\n              selectedIndex,\n              setSelectedIndex,\n            }}\n          >\n            <div className=\"dev-tools-indicator-inner\">\n              {issueCount > 0 && (\n                <MenuItem\n                  title={`${issueCount} ${issueCount === 1 ? 'issue' : 'issues'} found. Click to view details in the dev overlay.`}\n                  index={0}\n                  label=\"Issues\"\n                  value={<IssueCount>{issueCount}</IssueCount>}\n                  onClick={openErrorOverlay}\n                />\n              )}\n              <MenuItem\n                title={`Current route is ${isStaticRoute ? 'static' : 'dynamic'}.`}\n                label=\"Route\"\n                index={1}\n                value={isStaticRoute ? 'Static' : 'Dynamic'}\n                onClick={() => setOpen(OVERLAYS.Route)}\n                data-nextjs-route-type={isStaticRoute ? 'static' : 'dynamic'}\n              />\n              {isTurbopack ? (\n                <MenuItem\n                  title=\"Turbopack is enabled.\"\n                  label=\"Turbopack\"\n                  value=\"Enabled\"\n                />\n              ) : (\n                <MenuItem\n                  index={2}\n                  title=\"Learn about Turbopack and how to enable it in your application.\"\n                  label=\"Try Turbopack\"\n                  value={<ChevronRight />}\n                  onClick={() => setOpen(OVERLAYS.Turbo)}\n                />\n              )}\n            </div>\n\n            <div className=\"dev-tools-indicator-footer\">\n              <MenuItem\n                data-preferences\n                label=\"Preferences\"\n                value={<GearIcon />}\n                onClick={() => setOpen(OVERLAYS.Preferences)}\n                index={isTurbopack ? 2 : 3}\n              />\n            </div>\n          </Context.Provider>\n        </div>\n      )}\n    </Toast>\n  )\n}\n\nfunction ChevronRight() {\n  return (\n    <svg\n      xmlns=\"http://www.w3.org/2000/svg\"\n      width=\"16\"\n      height=\"16\"\n      viewBox=\"0 0 16 16\"\n      fill=\"none\"\n    >\n      <path\n        fill=\"#666\"\n        fillRule=\"evenodd\"\n        clipRule=\"evenodd\"\n        d=\"M5.50011 1.93945L6.03044 2.46978L10.8537 7.293C11.2442 7.68353 11.2442 8.31669 10.8537 8.70722L6.03044 13.5304L5.50011 14.0608L4.43945 13.0001L4.96978 12.4698L9.43945 8.00011L4.96978 3.53044L4.43945 3.00011L5.50011 1.93945Z\"\n      />\n    </svg>\n  )\n}\n\nfunction MenuItem({\n  index,\n  label,\n  value,\n  onClick,\n  href,\n  ...props\n}: {\n  index?: number\n  title?: string\n  label: string\n  value: React.ReactNode\n  href?: string\n  onClick?: () => void\n}) {\n  const isInteractive =\n    typeof onClick === 'function' || typeof href === 'string'\n  const { closeMenu, selectedIndex, setSelectedIndex } = useContext(Context)\n  const selected = selectedIndex === index\n\n  function click() {\n    if (isInteractive) {\n      onClick?.()\n      closeMenu()\n      if (href) {\n        window.open(href, '_blank', 'noopener, noreferrer')\n      }\n    }\n  }\n\n  return (\n    <div\n      className=\"dev-tools-indicator-item\"\n      data-index={index}\n      data-selected={selected}\n      onClick={click}\n      // Needs `onMouseMove` instead of enter to work together\n      // with keyboard and mouse input\n      onMouseMove={() => {\n        if (isInteractive && index !== undefined && selectedIndex !== index) {\n          setSelectedIndex(index)\n        }\n      }}\n      onMouseLeave={() => setSelectedIndex(-1)}\n      onKeyDown={(e) => {\n        if (e.key === 'Enter' || e.key === ' ') {\n          click()\n        }\n      }}\n      role={isInteractive ? 'menuitem' : undefined}\n      tabIndex={selected ? 0 : -1}\n      {...props}\n    >\n      <span className=\"dev-tools-indicator-label\">{label}</span>\n      <span className=\"dev-tools-indicator-value\">{value}</span>\n    </div>\n  )\n}\n\nfunction IssueCount({ children }: { children: number }) {\n  return (\n    <span\n      className=\"dev-tools-indicator-issue-count\"\n      data-has-issues={children > 0}\n    >\n      <span className=\"dev-tools-indicator-issue-count-indicator\" />\n      {children}\n    </span>\n  )\n}\n\n//////////////////////////////////////////////////////////////////////////////////////\n\nexport const DEV_TOOLS_INDICATOR_STYLES = `\n  .dev-tools-indicator-menu {\n    -webkit-font-smoothing: antialiased;\n    display: flex;\n    flex-direction: column;\n    align-items: flex-start;\n    background: var(--color-background-100);\n    border: 1px solid var(--color-gray-alpha-400);\n    background-clip: padding-box;\n    box-shadow: var(--shadow-menu);\n    border-radius: var(--rounded-xl);\n    position: absolute;\n    font-family: var(--font-stack-sans);\n    z-index: 1000;\n    overflow: hidden;\n    opacity: 0;\n    outline: 0;\n    min-width: 248px;\n    transition: opacity var(--animate-out-duration-ms)\n      var(--animate-out-timing-function);\n\n    &[data-rendered='true'] {\n      opacity: 1;\n      scale: 1;\n    }\n  }\n\n  .dev-tools-indicator-inner {\n    padding: 6px;\n    width: 100%;\n  }\n\n  .dev-tools-indicator-item {\n    display: flex;\n    align-items: center;\n    padding: 8px 6px;\n    height: var(--size-36);\n    border-radius: 6px;\n    text-decoration: none !important;\n    user-select: none;\n    white-space: nowrap;\n\n    svg {\n      width: var(--size-16);\n      height: var(--size-16);\n    }\n\n    &:focus-visible {\n      outline: 0;\n    }\n  }\n\n  .dev-tools-indicator-footer {\n    background: var(--color-background-200);\n    padding: 6px;\n    border-top: 1px solid var(--color-gray-400);\n    width: 100%;\n  }\n\n  .dev-tools-indicator-item[data-selected='true'] {\n    cursor: pointer;\n    background-color: var(--color-gray-200);\n  }\n\n  .dev-tools-indicator-label {\n    font-size: var(--size-14);\n    line-height: var(--size-20);\n    color: var(--color-gray-1000);\n  }\n\n  .dev-tools-indicator-value {\n    font-size: var(--size-14);\n    line-height: var(--size-20);\n    color: var(--color-gray-900);\n    margin-left: auto;\n  }\n\n  .dev-tools-indicator-issue-count {\n    --color-primary: var(--color-gray-800);\n    --color-secondary: var(--color-gray-100);\n    display: flex;\n    flex-direction: row;\n    align-items: center;\n    justify-content: center;\n    gap: 8px;\n    min-width: var(--size-40);\n    height: var(--size-24);\n    background: var(--color-background-100);\n    border: 1px solid var(--color-gray-alpha-400);\n    background-clip: padding-box;\n    box-shadow: var(--shadow-small);\n    padding: 2px;\n    color: var(--color-gray-1000);\n    border-radius: 128px;\n    font-weight: 500;\n    font-size: var(--size-13);\n    font-variant-numeric: tabular-nums;\n\n    &[data-has-issues='true'] {\n      --color-primary: var(--color-red-800);\n      --color-secondary: var(--color-red-100);\n    }\n\n    .dev-tools-indicator-issue-count-indicator {\n      width: var(--size-8);\n      height: var(--size-8);\n      background: var(--color-primary);\n      box-shadow: 0 0 0 2px var(--color-secondary);\n      border-radius: 50%;\n    }\n  }\n\n  .dev-tools-indicator-shortcut {\n    display: flex;\n    gap: 4px;\n\n    kbd {\n      width: var(--size-20);\n      height: var(--size-20);\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      border-radius: var(--rounded-md);\n      border: 1px solid var(--color-gray-400);\n      font-family: var(--font-stack-sans);\n      background: var(--color-background-100);\n      color: var(--color-gray-1000);\n      text-align: center;\n      font-size: var(--size-12);\n      line-height: var(--size-16);\n    }\n  }\n`\n"], "names": ["DEV_TOOLS_INDICATOR_STYLES", "DevToolsIndicator", "INDICATOR_POSITION", "process", "env", "__NEXT_DEV_INDICATOR_POSITION", "state", "errorCount", "isBuildError", "setIsErrorOverlayOpen", "isDevToolsIndicatorVisible", "setIsDevToolsIndicatorVisible", "useState", "DevToolsPopover", "routerType", "semver", "versionInfo", "installed", "issueCount", "isStaticRoute", "staticIndicator", "hide", "fetch", "method", "isTurbopack", "TURBOPACK", "disabled", "disableDevIndicator", "Context", "createContext", "getInitialPosition", "localStorage", "getItem", "STORAGE_KEY_POSITION", "OVERLAYS", "Root", "Turbo", "Route", "Preferences", "menuRef", "useRef", "triggerRef", "open", "<PERSON><PERSON><PERSON>", "position", "setPosition", "selectedIndex", "setSelectedIndex", "isMenuOpen", "isTurbopackInfoOpen", "isRouteInfoOpen", "isPreferencesOpen", "mounted", "menuMounted", "rendered", "menuRendered", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enterDelay", "exitDelay", "MENU_DURATION_MS", "useFocusTrap", "useClickOutside", "closeMenu", "useEffect", "id", "setTimeout", "clearTimeout", "select", "index", "all", "current", "querySelectorAll", "firstIndex", "getAttribute", "Number", "lastIndex", "length", "el", "querySelector", "focus", "onMenuKeydown", "e", "preventDefault", "key", "next", "prev", "openErrorOverlay", "toggleError<PERSON><PERSON>lay", "openRootMenu", "prevOpen", "onTriggerClick", "handleHideDevtools", "vertical", "horizontal", "split", "popover", "Toast", "data-nextjs-toast", "style", "MENU_CURVE", "boxShadow", "zIndex", "bottom", "left", "NextLogo", "ref", "aria-haspopup", "aria-expanded", "aria-controls", "aria-label", "data-nextjs-dev-tools-button", "isDevBuilding", "useIsDevBuilding", "isDevRendering", "useIsDevRendering", "RouteInfo", "isOpen", "close", "routeType", "TurbopackInfo", "UserPreferences", "div", "role", "dir", "aria-orientation", "tabIndex", "className", "onKeyDown", "data-rendered", "Provider", "value", "MenuItem", "title", "label", "IssueCount", "onClick", "data-nextjs-route-type", "ChevronRight", "data-preferences", "GearIcon", "svg", "xmlns", "width", "height", "viewBox", "fill", "path", "fillRule", "clipRule", "d", "href", "props", "isInteractive", "useContext", "selected", "click", "window", "data-index", "data-selected", "onMouseMove", "undefined", "onMouseLeave", "span", "children", "data-has-issues"], "mappings": ";;;;;;;;;;;;;;;IAueaA,0BAA0B;eAA1BA;;IA1cGC,iBAAiB;eAAjBA;;;;;wBA5BwC;uBAEe;uBACjD;0BACG;4BACQ;oCACC;kCACD;+BACH;2BACJ;mEACL;iCACW;uBAMzB;AAEP,mDAAmD;AAEnD,MAAMC,qBACJ,AAACC,QAAQC,GAAG,CACTC,6BAA6B,IAChC;AAIK,SAASJ,kBAAkB,KAYjC;IAZiC,IAAA,EAChCK,KAAK,EACLC,UAAU,EACVC,YAAY,EACZC,qBAAqB,EAQtB,GAZiC;IAahC,MAAM,CAACC,4BAA4BC,8BAA8B,GAC/DC,IAAAA,eAAQ,EAAC;IAEX,qBACE,qBAACC;QACCC,YAAYR,MAAMQ,UAAU;QAC5BC,QAAQT,MAAMU,WAAW,CAACC,SAAS;QACnCC,YAAYX;QACZY,eAAeb,MAAMc,eAAe;QACpCC,MAAM;YACJV,8BAA8B;YAC9BW,MAAM,mCAAmC;gBACvCC,QAAQ;YACV;QACF;QACAd,uBAAuBA;QACvBe,aAAa,CAAC,CAACrB,QAAQC,GAAG,CAACqB,SAAS;QACpCC,UAAUpB,MAAMqB,mBAAmB,IAAI,CAACjB;QACxCF,cAAcA;;AAGpB;AAUA,MAAMoB,wBAAUC,IAAAA,oBAAa,EAAC,CAAC;AAE/B,SAASC;IACP,IACE,OAAOC,iBAAiB,eACxBA,aAAaC,OAAO,CAACC,4BAAoB,GACzC;QACA,OAAOF,aAAaC,OAAO,CACzBC,4BAAoB;IAExB;IAEA,OAAO/B;AACT;AAEA,MAAMgC,WAAW;IACfC,MAAM;IACNC,OAAO;IACPC,OAAO;IACPC,aAAa;AACf;AAIA,SAASzB,gBAAgB,KAqBxB;IArBwB,IAAA,EACvBC,UAAU,EACVY,QAAQ,EACRR,UAAU,EACVC,aAAa,EACbK,WAAW,EACXhB,YAAY,EACZa,IAAI,EACJZ,qBAAqB,EAatB,GArBwB;IAsBvB,MAAM8B,UAAUC,IAAAA,aAAM,EAAiB;IACvC,MAAMC,aAAaD,IAAAA,aAAM,EAA2B;IAEpD,MAAM,CAACE,MAAMC,QAAQ,GAAG/B,IAAAA,eAAQ,EAAkB;IAClD,MAAM,CAACgC,UAAUC,YAAY,GAAGjC,IAAAA,eAAQ,EAACkB;IACzC,MAAM,CAACgB,eAAeC,iBAAiB,GAAGnC,IAAAA,eAAQ,EAAC,CAAC;IAEpD,MAAMoC,aAAaN,SAASR,SAASC,IAAI;IACzC,MAAMc,sBAAsBP,SAASR,SAASE,KAAK;IACnD,MAAMc,kBAAkBR,SAASR,SAASG,KAAK;IAC/C,MAAMc,oBAAoBT,SAASR,SAASI,WAAW;IAEvD,MAAM,EAAEc,SAASC,WAAW,EAAEC,UAAUC,YAAY,EAAE,GAAGC,IAAAA,kCAAgB,EACvER,YACA;QACE,6DAA6D;QAC7DS,YAAY;QACZ,yDAAyD;QACzDC,WAAWC,uBAAgB;IAC7B;IAGF,uCAAuC;IACvCC,IAAAA,mBAAY,EAACrB,SAASE,YAAYO;IAClCa,IAAAA,sBAAe,EAACtB,SAASE,YAAYO,YAAYc;IAEjDC,IAAAA,gBAAS,EAAC;QACR,IAAIrB,SAAS,MAAM;YACjB,gCAAgC;YAChC,MAAMsB,KAAKC,WAAW;gBACpBlB,iBAAiB,CAAC;YACpB,GAAGY,uBAAgB;YACnB,OAAO,IAAMO,aAAaF;QAC5B;IACF,GAAG;QAACtB;KAAK;IAET,SAASyB,OAAOC,KAAgC;YAuBnC7B;QAtBX,IAAI6B,UAAU,SAAS;YACrBH,WAAW;oBACG1B;gBAAZ,MAAM8B,OAAM9B,mBAAAA,QAAQ+B,OAAO,qBAAf/B,iBAAiBgC,gBAAgB,CAAC;gBAC9C,IAAIF,KAAK;oBACP,MAAMG,aAAaH,GAAG,CAAC,EAAE,CAACI,YAAY,CAAC;oBACvCN,OAAOO,OAAOF;gBAChB;YACF;YACA;QACF;QAEA,IAAIJ,UAAU,QAAQ;YACpBH,WAAW;oBACG1B;gBAAZ,MAAM8B,OAAM9B,mBAAAA,QAAQ+B,OAAO,qBAAf/B,iBAAiBgC,gBAAgB,CAAC;gBAC9C,IAAIF,KAAK;oBACP,MAAMM,YAAYN,IAAIO,MAAM,GAAG;oBAC/BT,OAAOQ;gBACT;YACF;YACA;QACF;QAEA,MAAME,MAAKtC,mBAAAA,QAAQ+B,OAAO,qBAAf/B,iBAAiBuC,aAAa,CACvC,AAAC,kBAAeV,QAAM;QAGxB,IAAIS,IAAI;YACN9B,iBAAiBqB;YACjBS,sBAAAA,GAAIE,KAAK;QACX;IACF;IAEA,SAASC,cAAcC,CAAsC;QAC3DA,EAAEC,cAAc;QAEhB,OAAQD,EAAEE,GAAG;YACX,KAAK;gBACH,MAAMC,OAAOtC,gBAAgB;gBAC7BqB,OAAOiB;gBACP;YACF,KAAK;gBACH,MAAMC,OAAOvC,gBAAgB;gBAC7BqB,OAAOkB;gBACP;YACF,KAAK;gBACHlB,OAAO;gBACP;YACF,KAAK;gBACHA,OAAO;gBACP;YACF;gBACE;QACJ;IACF;IAEA,SAASmB;QACP3C,QAAQ;QACR,IAAIzB,aAAa,GAAG;YAClBT,sBAAsB;QACxB;IACF;IAEA,SAAS8E;QACP9E,sBAAsB,CAAC4E,OAAS,CAACA;IACnC;IAEA,SAASG;QACP7C,QAAQ,CAAC8C;YACP,IAAIA,aAAa,MAAMtB,OAAO;YAC9B,OAAOjC,SAASC,IAAI;QACtB;IACF;IAEA,SAASuD;QACP,IAAIhD,SAASR,SAASC,IAAI,EAAE;YAC1BQ,QAAQ;QACV,OAAO;YACL6C;YACAvB,WAAW;gBACTE,OAAO;YACT;QACF;IACF;IAEA,SAASL;QACP,qCAAqC;QACrC,yCAAyC;QACzCnB,QAAQ,CAAC8C;YACP,IAAIA,aAAavD,SAASC,IAAI,EAAE;gBAC9B,OAAO;YACT;YACA,OAAOsD;QACT;IACF;IAEA,SAASE;QACPhD,QAAQ;QACRtB;IACF;IAEA,MAAM,CAACuE,UAAUC,WAAW,GAAGjD,SAASkD,KAAK,CAAC,KAAK;IACnD,MAAMC,UAAU;QAAE,CAACH,SAAS,EAAE;QAAoB,CAACC,WAAW,EAAE;IAAE;IAElE,qBACE,sBAACG,YAAK;QACJC,mBAAiB;QACjBC,OACE;YACE,6BAA6B,AAAC,KAAEvC,uBAAgB,GAAC;YACjD,iCAAiCwC,iBAAU;YAC3CC,WAAW;YACXC,QAAQ;YACR,iDAAiD;YACjDC,QAAQ;YACRC,MAAM;YACN,CAACX,SAAS,EAAE;YACZ,CAACC,WAAW,EAAE;QAChB;;0BAIF,qBAACW,kBAAQ;gBACPC,KAAKhE;gBACLiE,iBAAc;gBACdC,iBAAe3D;gBACf4D,iBAAc;gBACdC,cAAY,AAAC,KAAE7D,CAAAA,aAAa,UAAU,MAAK,IAAE;gBAC7C8D,8BAA4B;gBAC5BpF,UAAUA;gBACVR,YAAYA;gBACZwE,gBAAgBA;gBAChBH,oBAAoBA;gBACpBwB,eAAeC,IAAAA,4BAAgB;gBAC/BC,gBAAgBC,IAAAA,qCAAiB;gBACjC1G,cAAcA;;0BAIhB,qBAAC2G,oBAAS;gBACRC,QAAQlE;gBACRmE,OAAO7B;gBACP/C,YAAYA;gBACZyD,OAAOH;gBACPjF,YAAYA;gBACZwG,WAAWnG,gBAAgB,WAAW;;0BAIxC,qBAACoG,4BAAa;gBACZH,QAAQnE;gBACRoE,OAAO7B;gBACP/C,YAAYA;gBACZyD,OAAOH;;0BAIT,qBAACyB,gCAAe;gBACdJ,QAAQjE;gBACRkE,OAAO7B;gBACP/C,YAAYA;gBACZyD,OAAOH;gBACP1E,MAAMsE;gBACN9C,aAAaA;gBACbD,UAAUA;;YAIXS,6BACC,qBAACoE;gBACChB,KAAKlE;gBACLyB,IAAG;gBACH0D,MAAK;gBACLC,KAAI;gBACJC,oBAAiB;gBACjBf,cAAW;gBACXgB,UAAU,CAAC;gBACXC,WAAU;gBACVC,WAAW/C;gBACXgD,iBAAezE;gBACf2C,OAAOH;0BAEP,cAAA,sBAACnE,QAAQqG,QAAQ;oBACfC,OAAO;wBACLpE;wBACAhB;wBACAC;oBACF;;sCAEA,sBAAC0E;4BAAIK,WAAU;;gCACZ5G,aAAa,mBACZ,qBAACiH;oCACCC,OAAO,AAAGlH,aAAW,MAAGA,CAAAA,eAAe,IAAI,UAAU,QAAO,IAAE;oCAC9DkD,OAAO;oCACPiE,OAAM;oCACNH,qBAAO,qBAACI;kDAAYpH;;oCACpBqH,SAASjD;;8CAGb,qBAAC6C;oCACCC,OAAO,AAAC,sBAAmBjH,CAAAA,gBAAgB,WAAW,SAAQ,IAAE;oCAChEkH,OAAM;oCACNjE,OAAO;oCACP8D,OAAO/G,gBAAgB,WAAW;oCAClCoH,SAAS,IAAM5F,QAAQT,SAASG,KAAK;oCACrCmG,0BAAwBrH,gBAAgB,WAAW;;gCAEpDK,4BACC,qBAAC2G;oCACCC,OAAM;oCACNC,OAAM;oCACNH,OAAM;mDAGR,qBAACC;oCACC/D,OAAO;oCACPgE,OAAM;oCACNC,OAAM;oCACNH,qBAAO,qBAACO;oCACRF,SAAS,IAAM5F,QAAQT,SAASE,KAAK;;;;sCAK3C,qBAACqF;4BAAIK,WAAU;sCACb,cAAA,qBAACK;gCACCO,kBAAgB;gCAChBL,OAAM;gCACNH,qBAAO,qBAACS,iBAAQ;gCAChBJ,SAAS,IAAM5F,QAAQT,SAASI,WAAW;gCAC3C8B,OAAO5C,cAAc,IAAI;;;;;;;;AAQzC;AAEA,SAASiH;IACP,qBACE,qBAACG;QACCC,OAAM;QACNC,OAAM;QACNC,QAAO;QACPC,SAAQ;QACRC,MAAK;kBAEL,cAAA,qBAACC;YACCD,MAAK;YACLE,UAAS;YACTC,UAAS;YACTC,GAAE;;;AAIV;AAEA,SAASlB,SAAS,KAcjB;IAdiB,IAAA,EAChB/D,KAAK,EACLiE,KAAK,EACLH,KAAK,EACLK,OAAO,EACPe,IAAI,EACJ,GAAGC,OAQJ,GAdiB;IAehB,MAAMC,gBACJ,OAAOjB,YAAY,cAAc,OAAOe,SAAS;IACnD,MAAM,EAAExF,SAAS,EAAEhB,aAAa,EAAEC,gBAAgB,EAAE,GAAG0G,IAAAA,iBAAU,EAAC7H;IAClE,MAAM8H,WAAW5G,kBAAkBsB;IAEnC,SAASuF;QACP,IAAIH,eAAe;YACjBjB,2BAAAA;YACAzE;YACA,IAAIwF,MAAM;gBACRM,OAAOlH,IAAI,CAAC4G,MAAM,UAAU;YAC9B;QACF;IACF;IAEA,qBACE,sBAAC7B;QACCK,WAAU;QACV+B,cAAYzF;QACZ0F,iBAAeJ;QACfnB,SAASoB;QACT,wDAAwD;QACxD,gCAAgC;QAChCI,aAAa;YACX,IAAIP,iBAAiBpF,UAAU4F,aAAalH,kBAAkBsB,OAAO;gBACnErB,iBAAiBqB;YACnB;QACF;QACA6F,cAAc,IAAMlH,iBAAiB,CAAC;QACtCgF,WAAW,CAAC9C;YACV,IAAIA,EAAEE,GAAG,KAAK,WAAWF,EAAEE,GAAG,KAAK,KAAK;gBACtCwE;YACF;QACF;QACAjC,MAAM8B,gBAAgB,aAAaQ;QACnCnC,UAAU6B,WAAW,IAAI,CAAC;QACzB,GAAGH,KAAK;;0BAET,qBAACW;gBAAKpC,WAAU;0BAA6BO;;0BAC7C,qBAAC6B;gBAAKpC,WAAU;0BAA6BI;;;;AAGnD;AAEA,SAASI,WAAW,KAAkC;IAAlC,IAAA,EAAE6B,QAAQ,EAAwB,GAAlC;IAClB,qBACE,sBAACD;QACCpC,WAAU;QACVsC,mBAAiBD,WAAW;;0BAE5B,qBAACD;gBAAKpC,WAAU;;YACfqC;;;AAGP;AAIO,MAAMnK,6BAA8B"}