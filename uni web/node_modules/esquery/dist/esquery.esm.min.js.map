{"version": 3, "file": "esquery.esm.min.js", "sources": ["../node_modules/estraverse/estraverse.js", "../parser.js", "../esquery.js"], "sourcesContent": ["/*\n  Copyright (C) 2012-2013 <PERSON><PERSON> <<EMAIL>>\n  Copyright (C) 2012 <PERSON>ya Hidayat <<EMAIL>>\n\n  Redistribution and use in source and binary forms, with or without\n  modification, are permitted provided that the following conditions are met:\n\n    * Redistributions of source code must retain the above copyright\n      notice, this list of conditions and the following disclaimer.\n    * Redistributions in binary form must reproduce the above copyright\n      notice, this list of conditions and the following disclaimer in the\n      documentation and/or other materials provided with the distribution.\n\n  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\"\n  AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE\n  IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE\n  ARE DISCLAIMED. IN NO EVENT SHALL <COPYRIGHT HOLDER> BE LIABLE FOR ANY\n  DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES\n  (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;\n  LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND\n  ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF\n  THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n*/\n/*jslint vars:false, bitwise:true*/\n/*jshint indent:4*/\n/*global exports:true*/\n(function clone(exports) {\n    'use strict';\n\n    var Syntax,\n        VisitorOption,\n        VisitorKeys,\n        BREAK,\n        SKIP,\n        REMOVE;\n\n    function deepCopy(obj) {\n        var ret = {}, key, val;\n        for (key in obj) {\n            if (obj.hasOwnProperty(key)) {\n                val = obj[key];\n                if (typeof val === 'object' && val !== null) {\n                    ret[key] = deepCopy(val);\n                } else {\n                    ret[key] = val;\n                }\n            }\n        }\n        return ret;\n    }\n\n    // based on LLVM libc++ upper_bound / lower_bound\n    // MIT License\n\n    function upperBound(array, func) {\n        var diff, len, i, current;\n\n        len = array.length;\n        i = 0;\n\n        while (len) {\n            diff = len >>> 1;\n            current = i + diff;\n            if (func(array[current])) {\n                len = diff;\n            } else {\n                i = current + 1;\n                len -= diff + 1;\n            }\n        }\n        return i;\n    }\n\n    Syntax = {\n        AssignmentExpression: 'AssignmentExpression',\n        AssignmentPattern: 'AssignmentPattern',\n        ArrayExpression: 'ArrayExpression',\n        ArrayPattern: 'ArrayPattern',\n        ArrowFunctionExpression: 'ArrowFunctionExpression',\n        AwaitExpression: 'AwaitExpression', // CAUTION: It's deferred to ES7.\n        BlockStatement: 'BlockStatement',\n        BinaryExpression: 'BinaryExpression',\n        BreakStatement: 'BreakStatement',\n        CallExpression: 'CallExpression',\n        CatchClause: 'CatchClause',\n        ChainExpression: 'ChainExpression',\n        ClassBody: 'ClassBody',\n        ClassDeclaration: 'ClassDeclaration',\n        ClassExpression: 'ClassExpression',\n        ComprehensionBlock: 'ComprehensionBlock',  // CAUTION: It's deferred to ES7.\n        ComprehensionExpression: 'ComprehensionExpression',  // CAUTION: It's deferred to ES7.\n        ConditionalExpression: 'ConditionalExpression',\n        ContinueStatement: 'ContinueStatement',\n        DebuggerStatement: 'DebuggerStatement',\n        DirectiveStatement: 'DirectiveStatement',\n        DoWhileStatement: 'DoWhileStatement',\n        EmptyStatement: 'EmptyStatement',\n        ExportAllDeclaration: 'ExportAllDeclaration',\n        ExportDefaultDeclaration: 'ExportDefaultDeclaration',\n        ExportNamedDeclaration: 'ExportNamedDeclaration',\n        ExportSpecifier: 'ExportSpecifier',\n        ExpressionStatement: 'ExpressionStatement',\n        ForStatement: 'ForStatement',\n        ForInStatement: 'ForInStatement',\n        ForOfStatement: 'ForOfStatement',\n        FunctionDeclaration: 'FunctionDeclaration',\n        FunctionExpression: 'FunctionExpression',\n        GeneratorExpression: 'GeneratorExpression',  // CAUTION: It's deferred to ES7.\n        Identifier: 'Identifier',\n        IfStatement: 'IfStatement',\n        ImportExpression: 'ImportExpression',\n        ImportDeclaration: 'ImportDeclaration',\n        ImportDefaultSpecifier: 'ImportDefaultSpecifier',\n        ImportNamespaceSpecifier: 'ImportNamespaceSpecifier',\n        ImportSpecifier: 'ImportSpecifier',\n        Literal: 'Literal',\n        LabeledStatement: 'LabeledStatement',\n        LogicalExpression: 'LogicalExpression',\n        MemberExpression: 'MemberExpression',\n        MetaProperty: 'MetaProperty',\n        MethodDefinition: 'MethodDefinition',\n        ModuleSpecifier: 'ModuleSpecifier',\n        NewExpression: 'NewExpression',\n        ObjectExpression: 'ObjectExpression',\n        ObjectPattern: 'ObjectPattern',\n        PrivateIdentifier: 'PrivateIdentifier',\n        Program: 'Program',\n        Property: 'Property',\n        PropertyDefinition: 'PropertyDefinition',\n        RestElement: 'RestElement',\n        ReturnStatement: 'ReturnStatement',\n        SequenceExpression: 'SequenceExpression',\n        SpreadElement: 'SpreadElement',\n        Super: 'Super',\n        SwitchStatement: 'SwitchStatement',\n        SwitchCase: 'SwitchCase',\n        TaggedTemplateExpression: 'TaggedTemplateExpression',\n        TemplateElement: 'TemplateElement',\n        TemplateLiteral: 'TemplateLiteral',\n        ThisExpression: 'ThisExpression',\n        ThrowStatement: 'ThrowStatement',\n        TryStatement: 'TryStatement',\n        UnaryExpression: 'UnaryExpression',\n        UpdateExpression: 'UpdateExpression',\n        VariableDeclaration: 'VariableDeclaration',\n        VariableDeclarator: 'VariableDeclarator',\n        WhileStatement: 'WhileStatement',\n        WithStatement: 'WithStatement',\n        YieldExpression: 'YieldExpression'\n    };\n\n    VisitorKeys = {\n        AssignmentExpression: ['left', 'right'],\n        AssignmentPattern: ['left', 'right'],\n        ArrayExpression: ['elements'],\n        ArrayPattern: ['elements'],\n        ArrowFunctionExpression: ['params', 'body'],\n        AwaitExpression: ['argument'], // CAUTION: It's deferred to ES7.\n        BlockStatement: ['body'],\n        BinaryExpression: ['left', 'right'],\n        BreakStatement: ['label'],\n        CallExpression: ['callee', 'arguments'],\n        CatchClause: ['param', 'body'],\n        ChainExpression: ['expression'],\n        ClassBody: ['body'],\n        ClassDeclaration: ['id', 'superClass', 'body'],\n        ClassExpression: ['id', 'superClass', 'body'],\n        ComprehensionBlock: ['left', 'right'],  // CAUTION: It's deferred to ES7.\n        ComprehensionExpression: ['blocks', 'filter', 'body'],  // CAUTION: It's deferred to ES7.\n        ConditionalExpression: ['test', 'consequent', 'alternate'],\n        ContinueStatement: ['label'],\n        DebuggerStatement: [],\n        DirectiveStatement: [],\n        DoWhileStatement: ['body', 'test'],\n        EmptyStatement: [],\n        ExportAllDeclaration: ['source'],\n        ExportDefaultDeclaration: ['declaration'],\n        ExportNamedDeclaration: ['declaration', 'specifiers', 'source'],\n        ExportSpecifier: ['exported', 'local'],\n        ExpressionStatement: ['expression'],\n        ForStatement: ['init', 'test', 'update', 'body'],\n        ForInStatement: ['left', 'right', 'body'],\n        ForOfStatement: ['left', 'right', 'body'],\n        FunctionDeclaration: ['id', 'params', 'body'],\n        FunctionExpression: ['id', 'params', 'body'],\n        GeneratorExpression: ['blocks', 'filter', 'body'],  // CAUTION: It's deferred to ES7.\n        Identifier: [],\n        IfStatement: ['test', 'consequent', 'alternate'],\n        ImportExpression: ['source'],\n        ImportDeclaration: ['specifiers', 'source'],\n        ImportDefaultSpecifier: ['local'],\n        ImportNamespaceSpecifier: ['local'],\n        ImportSpecifier: ['imported', 'local'],\n        Literal: [],\n        LabeledStatement: ['label', 'body'],\n        LogicalExpression: ['left', 'right'],\n        MemberExpression: ['object', 'property'],\n        MetaProperty: ['meta', 'property'],\n        MethodDefinition: ['key', 'value'],\n        ModuleSpecifier: [],\n        NewExpression: ['callee', 'arguments'],\n        ObjectExpression: ['properties'],\n        ObjectPattern: ['properties'],\n        PrivateIdentifier: [],\n        Program: ['body'],\n        Property: ['key', 'value'],\n        PropertyDefinition: ['key', 'value'],\n        RestElement: [ 'argument' ],\n        ReturnStatement: ['argument'],\n        SequenceExpression: ['expressions'],\n        SpreadElement: ['argument'],\n        Super: [],\n        SwitchStatement: ['discriminant', 'cases'],\n        SwitchCase: ['test', 'consequent'],\n        TaggedTemplateExpression: ['tag', 'quasi'],\n        TemplateElement: [],\n        TemplateLiteral: ['quasis', 'expressions'],\n        ThisExpression: [],\n        ThrowStatement: ['argument'],\n        TryStatement: ['block', 'handler', 'finalizer'],\n        UnaryExpression: ['argument'],\n        UpdateExpression: ['argument'],\n        VariableDeclaration: ['declarations'],\n        VariableDeclarator: ['id', 'init'],\n        WhileStatement: ['test', 'body'],\n        WithStatement: ['object', 'body'],\n        YieldExpression: ['argument']\n    };\n\n    // unique id\n    BREAK = {};\n    SKIP = {};\n    REMOVE = {};\n\n    VisitorOption = {\n        Break: BREAK,\n        Skip: SKIP,\n        Remove: REMOVE\n    };\n\n    function Reference(parent, key) {\n        this.parent = parent;\n        this.key = key;\n    }\n\n    Reference.prototype.replace = function replace(node) {\n        this.parent[this.key] = node;\n    };\n\n    Reference.prototype.remove = function remove() {\n        if (Array.isArray(this.parent)) {\n            this.parent.splice(this.key, 1);\n            return true;\n        } else {\n            this.replace(null);\n            return false;\n        }\n    };\n\n    function Element(node, path, wrap, ref) {\n        this.node = node;\n        this.path = path;\n        this.wrap = wrap;\n        this.ref = ref;\n    }\n\n    function Controller() { }\n\n    // API:\n    // return property path array from root to current node\n    Controller.prototype.path = function path() {\n        var i, iz, j, jz, result, element;\n\n        function addToPath(result, path) {\n            if (Array.isArray(path)) {\n                for (j = 0, jz = path.length; j < jz; ++j) {\n                    result.push(path[j]);\n                }\n            } else {\n                result.push(path);\n            }\n        }\n\n        // root node\n        if (!this.__current.path) {\n            return null;\n        }\n\n        // first node is sentinel, second node is root element\n        result = [];\n        for (i = 2, iz = this.__leavelist.length; i < iz; ++i) {\n            element = this.__leavelist[i];\n            addToPath(result, element.path);\n        }\n        addToPath(result, this.__current.path);\n        return result;\n    };\n\n    // API:\n    // return type of current node\n    Controller.prototype.type = function () {\n        var node = this.current();\n        return node.type || this.__current.wrap;\n    };\n\n    // API:\n    // return array of parent elements\n    Controller.prototype.parents = function parents() {\n        var i, iz, result;\n\n        // first node is sentinel\n        result = [];\n        for (i = 1, iz = this.__leavelist.length; i < iz; ++i) {\n            result.push(this.__leavelist[i].node);\n        }\n\n        return result;\n    };\n\n    // API:\n    // return current node\n    Controller.prototype.current = function current() {\n        return this.__current.node;\n    };\n\n    Controller.prototype.__execute = function __execute(callback, element) {\n        var previous, result;\n\n        result = undefined;\n\n        previous  = this.__current;\n        this.__current = element;\n        this.__state = null;\n        if (callback) {\n            result = callback.call(this, element.node, this.__leavelist[this.__leavelist.length - 1].node);\n        }\n        this.__current = previous;\n\n        return result;\n    };\n\n    // API:\n    // notify control skip / break\n    Controller.prototype.notify = function notify(flag) {\n        this.__state = flag;\n    };\n\n    // API:\n    // skip child nodes of current node\n    Controller.prototype.skip = function () {\n        this.notify(SKIP);\n    };\n\n    // API:\n    // break traversals\n    Controller.prototype['break'] = function () {\n        this.notify(BREAK);\n    };\n\n    // API:\n    // remove node\n    Controller.prototype.remove = function () {\n        this.notify(REMOVE);\n    };\n\n    Controller.prototype.__initialize = function(root, visitor) {\n        this.visitor = visitor;\n        this.root = root;\n        this.__worklist = [];\n        this.__leavelist = [];\n        this.__current = null;\n        this.__state = null;\n        this.__fallback = null;\n        if (visitor.fallback === 'iteration') {\n            this.__fallback = Object.keys;\n        } else if (typeof visitor.fallback === 'function') {\n            this.__fallback = visitor.fallback;\n        }\n\n        this.__keys = VisitorKeys;\n        if (visitor.keys) {\n            this.__keys = Object.assign(Object.create(this.__keys), visitor.keys);\n        }\n    };\n\n    function isNode(node) {\n        if (node == null) {\n            return false;\n        }\n        return typeof node === 'object' && typeof node.type === 'string';\n    }\n\n    function isProperty(nodeType, key) {\n        return (nodeType === Syntax.ObjectExpression || nodeType === Syntax.ObjectPattern) && 'properties' === key;\n    }\n  \n    function candidateExistsInLeaveList(leavelist, candidate) {\n        for (var i = leavelist.length - 1; i >= 0; --i) {\n            if (leavelist[i].node === candidate) {\n                return true;\n            }\n        }\n        return false;\n    }\n\n    Controller.prototype.traverse = function traverse(root, visitor) {\n        var worklist,\n            leavelist,\n            element,\n            node,\n            nodeType,\n            ret,\n            key,\n            current,\n            current2,\n            candidates,\n            candidate,\n            sentinel;\n\n        this.__initialize(root, visitor);\n\n        sentinel = {};\n\n        // reference\n        worklist = this.__worklist;\n        leavelist = this.__leavelist;\n\n        // initialize\n        worklist.push(new Element(root, null, null, null));\n        leavelist.push(new Element(null, null, null, null));\n\n        while (worklist.length) {\n            element = worklist.pop();\n\n            if (element === sentinel) {\n                element = leavelist.pop();\n\n                ret = this.__execute(visitor.leave, element);\n\n                if (this.__state === BREAK || ret === BREAK) {\n                    return;\n                }\n                continue;\n            }\n\n            if (element.node) {\n\n                ret = this.__execute(visitor.enter, element);\n\n                if (this.__state === BREAK || ret === BREAK) {\n                    return;\n                }\n\n                worklist.push(sentinel);\n                leavelist.push(element);\n\n                if (this.__state === SKIP || ret === SKIP) {\n                    continue;\n                }\n\n                node = element.node;\n                nodeType = node.type || element.wrap;\n                candidates = this.__keys[nodeType];\n                if (!candidates) {\n                    if (this.__fallback) {\n                        candidates = this.__fallback(node);\n                    } else {\n                        throw new Error('Unknown node type ' + nodeType + '.');\n                    }\n                }\n\n                current = candidates.length;\n                while ((current -= 1) >= 0) {\n                    key = candidates[current];\n                    candidate = node[key];\n                    if (!candidate) {\n                        continue;\n                    }\n\n                    if (Array.isArray(candidate)) {\n                        current2 = candidate.length;\n                        while ((current2 -= 1) >= 0) {\n                            if (!candidate[current2]) {\n                                continue;\n                            }\n\n                            if (candidateExistsInLeaveList(leavelist, candidate[current2])) {\n                              continue;\n                            }\n\n                            if (isProperty(nodeType, candidates[current])) {\n                                element = new Element(candidate[current2], [key, current2], 'Property', null);\n                            } else if (isNode(candidate[current2])) {\n                                element = new Element(candidate[current2], [key, current2], null, null);\n                            } else {\n                                continue;\n                            }\n                            worklist.push(element);\n                        }\n                    } else if (isNode(candidate)) {\n                        if (candidateExistsInLeaveList(leavelist, candidate)) {\n                          continue;\n                        }\n\n                        worklist.push(new Element(candidate, key, null, null));\n                    }\n                }\n            }\n        }\n    };\n\n    Controller.prototype.replace = function replace(root, visitor) {\n        var worklist,\n            leavelist,\n            node,\n            nodeType,\n            target,\n            element,\n            current,\n            current2,\n            candidates,\n            candidate,\n            sentinel,\n            outer,\n            key;\n\n        function removeElem(element) {\n            var i,\n                key,\n                nextElem,\n                parent;\n\n            if (element.ref.remove()) {\n                // When the reference is an element of an array.\n                key = element.ref.key;\n                parent = element.ref.parent;\n\n                // If removed from array, then decrease following items' keys.\n                i = worklist.length;\n                while (i--) {\n                    nextElem = worklist[i];\n                    if (nextElem.ref && nextElem.ref.parent === parent) {\n                        if  (nextElem.ref.key < key) {\n                            break;\n                        }\n                        --nextElem.ref.key;\n                    }\n                }\n            }\n        }\n\n        this.__initialize(root, visitor);\n\n        sentinel = {};\n\n        // reference\n        worklist = this.__worklist;\n        leavelist = this.__leavelist;\n\n        // initialize\n        outer = {\n            root: root\n        };\n        element = new Element(root, null, null, new Reference(outer, 'root'));\n        worklist.push(element);\n        leavelist.push(element);\n\n        while (worklist.length) {\n            element = worklist.pop();\n\n            if (element === sentinel) {\n                element = leavelist.pop();\n\n                target = this.__execute(visitor.leave, element);\n\n                // node may be replaced with null,\n                // so distinguish between undefined and null in this place\n                if (target !== undefined && target !== BREAK && target !== SKIP && target !== REMOVE) {\n                    // replace\n                    element.ref.replace(target);\n                }\n\n                if (this.__state === REMOVE || target === REMOVE) {\n                    removeElem(element);\n                }\n\n                if (this.__state === BREAK || target === BREAK) {\n                    return outer.root;\n                }\n                continue;\n            }\n\n            target = this.__execute(visitor.enter, element);\n\n            // node may be replaced with null,\n            // so distinguish between undefined and null in this place\n            if (target !== undefined && target !== BREAK && target !== SKIP && target !== REMOVE) {\n                // replace\n                element.ref.replace(target);\n                element.node = target;\n            }\n\n            if (this.__state === REMOVE || target === REMOVE) {\n                removeElem(element);\n                element.node = null;\n            }\n\n            if (this.__state === BREAK || target === BREAK) {\n                return outer.root;\n            }\n\n            // node may be null\n            node = element.node;\n            if (!node) {\n                continue;\n            }\n\n            worklist.push(sentinel);\n            leavelist.push(element);\n\n            if (this.__state === SKIP || target === SKIP) {\n                continue;\n            }\n\n            nodeType = node.type || element.wrap;\n            candidates = this.__keys[nodeType];\n            if (!candidates) {\n                if (this.__fallback) {\n                    candidates = this.__fallback(node);\n                } else {\n                    throw new Error('Unknown node type ' + nodeType + '.');\n                }\n            }\n\n            current = candidates.length;\n            while ((current -= 1) >= 0) {\n                key = candidates[current];\n                candidate = node[key];\n                if (!candidate) {\n                    continue;\n                }\n\n                if (Array.isArray(candidate)) {\n                    current2 = candidate.length;\n                    while ((current2 -= 1) >= 0) {\n                        if (!candidate[current2]) {\n                            continue;\n                        }\n                        if (isProperty(nodeType, candidates[current])) {\n                            element = new Element(candidate[current2], [key, current2], 'Property', new Reference(candidate, current2));\n                        } else if (isNode(candidate[current2])) {\n                            element = new Element(candidate[current2], [key, current2], null, new Reference(candidate, current2));\n                        } else {\n                            continue;\n                        }\n                        worklist.push(element);\n                    }\n                } else if (isNode(candidate)) {\n                    worklist.push(new Element(candidate, key, null, new Reference(node, key)));\n                }\n            }\n        }\n\n        return outer.root;\n    };\n\n    function traverse(root, visitor) {\n        var controller = new Controller();\n        return controller.traverse(root, visitor);\n    }\n\n    function replace(root, visitor) {\n        var controller = new Controller();\n        return controller.replace(root, visitor);\n    }\n\n    function extendCommentRange(comment, tokens) {\n        var target;\n\n        target = upperBound(tokens, function search(token) {\n            return token.range[0] > comment.range[0];\n        });\n\n        comment.extendedRange = [comment.range[0], comment.range[1]];\n\n        if (target !== tokens.length) {\n            comment.extendedRange[1] = tokens[target].range[0];\n        }\n\n        target -= 1;\n        if (target >= 0) {\n            comment.extendedRange[0] = tokens[target].range[1];\n        }\n\n        return comment;\n    }\n\n    function attachComments(tree, providedComments, tokens) {\n        // At first, we should calculate extended comment ranges.\n        var comments = [], comment, len, i, cursor;\n\n        if (!tree.range) {\n            throw new Error('attachComments needs range information');\n        }\n\n        // tokens array is empty, we attach comments to tree as 'leadingComments'\n        if (!tokens.length) {\n            if (providedComments.length) {\n                for (i = 0, len = providedComments.length; i < len; i += 1) {\n                    comment = deepCopy(providedComments[i]);\n                    comment.extendedRange = [0, tree.range[0]];\n                    comments.push(comment);\n                }\n                tree.leadingComments = comments;\n            }\n            return tree;\n        }\n\n        for (i = 0, len = providedComments.length; i < len; i += 1) {\n            comments.push(extendCommentRange(deepCopy(providedComments[i]), tokens));\n        }\n\n        // This is based on John Freeman's implementation.\n        cursor = 0;\n        traverse(tree, {\n            enter: function (node) {\n                var comment;\n\n                while (cursor < comments.length) {\n                    comment = comments[cursor];\n                    if (comment.extendedRange[1] > node.range[0]) {\n                        break;\n                    }\n\n                    if (comment.extendedRange[1] === node.range[0]) {\n                        if (!node.leadingComments) {\n                            node.leadingComments = [];\n                        }\n                        node.leadingComments.push(comment);\n                        comments.splice(cursor, 1);\n                    } else {\n                        cursor += 1;\n                    }\n                }\n\n                // already out of owned node\n                if (cursor === comments.length) {\n                    return VisitorOption.Break;\n                }\n\n                if (comments[cursor].extendedRange[0] > node.range[1]) {\n                    return VisitorOption.Skip;\n                }\n            }\n        });\n\n        cursor = 0;\n        traverse(tree, {\n            leave: function (node) {\n                var comment;\n\n                while (cursor < comments.length) {\n                    comment = comments[cursor];\n                    if (node.range[1] < comment.extendedRange[0]) {\n                        break;\n                    }\n\n                    if (node.range[1] === comment.extendedRange[0]) {\n                        if (!node.trailingComments) {\n                            node.trailingComments = [];\n                        }\n                        node.trailingComments.push(comment);\n                        comments.splice(cursor, 1);\n                    } else {\n                        cursor += 1;\n                    }\n                }\n\n                // already out of owned node\n                if (cursor === comments.length) {\n                    return VisitorOption.Break;\n                }\n\n                if (comments[cursor].extendedRange[0] > node.range[1]) {\n                    return VisitorOption.Skip;\n                }\n            }\n        });\n\n        return tree;\n    }\n\n    exports.Syntax = Syntax;\n    exports.traverse = traverse;\n    exports.replace = replace;\n    exports.attachComments = attachComments;\n    exports.VisitorKeys = VisitorKeys;\n    exports.VisitorOption = VisitorOption;\n    exports.Controller = Controller;\n    exports.cloneEnvironment = function () { return clone({}); };\n\n    return exports;\n}(exports));\n/* vim: set sw=4 ts=4 et tw=80 : */\n", "/*\n * Generated by PEG.js 0.10.0.\n *\n * http://pegjs.org/\n */\n(function(root, factory) {\n  if (typeof define === \"function\" && define.amd) {\n    define([], factory);\n  } else if (typeof module === \"object\" && module.exports) {\n    module.exports = factory();\n  }\n})(this, function() {\n  \"use strict\";\n\n  function peg$subclass(child, parent) {\n    function ctor() { this.constructor = child; }\n    ctor.prototype = parent.prototype;\n    child.prototype = new ctor();\n  }\n\n  function peg$SyntaxError(message, expected, found, location) {\n    this.message  = message;\n    this.expected = expected;\n    this.found    = found;\n    this.location = location;\n    this.name     = \"SyntaxError\";\n\n    if (typeof Error.captureStackTrace === \"function\") {\n      Error.captureStackTrace(this, peg$SyntaxError);\n    }\n  }\n\n  peg$subclass(peg$SyntaxError, Error);\n\n  peg$SyntaxError.buildMessage = function(expected, found) {\n    var DESCRIBE_EXPECTATION_FNS = {\n          literal: function(expectation) {\n            return \"\\\"\" + literalEscape(expectation.text) + \"\\\"\";\n          },\n\n          \"class\": function(expectation) {\n            var escapedParts = \"\",\n                i;\n\n            for (i = 0; i < expectation.parts.length; i++) {\n              escapedParts += expectation.parts[i] instanceof Array\n                ? classEscape(expectation.parts[i][0]) + \"-\" + classEscape(expectation.parts[i][1])\n                : classEscape(expectation.parts[i]);\n            }\n\n            return \"[\" + (expectation.inverted ? \"^\" : \"\") + escapedParts + \"]\";\n          },\n\n          any: function(expectation) {\n            return \"any character\";\n          },\n\n          end: function(expectation) {\n            return \"end of input\";\n          },\n\n          other: function(expectation) {\n            return expectation.description;\n          }\n        };\n\n    function hex(ch) {\n      return ch.charCodeAt(0).toString(16).toUpperCase();\n    }\n\n    function literalEscape(s) {\n      return s\n        .replace(/\\\\/g, '\\\\\\\\')\n        .replace(/\"/g,  '\\\\\"')\n        .replace(/\\0/g, '\\\\0')\n        .replace(/\\t/g, '\\\\t')\n        .replace(/\\n/g, '\\\\n')\n        .replace(/\\r/g, '\\\\r')\n        .replace(/[\\x00-\\x0F]/g,          function(ch) { return '\\\\x0' + hex(ch); })\n        .replace(/[\\x10-\\x1F\\x7F-\\x9F]/g, function(ch) { return '\\\\x'  + hex(ch); });\n    }\n\n    function classEscape(s) {\n      return s\n        .replace(/\\\\/g, '\\\\\\\\')\n        .replace(/\\]/g, '\\\\]')\n        .replace(/\\^/g, '\\\\^')\n        .replace(/-/g,  '\\\\-')\n        .replace(/\\0/g, '\\\\0')\n        .replace(/\\t/g, '\\\\t')\n        .replace(/\\n/g, '\\\\n')\n        .replace(/\\r/g, '\\\\r')\n        .replace(/[\\x00-\\x0F]/g,          function(ch) { return '\\\\x0' + hex(ch); })\n        .replace(/[\\x10-\\x1F\\x7F-\\x9F]/g, function(ch) { return '\\\\x'  + hex(ch); });\n    }\n\n    function describeExpectation(expectation) {\n      return DESCRIBE_EXPECTATION_FNS[expectation.type](expectation);\n    }\n\n    function describeExpected(expected) {\n      var descriptions = new Array(expected.length),\n          i, j;\n\n      for (i = 0; i < expected.length; i++) {\n        descriptions[i] = describeExpectation(expected[i]);\n      }\n\n      descriptions.sort();\n\n      if (descriptions.length > 0) {\n        for (i = 1, j = 1; i < descriptions.length; i++) {\n          if (descriptions[i - 1] !== descriptions[i]) {\n            descriptions[j] = descriptions[i];\n            j++;\n          }\n        }\n        descriptions.length = j;\n      }\n\n      switch (descriptions.length) {\n        case 1:\n          return descriptions[0];\n\n        case 2:\n          return descriptions[0] + \" or \" + descriptions[1];\n\n        default:\n          return descriptions.slice(0, -1).join(\", \")\n            + \", or \"\n            + descriptions[descriptions.length - 1];\n      }\n    }\n\n    function describeFound(found) {\n      return found ? \"\\\"\" + literalEscape(found) + \"\\\"\" : \"end of input\";\n    }\n\n    return \"Expected \" + describeExpected(expected) + \" but \" + describeFound(found) + \" found.\";\n  };\n\n  function peg$parse(input, options) {\n    options = options !== void 0 ? options : {};\n\n    var peg$FAILED = {},\n\n        peg$startRuleFunctions = { start: peg$parsestart },\n        peg$startRuleFunction  = peg$parsestart,\n\n        peg$c0 = function(ss) {\n            return ss.length === 1 ? ss[0] : { type: 'matches', selectors: ss };\n          },\n        peg$c1 = function() { return void 0; },\n        peg$c2 = \" \",\n        peg$c3 = peg$literalExpectation(\" \", false),\n        peg$c4 = /^[^ [\\],():#!=><~+.]/,\n        peg$c5 = peg$classExpectation([\" \", \"[\", \"]\", \",\", \"(\", \")\", \":\", \"#\", \"!\", \"=\", \">\", \"<\", \"~\", \"+\", \".\"], true, false),\n        peg$c6 = function(i) { return i.join(''); },\n        peg$c7 = \">\",\n        peg$c8 = peg$literalExpectation(\">\", false),\n        peg$c9 = function() { return 'child'; },\n        peg$c10 = \"~\",\n        peg$c11 = peg$literalExpectation(\"~\", false),\n        peg$c12 = function() { return 'sibling'; },\n        peg$c13 = \"+\",\n        peg$c14 = peg$literalExpectation(\"+\", false),\n        peg$c15 = function() { return 'adjacent'; },\n        peg$c16 = function() { return 'descendant'; },\n        peg$c17 = \",\",\n        peg$c18 = peg$literalExpectation(\",\", false),\n        peg$c19 = function(s, ss) {\n          return [s].concat(ss.map(function (s) { return s[3]; }));\n        },\n        peg$c20 = function(op, s) {\n            if (!op) return s;\n            return { type: op, left: { type: 'exactNode' }, right: s };\n          },\n        peg$c21 = function(a, ops) {\n            return ops.reduce(function (memo, rhs) {\n              return { type: rhs[0], left: memo, right: rhs[1] };\n            }, a);\n          },\n        peg$c22 = \"!\",\n        peg$c23 = peg$literalExpectation(\"!\", false),\n        peg$c24 = function(subject, as) {\n            const b = as.length === 1 ? as[0] : { type: 'compound', selectors: as };\n            if(subject) b.subject = true;\n            return b;\n          },\n        peg$c25 = \"*\",\n        peg$c26 = peg$literalExpectation(\"*\", false),\n        peg$c27 = function(a) { return { type: 'wildcard', value: a }; },\n        peg$c28 = \"#\",\n        peg$c29 = peg$literalExpectation(\"#\", false),\n        peg$c30 = function(i) { return { type: 'identifier', value: i }; },\n        peg$c31 = \"[\",\n        peg$c32 = peg$literalExpectation(\"[\", false),\n        peg$c33 = \"]\",\n        peg$c34 = peg$literalExpectation(\"]\", false),\n        peg$c35 = function(v) { return v; },\n        peg$c36 = /^[><!]/,\n        peg$c37 = peg$classExpectation([\">\", \"<\", \"!\"], false, false),\n        peg$c38 = \"=\",\n        peg$c39 = peg$literalExpectation(\"=\", false),\n        peg$c40 = function(a) { return (a || '') + '='; },\n        peg$c41 = /^[><]/,\n        peg$c42 = peg$classExpectation([\">\", \"<\"], false, false),\n        peg$c43 = \".\",\n        peg$c44 = peg$literalExpectation(\".\", false),\n        peg$c45 = function(a, as) {\n            return [].concat.apply([a], as).join('');\n          },\n        peg$c46 = function(name, op, value) {\n              return { type: 'attribute', name: name, operator: op, value: value };\n            },\n        peg$c47 = function(name) { return { type: 'attribute', name: name }; },\n        peg$c48 = \"\\\"\",\n        peg$c49 = peg$literalExpectation(\"\\\"\", false),\n        peg$c50 = /^[^\\\\\"]/,\n        peg$c51 = peg$classExpectation([\"\\\\\", \"\\\"\"], true, false),\n        peg$c52 = \"\\\\\",\n        peg$c53 = peg$literalExpectation(\"\\\\\", false),\n        peg$c54 = peg$anyExpectation(),\n        peg$c55 = function(a, b) { return a + b; },\n        peg$c56 = function(d) {\n                return { type: 'literal', value: strUnescape(d.join('')) };\n              },\n        peg$c57 = \"'\",\n        peg$c58 = peg$literalExpectation(\"'\", false),\n        peg$c59 = /^[^\\\\']/,\n        peg$c60 = peg$classExpectation([\"\\\\\", \"'\"], true, false),\n        peg$c61 = /^[0-9]/,\n        peg$c62 = peg$classExpectation([[\"0\", \"9\"]], false, false),\n        peg$c63 = function(a, b) {\n                // Can use `a.flat().join('')` once supported\n                const leadingDecimals = a ? [].concat.apply([], a).join('') : '';\n                return { type: 'literal', value: parseFloat(leadingDecimals + b.join('')) };\n              },\n        peg$c64 = function(i) { return { type: 'literal', value: i }; },\n        peg$c65 = \"type(\",\n        peg$c66 = peg$literalExpectation(\"type(\", false),\n        peg$c67 = /^[^ )]/,\n        peg$c68 = peg$classExpectation([\" \", \")\"], true, false),\n        peg$c69 = \")\",\n        peg$c70 = peg$literalExpectation(\")\", false),\n        peg$c71 = function(t) { return { type: 'type', value: t.join('') }; },\n        peg$c72 = /^[imsu]/,\n        peg$c73 = peg$classExpectation([\"i\", \"m\", \"s\", \"u\"], false, false),\n        peg$c74 = \"/\",\n        peg$c75 = peg$literalExpectation(\"/\", false),\n        peg$c76 = /^[^\\/]/,\n        peg$c77 = peg$classExpectation([\"/\"], true, false),\n        peg$c78 = function(d, flgs) { return {\n              type: 'regexp', value: new RegExp(d.join(''), flgs ? flgs.join('') : '') };\n            },\n        peg$c79 = function(i, is) {\n          return { type: 'field', name: is.reduce(function(memo, p){ return memo + p[0] + p[1]; }, i)};\n        },\n        peg$c80 = \":not(\",\n        peg$c81 = peg$literalExpectation(\":not(\", false),\n        peg$c82 = function(ss) { return { type: 'not', selectors: ss }; },\n        peg$c83 = \":matches(\",\n        peg$c84 = peg$literalExpectation(\":matches(\", false),\n        peg$c85 = function(ss) { return { type: 'matches', selectors: ss }; },\n        peg$c86 = \":has(\",\n        peg$c87 = peg$literalExpectation(\":has(\", false),\n        peg$c88 = function(ss) { return { type: 'has', selectors: ss }; },\n        peg$c89 = \":first-child\",\n        peg$c90 = peg$literalExpectation(\":first-child\", false),\n        peg$c91 = function() { return nth(1); },\n        peg$c92 = \":last-child\",\n        peg$c93 = peg$literalExpectation(\":last-child\", false),\n        peg$c94 = function() { return nthLast(1); },\n        peg$c95 = \":nth-child(\",\n        peg$c96 = peg$literalExpectation(\":nth-child(\", false),\n        peg$c97 = function(n) { return nth(parseInt(n.join(''), 10)); },\n        peg$c98 = \":nth-last-child(\",\n        peg$c99 = peg$literalExpectation(\":nth-last-child(\", false),\n        peg$c100 = function(n) { return nthLast(parseInt(n.join(''), 10)); },\n        peg$c101 = \":\",\n        peg$c102 = peg$literalExpectation(\":\", false),\n        peg$c103 = function(c) {\n          return { type: 'class', name: c };\n        },\n\n        peg$currPos          = 0,\n        peg$savedPos         = 0,\n        peg$posDetailsCache  = [{ line: 1, column: 1 }],\n        peg$maxFailPos       = 0,\n        peg$maxFailExpected  = [],\n        peg$silentFails      = 0,\n\n        peg$resultsCache = {},\n\n        peg$result;\n\n    if (\"startRule\" in options) {\n      if (!(options.startRule in peg$startRuleFunctions)) {\n        throw new Error(\"Can't start parsing from rule \\\"\" + options.startRule + \"\\\".\");\n      }\n\n      peg$startRuleFunction = peg$startRuleFunctions[options.startRule];\n    }\n\n    function text() {\n      return input.substring(peg$savedPos, peg$currPos);\n    }\n\n    function location() {\n      return peg$computeLocation(peg$savedPos, peg$currPos);\n    }\n\n    function expected(description, location) {\n      location = location !== void 0 ? location : peg$computeLocation(peg$savedPos, peg$currPos)\n\n      throw peg$buildStructuredError(\n        [peg$otherExpectation(description)],\n        input.substring(peg$savedPos, peg$currPos),\n        location\n      );\n    }\n\n    function error(message, location) {\n      location = location !== void 0 ? location : peg$computeLocation(peg$savedPos, peg$currPos)\n\n      throw peg$buildSimpleError(message, location);\n    }\n\n    function peg$literalExpectation(text, ignoreCase) {\n      return { type: \"literal\", text: text, ignoreCase: ignoreCase };\n    }\n\n    function peg$classExpectation(parts, inverted, ignoreCase) {\n      return { type: \"class\", parts: parts, inverted: inverted, ignoreCase: ignoreCase };\n    }\n\n    function peg$anyExpectation() {\n      return { type: \"any\" };\n    }\n\n    function peg$endExpectation() {\n      return { type: \"end\" };\n    }\n\n    function peg$otherExpectation(description) {\n      return { type: \"other\", description: description };\n    }\n\n    function peg$computePosDetails(pos) {\n      var details = peg$posDetailsCache[pos], p;\n\n      if (details) {\n        return details;\n      } else {\n        p = pos - 1;\n        while (!peg$posDetailsCache[p]) {\n          p--;\n        }\n\n        details = peg$posDetailsCache[p];\n        details = {\n          line:   details.line,\n          column: details.column\n        };\n\n        while (p < pos) {\n          if (input.charCodeAt(p) === 10) {\n            details.line++;\n            details.column = 1;\n          } else {\n            details.column++;\n          }\n\n          p++;\n        }\n\n        peg$posDetailsCache[pos] = details;\n        return details;\n      }\n    }\n\n    function peg$computeLocation(startPos, endPos) {\n      var startPosDetails = peg$computePosDetails(startPos),\n          endPosDetails   = peg$computePosDetails(endPos);\n\n      return {\n        start: {\n          offset: startPos,\n          line:   startPosDetails.line,\n          column: startPosDetails.column\n        },\n        end: {\n          offset: endPos,\n          line:   endPosDetails.line,\n          column: endPosDetails.column\n        }\n      };\n    }\n\n    function peg$fail(expected) {\n      if (peg$currPos < peg$maxFailPos) { return; }\n\n      if (peg$currPos > peg$maxFailPos) {\n        peg$maxFailPos = peg$currPos;\n        peg$maxFailExpected = [];\n      }\n\n      peg$maxFailExpected.push(expected);\n    }\n\n    function peg$buildSimpleError(message, location) {\n      return new peg$SyntaxError(message, null, null, location);\n    }\n\n    function peg$buildStructuredError(expected, found, location) {\n      return new peg$SyntaxError(\n        peg$SyntaxError.buildMessage(expected, found),\n        expected,\n        found,\n        location\n      );\n    }\n\n    function peg$parsestart() {\n      var s0, s1, s2, s3;\n\n      var key    = peg$currPos * 32 + 0,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      s1 = peg$parse_();\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parseselectors();\n        if (s2 !== peg$FAILED) {\n          s3 = peg$parse_();\n          if (s3 !== peg$FAILED) {\n            peg$savedPos = s0;\n            s1 = peg$c0(s2);\n            s0 = s1;\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n      if (s0 === peg$FAILED) {\n        s0 = peg$currPos;\n        s1 = peg$parse_();\n        if (s1 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c1();\n        }\n        s0 = s1;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parse_() {\n      var s0, s1;\n\n      var key    = peg$currPos * 32 + 1,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = [];\n      if (input.charCodeAt(peg$currPos) === 32) {\n        s1 = peg$c2;\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c3); }\n      }\n      while (s1 !== peg$FAILED) {\n        s0.push(s1);\n        if (input.charCodeAt(peg$currPos) === 32) {\n          s1 = peg$c2;\n          peg$currPos++;\n        } else {\n          s1 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c3); }\n        }\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseidentifierName() {\n      var s0, s1, s2;\n\n      var key    = peg$currPos * 32 + 2,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      s1 = [];\n      if (peg$c4.test(input.charAt(peg$currPos))) {\n        s2 = input.charAt(peg$currPos);\n        peg$currPos++;\n      } else {\n        s2 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c5); }\n      }\n      if (s2 !== peg$FAILED) {\n        while (s2 !== peg$FAILED) {\n          s1.push(s2);\n          if (peg$c4.test(input.charAt(peg$currPos))) {\n            s2 = input.charAt(peg$currPos);\n            peg$currPos++;\n          } else {\n            s2 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c5); }\n          }\n        }\n      } else {\n        s1 = peg$FAILED;\n      }\n      if (s1 !== peg$FAILED) {\n        peg$savedPos = s0;\n        s1 = peg$c6(s1);\n      }\n      s0 = s1;\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsebinaryOp() {\n      var s0, s1, s2, s3;\n\n      var key    = peg$currPos * 32 + 3,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      s1 = peg$parse_();\n      if (s1 !== peg$FAILED) {\n        if (input.charCodeAt(peg$currPos) === 62) {\n          s2 = peg$c7;\n          peg$currPos++;\n        } else {\n          s2 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c8); }\n        }\n        if (s2 !== peg$FAILED) {\n          s3 = peg$parse_();\n          if (s3 !== peg$FAILED) {\n            peg$savedPos = s0;\n            s1 = peg$c9();\n            s0 = s1;\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n      if (s0 === peg$FAILED) {\n        s0 = peg$currPos;\n        s1 = peg$parse_();\n        if (s1 !== peg$FAILED) {\n          if (input.charCodeAt(peg$currPos) === 126) {\n            s2 = peg$c10;\n            peg$currPos++;\n          } else {\n            s2 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c11); }\n          }\n          if (s2 !== peg$FAILED) {\n            s3 = peg$parse_();\n            if (s3 !== peg$FAILED) {\n              peg$savedPos = s0;\n              s1 = peg$c12();\n              s0 = s1;\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n        if (s0 === peg$FAILED) {\n          s0 = peg$currPos;\n          s1 = peg$parse_();\n          if (s1 !== peg$FAILED) {\n            if (input.charCodeAt(peg$currPos) === 43) {\n              s2 = peg$c13;\n              peg$currPos++;\n            } else {\n              s2 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c14); }\n            }\n            if (s2 !== peg$FAILED) {\n              s3 = peg$parse_();\n              if (s3 !== peg$FAILED) {\n                peg$savedPos = s0;\n                s1 = peg$c15();\n                s0 = s1;\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n          if (s0 === peg$FAILED) {\n            s0 = peg$currPos;\n            if (input.charCodeAt(peg$currPos) === 32) {\n              s1 = peg$c2;\n              peg$currPos++;\n            } else {\n              s1 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c3); }\n            }\n            if (s1 !== peg$FAILED) {\n              s2 = peg$parse_();\n              if (s2 !== peg$FAILED) {\n                peg$savedPos = s0;\n                s1 = peg$c16();\n                s0 = s1;\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          }\n        }\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsehasSelectors() {\n      var s0, s1, s2, s3, s4, s5, s6, s7;\n\n      var key    = peg$currPos * 32 + 4,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      s1 = peg$parsehasSelector();\n      if (s1 !== peg$FAILED) {\n        s2 = [];\n        s3 = peg$currPos;\n        s4 = peg$parse_();\n        if (s4 !== peg$FAILED) {\n          if (input.charCodeAt(peg$currPos) === 44) {\n            s5 = peg$c17;\n            peg$currPos++;\n          } else {\n            s5 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c18); }\n          }\n          if (s5 !== peg$FAILED) {\n            s6 = peg$parse_();\n            if (s6 !== peg$FAILED) {\n              s7 = peg$parsehasSelector();\n              if (s7 !== peg$FAILED) {\n                s4 = [s4, s5, s6, s7];\n                s3 = s4;\n              } else {\n                peg$currPos = s3;\n                s3 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s3;\n              s3 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s3;\n            s3 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s3;\n          s3 = peg$FAILED;\n        }\n        while (s3 !== peg$FAILED) {\n          s2.push(s3);\n          s3 = peg$currPos;\n          s4 = peg$parse_();\n          if (s4 !== peg$FAILED) {\n            if (input.charCodeAt(peg$currPos) === 44) {\n              s5 = peg$c17;\n              peg$currPos++;\n            } else {\n              s5 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c18); }\n            }\n            if (s5 !== peg$FAILED) {\n              s6 = peg$parse_();\n              if (s6 !== peg$FAILED) {\n                s7 = peg$parsehasSelector();\n                if (s7 !== peg$FAILED) {\n                  s4 = [s4, s5, s6, s7];\n                  s3 = s4;\n                } else {\n                  peg$currPos = s3;\n                  s3 = peg$FAILED;\n                }\n              } else {\n                peg$currPos = s3;\n                s3 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s3;\n              s3 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s3;\n            s3 = peg$FAILED;\n          }\n        }\n        if (s2 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c19(s1, s2);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseselectors() {\n      var s0, s1, s2, s3, s4, s5, s6, s7;\n\n      var key    = peg$currPos * 32 + 5,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      s1 = peg$parseselector();\n      if (s1 !== peg$FAILED) {\n        s2 = [];\n        s3 = peg$currPos;\n        s4 = peg$parse_();\n        if (s4 !== peg$FAILED) {\n          if (input.charCodeAt(peg$currPos) === 44) {\n            s5 = peg$c17;\n            peg$currPos++;\n          } else {\n            s5 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c18); }\n          }\n          if (s5 !== peg$FAILED) {\n            s6 = peg$parse_();\n            if (s6 !== peg$FAILED) {\n              s7 = peg$parseselector();\n              if (s7 !== peg$FAILED) {\n                s4 = [s4, s5, s6, s7];\n                s3 = s4;\n              } else {\n                peg$currPos = s3;\n                s3 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s3;\n              s3 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s3;\n            s3 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s3;\n          s3 = peg$FAILED;\n        }\n        while (s3 !== peg$FAILED) {\n          s2.push(s3);\n          s3 = peg$currPos;\n          s4 = peg$parse_();\n          if (s4 !== peg$FAILED) {\n            if (input.charCodeAt(peg$currPos) === 44) {\n              s5 = peg$c17;\n              peg$currPos++;\n            } else {\n              s5 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c18); }\n            }\n            if (s5 !== peg$FAILED) {\n              s6 = peg$parse_();\n              if (s6 !== peg$FAILED) {\n                s7 = peg$parseselector();\n                if (s7 !== peg$FAILED) {\n                  s4 = [s4, s5, s6, s7];\n                  s3 = s4;\n                } else {\n                  peg$currPos = s3;\n                  s3 = peg$FAILED;\n                }\n              } else {\n                peg$currPos = s3;\n                s3 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s3;\n              s3 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s3;\n            s3 = peg$FAILED;\n          }\n        }\n        if (s2 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c19(s1, s2);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsehasSelector() {\n      var s0, s1, s2;\n\n      var key    = peg$currPos * 32 + 6,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      s1 = peg$parsebinaryOp();\n      if (s1 === peg$FAILED) {\n        s1 = null;\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parseselector();\n        if (s2 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c20(s1, s2);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseselector() {\n      var s0, s1, s2, s3, s4, s5;\n\n      var key    = peg$currPos * 32 + 7,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      s1 = peg$parsesequence();\n      if (s1 !== peg$FAILED) {\n        s2 = [];\n        s3 = peg$currPos;\n        s4 = peg$parsebinaryOp();\n        if (s4 !== peg$FAILED) {\n          s5 = peg$parsesequence();\n          if (s5 !== peg$FAILED) {\n            s4 = [s4, s5];\n            s3 = s4;\n          } else {\n            peg$currPos = s3;\n            s3 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s3;\n          s3 = peg$FAILED;\n        }\n        while (s3 !== peg$FAILED) {\n          s2.push(s3);\n          s3 = peg$currPos;\n          s4 = peg$parsebinaryOp();\n          if (s4 !== peg$FAILED) {\n            s5 = peg$parsesequence();\n            if (s5 !== peg$FAILED) {\n              s4 = [s4, s5];\n              s3 = s4;\n            } else {\n              peg$currPos = s3;\n              s3 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s3;\n            s3 = peg$FAILED;\n          }\n        }\n        if (s2 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c21(s1, s2);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsesequence() {\n      var s0, s1, s2, s3;\n\n      var key    = peg$currPos * 32 + 8,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.charCodeAt(peg$currPos) === 33) {\n        s1 = peg$c22;\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c23); }\n      }\n      if (s1 === peg$FAILED) {\n        s1 = null;\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = [];\n        s3 = peg$parseatom();\n        if (s3 !== peg$FAILED) {\n          while (s3 !== peg$FAILED) {\n            s2.push(s3);\n            s3 = peg$parseatom();\n          }\n        } else {\n          s2 = peg$FAILED;\n        }\n        if (s2 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c24(s1, s2);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseatom() {\n      var s0;\n\n      var key    = peg$currPos * 32 + 9,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$parsewildcard();\n      if (s0 === peg$FAILED) {\n        s0 = peg$parseidentifier();\n        if (s0 === peg$FAILED) {\n          s0 = peg$parseattr();\n          if (s0 === peg$FAILED) {\n            s0 = peg$parsefield();\n            if (s0 === peg$FAILED) {\n              s0 = peg$parsenegation();\n              if (s0 === peg$FAILED) {\n                s0 = peg$parsematches();\n                if (s0 === peg$FAILED) {\n                  s0 = peg$parsehas();\n                  if (s0 === peg$FAILED) {\n                    s0 = peg$parsefirstChild();\n                    if (s0 === peg$FAILED) {\n                      s0 = peg$parselastChild();\n                      if (s0 === peg$FAILED) {\n                        s0 = peg$parsenthChild();\n                        if (s0 === peg$FAILED) {\n                          s0 = peg$parsenthLastChild();\n                          if (s0 === peg$FAILED) {\n                            s0 = peg$parseclass();\n                          }\n                        }\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          }\n        }\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsewildcard() {\n      var s0, s1;\n\n      var key    = peg$currPos * 32 + 10,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.charCodeAt(peg$currPos) === 42) {\n        s1 = peg$c25;\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c26); }\n      }\n      if (s1 !== peg$FAILED) {\n        peg$savedPos = s0;\n        s1 = peg$c27(s1);\n      }\n      s0 = s1;\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseidentifier() {\n      var s0, s1, s2;\n\n      var key    = peg$currPos * 32 + 11,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.charCodeAt(peg$currPos) === 35) {\n        s1 = peg$c28;\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c29); }\n      }\n      if (s1 === peg$FAILED) {\n        s1 = null;\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parseidentifierName();\n        if (s2 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c30(s2);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseattr() {\n      var s0, s1, s2, s3, s4, s5;\n\n      var key    = peg$currPos * 32 + 12,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.charCodeAt(peg$currPos) === 91) {\n        s1 = peg$c31;\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c32); }\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parse_();\n        if (s2 !== peg$FAILED) {\n          s3 = peg$parseattrValue();\n          if (s3 !== peg$FAILED) {\n            s4 = peg$parse_();\n            if (s4 !== peg$FAILED) {\n              if (input.charCodeAt(peg$currPos) === 93) {\n                s5 = peg$c33;\n                peg$currPos++;\n              } else {\n                s5 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c34); }\n              }\n              if (s5 !== peg$FAILED) {\n                peg$savedPos = s0;\n                s1 = peg$c35(s3);\n                s0 = s1;\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseattrOps() {\n      var s0, s1, s2;\n\n      var key    = peg$currPos * 32 + 13,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (peg$c36.test(input.charAt(peg$currPos))) {\n        s1 = input.charAt(peg$currPos);\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c37); }\n      }\n      if (s1 === peg$FAILED) {\n        s1 = null;\n      }\n      if (s1 !== peg$FAILED) {\n        if (input.charCodeAt(peg$currPos) === 61) {\n          s2 = peg$c38;\n          peg$currPos++;\n        } else {\n          s2 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c39); }\n        }\n        if (s2 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c40(s1);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n      if (s0 === peg$FAILED) {\n        if (peg$c41.test(input.charAt(peg$currPos))) {\n          s0 = input.charAt(peg$currPos);\n          peg$currPos++;\n        } else {\n          s0 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c42); }\n        }\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseattrEqOps() {\n      var s0, s1, s2;\n\n      var key    = peg$currPos * 32 + 14,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.charCodeAt(peg$currPos) === 33) {\n        s1 = peg$c22;\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c23); }\n      }\n      if (s1 === peg$FAILED) {\n        s1 = null;\n      }\n      if (s1 !== peg$FAILED) {\n        if (input.charCodeAt(peg$currPos) === 61) {\n          s2 = peg$c38;\n          peg$currPos++;\n        } else {\n          s2 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c39); }\n        }\n        if (s2 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c40(s1);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseattrName() {\n      var s0, s1, s2, s3, s4, s5;\n\n      var key    = peg$currPos * 32 + 15,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      s1 = peg$parseidentifierName();\n      if (s1 !== peg$FAILED) {\n        s2 = [];\n        s3 = peg$currPos;\n        if (input.charCodeAt(peg$currPos) === 46) {\n          s4 = peg$c43;\n          peg$currPos++;\n        } else {\n          s4 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c44); }\n        }\n        if (s4 !== peg$FAILED) {\n          s5 = peg$parseidentifierName();\n          if (s5 !== peg$FAILED) {\n            s4 = [s4, s5];\n            s3 = s4;\n          } else {\n            peg$currPos = s3;\n            s3 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s3;\n          s3 = peg$FAILED;\n        }\n        while (s3 !== peg$FAILED) {\n          s2.push(s3);\n          s3 = peg$currPos;\n          if (input.charCodeAt(peg$currPos) === 46) {\n            s4 = peg$c43;\n            peg$currPos++;\n          } else {\n            s4 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c44); }\n          }\n          if (s4 !== peg$FAILED) {\n            s5 = peg$parseidentifierName();\n            if (s5 !== peg$FAILED) {\n              s4 = [s4, s5];\n              s3 = s4;\n            } else {\n              peg$currPos = s3;\n              s3 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s3;\n            s3 = peg$FAILED;\n          }\n        }\n        if (s2 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c45(s1, s2);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseattrValue() {\n      var s0, s1, s2, s3, s4, s5;\n\n      var key    = peg$currPos * 32 + 16,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      s1 = peg$parseattrName();\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parse_();\n        if (s2 !== peg$FAILED) {\n          s3 = peg$parseattrEqOps();\n          if (s3 !== peg$FAILED) {\n            s4 = peg$parse_();\n            if (s4 !== peg$FAILED) {\n              s5 = peg$parsetype();\n              if (s5 === peg$FAILED) {\n                s5 = peg$parseregex();\n              }\n              if (s5 !== peg$FAILED) {\n                peg$savedPos = s0;\n                s1 = peg$c46(s1, s3, s5);\n                s0 = s1;\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n      if (s0 === peg$FAILED) {\n        s0 = peg$currPos;\n        s1 = peg$parseattrName();\n        if (s1 !== peg$FAILED) {\n          s2 = peg$parse_();\n          if (s2 !== peg$FAILED) {\n            s3 = peg$parseattrOps();\n            if (s3 !== peg$FAILED) {\n              s4 = peg$parse_();\n              if (s4 !== peg$FAILED) {\n                s5 = peg$parsestring();\n                if (s5 === peg$FAILED) {\n                  s5 = peg$parsenumber();\n                  if (s5 === peg$FAILED) {\n                    s5 = peg$parsepath();\n                  }\n                }\n                if (s5 !== peg$FAILED) {\n                  peg$savedPos = s0;\n                  s1 = peg$c46(s1, s3, s5);\n                  s0 = s1;\n                } else {\n                  peg$currPos = s0;\n                  s0 = peg$FAILED;\n                }\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n        if (s0 === peg$FAILED) {\n          s0 = peg$currPos;\n          s1 = peg$parseattrName();\n          if (s1 !== peg$FAILED) {\n            peg$savedPos = s0;\n            s1 = peg$c47(s1);\n          }\n          s0 = s1;\n        }\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsestring() {\n      var s0, s1, s2, s3, s4, s5;\n\n      var key    = peg$currPos * 32 + 17,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.charCodeAt(peg$currPos) === 34) {\n        s1 = peg$c48;\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c49); }\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = [];\n        if (peg$c50.test(input.charAt(peg$currPos))) {\n          s3 = input.charAt(peg$currPos);\n          peg$currPos++;\n        } else {\n          s3 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c51); }\n        }\n        if (s3 === peg$FAILED) {\n          s3 = peg$currPos;\n          if (input.charCodeAt(peg$currPos) === 92) {\n            s4 = peg$c52;\n            peg$currPos++;\n          } else {\n            s4 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c53); }\n          }\n          if (s4 !== peg$FAILED) {\n            if (input.length > peg$currPos) {\n              s5 = input.charAt(peg$currPos);\n              peg$currPos++;\n            } else {\n              s5 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c54); }\n            }\n            if (s5 !== peg$FAILED) {\n              peg$savedPos = s3;\n              s4 = peg$c55(s4, s5);\n              s3 = s4;\n            } else {\n              peg$currPos = s3;\n              s3 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s3;\n            s3 = peg$FAILED;\n          }\n        }\n        while (s3 !== peg$FAILED) {\n          s2.push(s3);\n          if (peg$c50.test(input.charAt(peg$currPos))) {\n            s3 = input.charAt(peg$currPos);\n            peg$currPos++;\n          } else {\n            s3 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c51); }\n          }\n          if (s3 === peg$FAILED) {\n            s3 = peg$currPos;\n            if (input.charCodeAt(peg$currPos) === 92) {\n              s4 = peg$c52;\n              peg$currPos++;\n            } else {\n              s4 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c53); }\n            }\n            if (s4 !== peg$FAILED) {\n              if (input.length > peg$currPos) {\n                s5 = input.charAt(peg$currPos);\n                peg$currPos++;\n              } else {\n                s5 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c54); }\n              }\n              if (s5 !== peg$FAILED) {\n                peg$savedPos = s3;\n                s4 = peg$c55(s4, s5);\n                s3 = s4;\n              } else {\n                peg$currPos = s3;\n                s3 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s3;\n              s3 = peg$FAILED;\n            }\n          }\n        }\n        if (s2 !== peg$FAILED) {\n          if (input.charCodeAt(peg$currPos) === 34) {\n            s3 = peg$c48;\n            peg$currPos++;\n          } else {\n            s3 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c49); }\n          }\n          if (s3 !== peg$FAILED) {\n            peg$savedPos = s0;\n            s1 = peg$c56(s2);\n            s0 = s1;\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n      if (s0 === peg$FAILED) {\n        s0 = peg$currPos;\n        if (input.charCodeAt(peg$currPos) === 39) {\n          s1 = peg$c57;\n          peg$currPos++;\n        } else {\n          s1 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c58); }\n        }\n        if (s1 !== peg$FAILED) {\n          s2 = [];\n          if (peg$c59.test(input.charAt(peg$currPos))) {\n            s3 = input.charAt(peg$currPos);\n            peg$currPos++;\n          } else {\n            s3 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c60); }\n          }\n          if (s3 === peg$FAILED) {\n            s3 = peg$currPos;\n            if (input.charCodeAt(peg$currPos) === 92) {\n              s4 = peg$c52;\n              peg$currPos++;\n            } else {\n              s4 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c53); }\n            }\n            if (s4 !== peg$FAILED) {\n              if (input.length > peg$currPos) {\n                s5 = input.charAt(peg$currPos);\n                peg$currPos++;\n              } else {\n                s5 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c54); }\n              }\n              if (s5 !== peg$FAILED) {\n                peg$savedPos = s3;\n                s4 = peg$c55(s4, s5);\n                s3 = s4;\n              } else {\n                peg$currPos = s3;\n                s3 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s3;\n              s3 = peg$FAILED;\n            }\n          }\n          while (s3 !== peg$FAILED) {\n            s2.push(s3);\n            if (peg$c59.test(input.charAt(peg$currPos))) {\n              s3 = input.charAt(peg$currPos);\n              peg$currPos++;\n            } else {\n              s3 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c60); }\n            }\n            if (s3 === peg$FAILED) {\n              s3 = peg$currPos;\n              if (input.charCodeAt(peg$currPos) === 92) {\n                s4 = peg$c52;\n                peg$currPos++;\n              } else {\n                s4 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c53); }\n              }\n              if (s4 !== peg$FAILED) {\n                if (input.length > peg$currPos) {\n                  s5 = input.charAt(peg$currPos);\n                  peg$currPos++;\n                } else {\n                  s5 = peg$FAILED;\n                  if (peg$silentFails === 0) { peg$fail(peg$c54); }\n                }\n                if (s5 !== peg$FAILED) {\n                  peg$savedPos = s3;\n                  s4 = peg$c55(s4, s5);\n                  s3 = s4;\n                } else {\n                  peg$currPos = s3;\n                  s3 = peg$FAILED;\n                }\n              } else {\n                peg$currPos = s3;\n                s3 = peg$FAILED;\n              }\n            }\n          }\n          if (s2 !== peg$FAILED) {\n            if (input.charCodeAt(peg$currPos) === 39) {\n              s3 = peg$c57;\n              peg$currPos++;\n            } else {\n              s3 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c58); }\n            }\n            if (s3 !== peg$FAILED) {\n              peg$savedPos = s0;\n              s1 = peg$c56(s2);\n              s0 = s1;\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsenumber() {\n      var s0, s1, s2, s3;\n\n      var key    = peg$currPos * 32 + 18,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      s1 = peg$currPos;\n      s2 = [];\n      if (peg$c61.test(input.charAt(peg$currPos))) {\n        s3 = input.charAt(peg$currPos);\n        peg$currPos++;\n      } else {\n        s3 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c62); }\n      }\n      while (s3 !== peg$FAILED) {\n        s2.push(s3);\n        if (peg$c61.test(input.charAt(peg$currPos))) {\n          s3 = input.charAt(peg$currPos);\n          peg$currPos++;\n        } else {\n          s3 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c62); }\n        }\n      }\n      if (s2 !== peg$FAILED) {\n        if (input.charCodeAt(peg$currPos) === 46) {\n          s3 = peg$c43;\n          peg$currPos++;\n        } else {\n          s3 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c44); }\n        }\n        if (s3 !== peg$FAILED) {\n          s2 = [s2, s3];\n          s1 = s2;\n        } else {\n          peg$currPos = s1;\n          s1 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s1;\n        s1 = peg$FAILED;\n      }\n      if (s1 === peg$FAILED) {\n        s1 = null;\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = [];\n        if (peg$c61.test(input.charAt(peg$currPos))) {\n          s3 = input.charAt(peg$currPos);\n          peg$currPos++;\n        } else {\n          s3 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c62); }\n        }\n        if (s3 !== peg$FAILED) {\n          while (s3 !== peg$FAILED) {\n            s2.push(s3);\n            if (peg$c61.test(input.charAt(peg$currPos))) {\n              s3 = input.charAt(peg$currPos);\n              peg$currPos++;\n            } else {\n              s3 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c62); }\n            }\n          }\n        } else {\n          s2 = peg$FAILED;\n        }\n        if (s2 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c63(s1, s2);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsepath() {\n      var s0, s1;\n\n      var key    = peg$currPos * 32 + 19,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      s1 = peg$parseidentifierName();\n      if (s1 !== peg$FAILED) {\n        peg$savedPos = s0;\n        s1 = peg$c64(s1);\n      }\n      s0 = s1;\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsetype() {\n      var s0, s1, s2, s3, s4, s5;\n\n      var key    = peg$currPos * 32 + 20,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.substr(peg$currPos, 5) === peg$c65) {\n        s1 = peg$c65;\n        peg$currPos += 5;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c66); }\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parse_();\n        if (s2 !== peg$FAILED) {\n          s3 = [];\n          if (peg$c67.test(input.charAt(peg$currPos))) {\n            s4 = input.charAt(peg$currPos);\n            peg$currPos++;\n          } else {\n            s4 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c68); }\n          }\n          if (s4 !== peg$FAILED) {\n            while (s4 !== peg$FAILED) {\n              s3.push(s4);\n              if (peg$c67.test(input.charAt(peg$currPos))) {\n                s4 = input.charAt(peg$currPos);\n                peg$currPos++;\n              } else {\n                s4 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c68); }\n              }\n            }\n          } else {\n            s3 = peg$FAILED;\n          }\n          if (s3 !== peg$FAILED) {\n            s4 = peg$parse_();\n            if (s4 !== peg$FAILED) {\n              if (input.charCodeAt(peg$currPos) === 41) {\n                s5 = peg$c69;\n                peg$currPos++;\n              } else {\n                s5 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c70); }\n              }\n              if (s5 !== peg$FAILED) {\n                peg$savedPos = s0;\n                s1 = peg$c71(s3);\n                s0 = s1;\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseflags() {\n      var s0, s1;\n\n      var key    = peg$currPos * 32 + 21,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = [];\n      if (peg$c72.test(input.charAt(peg$currPos))) {\n        s1 = input.charAt(peg$currPos);\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c73); }\n      }\n      if (s1 !== peg$FAILED) {\n        while (s1 !== peg$FAILED) {\n          s0.push(s1);\n          if (peg$c72.test(input.charAt(peg$currPos))) {\n            s1 = input.charAt(peg$currPos);\n            peg$currPos++;\n          } else {\n            s1 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c73); }\n          }\n        }\n      } else {\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseregex() {\n      var s0, s1, s2, s3, s4;\n\n      var key    = peg$currPos * 32 + 22,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.charCodeAt(peg$currPos) === 47) {\n        s1 = peg$c74;\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c75); }\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = [];\n        if (peg$c76.test(input.charAt(peg$currPos))) {\n          s3 = input.charAt(peg$currPos);\n          peg$currPos++;\n        } else {\n          s3 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c77); }\n        }\n        if (s3 !== peg$FAILED) {\n          while (s3 !== peg$FAILED) {\n            s2.push(s3);\n            if (peg$c76.test(input.charAt(peg$currPos))) {\n              s3 = input.charAt(peg$currPos);\n              peg$currPos++;\n            } else {\n              s3 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c77); }\n            }\n          }\n        } else {\n          s2 = peg$FAILED;\n        }\n        if (s2 !== peg$FAILED) {\n          if (input.charCodeAt(peg$currPos) === 47) {\n            s3 = peg$c74;\n            peg$currPos++;\n          } else {\n            s3 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c75); }\n          }\n          if (s3 !== peg$FAILED) {\n            s4 = peg$parseflags();\n            if (s4 === peg$FAILED) {\n              s4 = null;\n            }\n            if (s4 !== peg$FAILED) {\n              peg$savedPos = s0;\n              s1 = peg$c78(s2, s4);\n              s0 = s1;\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsefield() {\n      var s0, s1, s2, s3, s4, s5, s6;\n\n      var key    = peg$currPos * 32 + 23,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.charCodeAt(peg$currPos) === 46) {\n        s1 = peg$c43;\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c44); }\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parseidentifierName();\n        if (s2 !== peg$FAILED) {\n          s3 = [];\n          s4 = peg$currPos;\n          if (input.charCodeAt(peg$currPos) === 46) {\n            s5 = peg$c43;\n            peg$currPos++;\n          } else {\n            s5 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c44); }\n          }\n          if (s5 !== peg$FAILED) {\n            s6 = peg$parseidentifierName();\n            if (s6 !== peg$FAILED) {\n              s5 = [s5, s6];\n              s4 = s5;\n            } else {\n              peg$currPos = s4;\n              s4 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s4;\n            s4 = peg$FAILED;\n          }\n          while (s4 !== peg$FAILED) {\n            s3.push(s4);\n            s4 = peg$currPos;\n            if (input.charCodeAt(peg$currPos) === 46) {\n              s5 = peg$c43;\n              peg$currPos++;\n            } else {\n              s5 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c44); }\n            }\n            if (s5 !== peg$FAILED) {\n              s6 = peg$parseidentifierName();\n              if (s6 !== peg$FAILED) {\n                s5 = [s5, s6];\n                s4 = s5;\n              } else {\n                peg$currPos = s4;\n                s4 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s4;\n              s4 = peg$FAILED;\n            }\n          }\n          if (s3 !== peg$FAILED) {\n            peg$savedPos = s0;\n            s1 = peg$c79(s2, s3);\n            s0 = s1;\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsenegation() {\n      var s0, s1, s2, s3, s4, s5;\n\n      var key    = peg$currPos * 32 + 24,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.substr(peg$currPos, 5) === peg$c80) {\n        s1 = peg$c80;\n        peg$currPos += 5;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c81); }\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parse_();\n        if (s2 !== peg$FAILED) {\n          s3 = peg$parseselectors();\n          if (s3 !== peg$FAILED) {\n            s4 = peg$parse_();\n            if (s4 !== peg$FAILED) {\n              if (input.charCodeAt(peg$currPos) === 41) {\n                s5 = peg$c69;\n                peg$currPos++;\n              } else {\n                s5 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c70); }\n              }\n              if (s5 !== peg$FAILED) {\n                peg$savedPos = s0;\n                s1 = peg$c82(s3);\n                s0 = s1;\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsematches() {\n      var s0, s1, s2, s3, s4, s5;\n\n      var key    = peg$currPos * 32 + 25,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.substr(peg$currPos, 9) === peg$c83) {\n        s1 = peg$c83;\n        peg$currPos += 9;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c84); }\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parse_();\n        if (s2 !== peg$FAILED) {\n          s3 = peg$parseselectors();\n          if (s3 !== peg$FAILED) {\n            s4 = peg$parse_();\n            if (s4 !== peg$FAILED) {\n              if (input.charCodeAt(peg$currPos) === 41) {\n                s5 = peg$c69;\n                peg$currPos++;\n              } else {\n                s5 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c70); }\n              }\n              if (s5 !== peg$FAILED) {\n                peg$savedPos = s0;\n                s1 = peg$c85(s3);\n                s0 = s1;\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsehas() {\n      var s0, s1, s2, s3, s4, s5;\n\n      var key    = peg$currPos * 32 + 26,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.substr(peg$currPos, 5) === peg$c86) {\n        s1 = peg$c86;\n        peg$currPos += 5;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c87); }\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parse_();\n        if (s2 !== peg$FAILED) {\n          s3 = peg$parsehasSelectors();\n          if (s3 !== peg$FAILED) {\n            s4 = peg$parse_();\n            if (s4 !== peg$FAILED) {\n              if (input.charCodeAt(peg$currPos) === 41) {\n                s5 = peg$c69;\n                peg$currPos++;\n              } else {\n                s5 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c70); }\n              }\n              if (s5 !== peg$FAILED) {\n                peg$savedPos = s0;\n                s1 = peg$c88(s3);\n                s0 = s1;\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsefirstChild() {\n      var s0, s1;\n\n      var key    = peg$currPos * 32 + 27,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.substr(peg$currPos, 12) === peg$c89) {\n        s1 = peg$c89;\n        peg$currPos += 12;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c90); }\n      }\n      if (s1 !== peg$FAILED) {\n        peg$savedPos = s0;\n        s1 = peg$c91();\n      }\n      s0 = s1;\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parselastChild() {\n      var s0, s1;\n\n      var key    = peg$currPos * 32 + 28,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.substr(peg$currPos, 11) === peg$c92) {\n        s1 = peg$c92;\n        peg$currPos += 11;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c93); }\n      }\n      if (s1 !== peg$FAILED) {\n        peg$savedPos = s0;\n        s1 = peg$c94();\n      }\n      s0 = s1;\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsenthChild() {\n      var s0, s1, s2, s3, s4, s5;\n\n      var key    = peg$currPos * 32 + 29,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.substr(peg$currPos, 11) === peg$c95) {\n        s1 = peg$c95;\n        peg$currPos += 11;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c96); }\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parse_();\n        if (s2 !== peg$FAILED) {\n          s3 = [];\n          if (peg$c61.test(input.charAt(peg$currPos))) {\n            s4 = input.charAt(peg$currPos);\n            peg$currPos++;\n          } else {\n            s4 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c62); }\n          }\n          if (s4 !== peg$FAILED) {\n            while (s4 !== peg$FAILED) {\n              s3.push(s4);\n              if (peg$c61.test(input.charAt(peg$currPos))) {\n                s4 = input.charAt(peg$currPos);\n                peg$currPos++;\n              } else {\n                s4 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c62); }\n              }\n            }\n          } else {\n            s3 = peg$FAILED;\n          }\n          if (s3 !== peg$FAILED) {\n            s4 = peg$parse_();\n            if (s4 !== peg$FAILED) {\n              if (input.charCodeAt(peg$currPos) === 41) {\n                s5 = peg$c69;\n                peg$currPos++;\n              } else {\n                s5 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c70); }\n              }\n              if (s5 !== peg$FAILED) {\n                peg$savedPos = s0;\n                s1 = peg$c97(s3);\n                s0 = s1;\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsenthLastChild() {\n      var s0, s1, s2, s3, s4, s5;\n\n      var key    = peg$currPos * 32 + 30,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.substr(peg$currPos, 16) === peg$c98) {\n        s1 = peg$c98;\n        peg$currPos += 16;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c99); }\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parse_();\n        if (s2 !== peg$FAILED) {\n          s3 = [];\n          if (peg$c61.test(input.charAt(peg$currPos))) {\n            s4 = input.charAt(peg$currPos);\n            peg$currPos++;\n          } else {\n            s4 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c62); }\n          }\n          if (s4 !== peg$FAILED) {\n            while (s4 !== peg$FAILED) {\n              s3.push(s4);\n              if (peg$c61.test(input.charAt(peg$currPos))) {\n                s4 = input.charAt(peg$currPos);\n                peg$currPos++;\n              } else {\n                s4 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c62); }\n              }\n            }\n          } else {\n            s3 = peg$FAILED;\n          }\n          if (s3 !== peg$FAILED) {\n            s4 = peg$parse_();\n            if (s4 !== peg$FAILED) {\n              if (input.charCodeAt(peg$currPos) === 41) {\n                s5 = peg$c69;\n                peg$currPos++;\n              } else {\n                s5 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c70); }\n              }\n              if (s5 !== peg$FAILED) {\n                peg$savedPos = s0;\n                s1 = peg$c100(s3);\n                s0 = s1;\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseclass() {\n      var s0, s1, s2;\n\n      var key    = peg$currPos * 32 + 31,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.charCodeAt(peg$currPos) === 58) {\n        s1 = peg$c101;\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c102); }\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parseidentifierName();\n        if (s2 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c103(s2);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n\n      function nth(n) { return { type: 'nth-child', index: { type: 'literal', value: n } }; }\n      function nthLast(n) { return { type: 'nth-last-child', index: { type: 'literal', value: n } }; }\n      function strUnescape(s) {\n        return s.replace(/\\\\(.)/g, function(match, ch) {\n          switch(ch) {\n            case 'b': return '\\b';\n            case 'f': return '\\f';\n            case 'n': return '\\n';\n            case 'r': return '\\r';\n            case 't': return '\\t';\n            case 'v': return '\\v';\n            default: return ch;\n          }\n        });\n      }\n\n\n    peg$result = peg$startRuleFunction();\n\n    if (peg$result !== peg$FAILED && peg$currPos === input.length) {\n      return peg$result;\n    } else {\n      if (peg$result !== peg$FAILED && peg$currPos < input.length) {\n        peg$fail(peg$endExpectation());\n      }\n\n      throw peg$buildStructuredError(\n        peg$maxFailExpected,\n        peg$maxFailPos < input.length ? input.charAt(peg$maxFailPos) : null,\n        peg$maxFailPos < input.length\n          ? peg$computeLocation(peg$maxFailPos, peg$maxFailPos + 1)\n          : peg$computeLocation(peg$maxFailPos, peg$maxFailPos)\n      );\n    }\n  }\n\n  return {\n    SyntaxError: peg$SyntaxError,\n    parse:       peg$parse\n  };\n});\n", "/* vim: set sw=4 sts=4 : */\nimport estraverse from 'estraverse';\nimport parser from './parser.js';\n\n/**\n* @typedef {\"LEFT_SIDE\"|\"RIGHT_SIDE\"} Side\n*/\n\nconst LEFT_SIDE = 'LEFT_SIDE';\nconst RIGHT_SIDE = 'RIGHT_SIDE';\n\n/**\n * @external AST\n * @see https://esprima.readthedocs.io/en/latest/syntax-tree-format.html\n */\n\n/**\n * One of the rules of `grammar.pegjs`\n * @typedef {PlainObject} SelectorAST\n * @see grammar.pegjs\n*/\n\n/**\n * The `sequence` production of `grammar.pegjs`\n * @typedef {PlainObject} SelectorSequenceAST\n*/\n\n/**\n * Get the value of a property which may be multiple levels down\n * in the object.\n * @param {?PlainObject} obj\n * @param {string[]} keys\n * @returns {undefined|boolean|string|number|external:AST}\n */\nfunction getPath(obj, keys) {\n    for (let i = 0; i < keys.length; ++i) {\n        if (obj == null) { return obj; }\n        obj = obj[keys[i]];\n    }\n    return obj;\n}\n\n/**\n * Determine whether `node` can be reached by following `path`,\n * starting at `ancestor`.\n * @param {?external:AST} node\n * @param {?external:AST} ancestor\n * @param {string[]} path\n * @param {Integer} fromPathIndex\n * @returns {boolean}\n */\nfunction inPath(node, ancestor, path, fromPathIndex) {\n    let current = ancestor;\n    for (let i = fromPathIndex; i < path.length; ++i) {\n        if (current == null) {\n            return false;\n        }\n        const field = current[path[i]];\n        if (Array.isArray(field)) {\n            for (let k = 0; k < field.length; ++k) {\n                if (inPath(node, field[k], path, i + 1)) {\n                    return true;\n                }\n            }\n            return false;\n        }\n        current = field;\n    }\n    return node === current;\n}\n\n/**\n * A generated matcher function for a selector.\n * @callback SelectorMatcher\n * @param {?SelectorAST} selector\n * @param {external:AST[]} [ancestry=[]]\n * @param {ESQueryOptions} [options]\n * @returns {void}\n*/\n\n/**\n * A WeakMap for holding cached matcher functions for selectors.\n * @type {WeakMap<SelectorAST, SelectorMatcher>}\n*/\nconst MATCHER_CACHE = typeof WeakMap === 'function' ? new WeakMap : null;\n\n/**\n * Look up a matcher function for `selector` in the cache.\n * If it does not exist, generate it with `generateMatcher` and add it to the cache.\n * In engines without WeakMap, the caching is skipped and matchers are generated with every call.\n * @param {?SelectorAST} selector\n * @returns {SelectorMatcher}\n */\nfunction getMatcher(selector) {\n    if (selector == null) {\n        return () => true;\n    }\n\n    if (MATCHER_CACHE != null) {\n        let matcher = MATCHER_CACHE.get(selector);\n        if (matcher != null) {\n            return matcher;\n        }\n        matcher = generateMatcher(selector);\n        MATCHER_CACHE.set(selector, matcher);\n        return matcher;\n    }\n\n    return generateMatcher(selector);\n}\n\n/**\n * Create a matcher function for `selector`,\n * @param {?SelectorAST} selector\n * @returns {SelectorMatcher}\n */\nfunction generateMatcher(selector) {\n    switch(selector.type) {\n        case 'wildcard':\n            return () => true;\n\n        case 'identifier': {\n            const value = selector.value.toLowerCase();\n            return (node, ancestry, options) => {\n                const nodeTypeKey = (options && options.nodeTypeKey) || 'type';\n                return value === node[nodeTypeKey].toLowerCase();\n            };\n        }\n\n        case 'exactNode':\n            return (node, ancestry) => {\n                return ancestry.length === 0;\n            };\n\n        case 'field': {\n            const path = selector.name.split('.');\n            return (node, ancestry) => {\n                const ancestor = ancestry[path.length - 1];\n                return inPath(node, ancestor, path, 0);\n            };\n        }\n\n        case 'matches': {\n            const matchers = selector.selectors.map(getMatcher);\n            return (node, ancestry, options) => {\n                for (let i = 0; i < matchers.length; ++i) {\n                    if (matchers[i](node, ancestry, options)) { return true; }\n                }\n                return false;\n            };\n        }\n\n        case 'compound': {\n            const matchers = selector.selectors.map(getMatcher);\n            return (node, ancestry, options) => {\n                for (let i = 0; i < matchers.length; ++i) {\n                    if (!matchers[i](node, ancestry, options)) { return false; }\n                }\n                return true;\n            };\n        }\n\n        case 'not': {\n            const matchers = selector.selectors.map(getMatcher);\n            return (node, ancestry, options) => {\n                for (let i = 0; i < matchers.length; ++i) {\n                    if (matchers[i](node, ancestry, options)) { return false; }\n                }\n                return true;\n            };\n        }\n\n        case 'has': {\n            const matchers = selector.selectors.map(getMatcher);\n            return (node, ancestry, options) => {\n                let result = false;\n\n                const a = [];\n                estraverse.traverse(node, {\n                    enter (node, parent) {\n                        if (parent != null) { a.unshift(parent); }\n\n                        for (let i = 0; i < matchers.length; ++i) {\n                            if (matchers[i](node, a, options)) {\n                                result = true;\n                                this.break();\n                                return;\n                            }\n                        }\n                    },\n                    leave () { a.shift(); },\n                    keys: options && options.visitorKeys,\n                    fallback: options && options.fallback || 'iteration'\n                });\n\n                return result;\n            };\n        }\n\n        case 'child': {\n            const left = getMatcher(selector.left);\n            const right = getMatcher(selector.right);\n            return (node, ancestry, options) => {\n                if (ancestry.length > 0 && right(node, ancestry, options)) {\n                    return left(ancestry[0], ancestry.slice(1), options);\n                }\n                return false;\n            };\n        }\n\n        case 'descendant': {\n            const left = getMatcher(selector.left);\n            const right = getMatcher(selector.right);\n            return (node, ancestry, options) => {\n                if (right(node, ancestry, options)) {\n                    for (let i = 0, l = ancestry.length; i < l; ++i) {\n                        if (left(ancestry[i], ancestry.slice(i + 1), options)) {\n                            return true;\n                        }\n                    }\n                }\n                return false;\n            };\n        }\n\n        case 'attribute': {\n            const path = selector.name.split('.');\n            switch (selector.operator) {\n                case void 0:\n                    return (node) => getPath(node, path) != null;\n                case '=':\n                    switch (selector.value.type) {\n                        case 'regexp':\n                            return (node) => {\n                                const p = getPath(node, path);\n                                return typeof p === 'string' && selector.value.value.test(p);\n                            };\n                        case 'literal': {\n                            const literal = `${selector.value.value}`;\n                            return (node) => literal === `${getPath(node, path)}`;\n                        }\n                        case 'type':\n                            return (node) => selector.value.value === typeof getPath(node, path);\n                    }\n                    throw new Error(`Unknown selector value type: ${selector.value.type}`);\n                case '!=':\n                    switch (selector.value.type) {\n                        case 'regexp':\n                            return (node) => !selector.value.value.test(getPath(node, path));\n                        case 'literal': {\n                            const literal = `${selector.value.value}`;\n                            return (node) => literal !== `${getPath(node, path)}`;\n                        }\n                        case 'type':\n                            return (node) => selector.value.value !== typeof getPath(node, path);\n                    }\n                    throw new Error(`Unknown selector value type: ${selector.value.type}`);\n                case '<=':\n                    return (node) => getPath(node, path) <= selector.value.value;\n                case '<':\n                    return (node) => getPath(node, path) < selector.value.value;\n                case '>':\n                    return (node) => getPath(node, path) > selector.value.value;\n                case '>=':\n                    return (node) => getPath(node, path) >= selector.value.value;\n            }\n            throw new Error(`Unknown operator: ${selector.operator}`);\n        }\n\n        case 'sibling': {\n            const left = getMatcher(selector.left);\n            const right = getMatcher(selector.right);\n            return (node, ancestry, options) =>\n                right(node, ancestry, options) &&\n                    sibling(node, left, ancestry, LEFT_SIDE, options) ||\n                    selector.left.subject &&\n                    left(node, ancestry, options) &&\n                    sibling(node, right, ancestry, RIGHT_SIDE, options);\n        }\n\n        case 'adjacent': {\n            const left = getMatcher(selector.left);\n            const right = getMatcher(selector.right);\n            return (node, ancestry, options) =>\n                right(node, ancestry, options) &&\n                    adjacent(node, left, ancestry, LEFT_SIDE, options) ||\n                    selector.right.subject &&\n                    left(node, ancestry, options) &&\n                    adjacent(node, right, ancestry, RIGHT_SIDE, options);\n        }\n\n        case 'nth-child': {\n            const nth = selector.index.value;\n            const right = getMatcher(selector.right);\n            return (node, ancestry, options) =>\n                right(node, ancestry, options) &&\n                    nthChild(node, ancestry, nth, options);\n        }\n\n        case 'nth-last-child': {\n            const nth = -selector.index.value;\n            const right = getMatcher(selector.right);\n            return (node, ancestry, options) =>\n                right(node, ancestry, options) &&\n                    nthChild(node, ancestry, nth, options);\n        }\n\n        case 'class': {\n            \n            const name = selector.name.toLowerCase();\n\n            return (node, ancestry, options) => {\n                \n                if (options && options.matchClass) {\n                    return options.matchClass(selector.name, node, ancestry);\n                }\n                \n                if (options && options.nodeTypeKey) return false;    \n\n                switch(name){\n                    case 'statement':\n                        if(node.type.slice(-9) === 'Statement') return true;\n                        // fallthrough: interface Declaration <: Statement { }\n                    case 'declaration':\n                        return node.type.slice(-11) === 'Declaration';\n                    case 'pattern':\n                        if(node.type.slice(-7) === 'Pattern') return true;\n                        // fallthrough: interface Expression <: Node, Pattern { }\n                    case 'expression':\n                        return node.type.slice(-10) === 'Expression' ||\n                            node.type.slice(-7) === 'Literal' ||\n                            (\n                                node.type === 'Identifier' &&\n                                (ancestry.length === 0 || ancestry[0].type !== 'MetaProperty')\n                            ) ||\n                            node.type === 'MetaProperty';\n                    case 'function':\n                        return node.type === 'FunctionDeclaration' ||\n                            node.type === 'FunctionExpression' ||\n                            node.type === 'ArrowFunctionExpression';\n                }\n                throw new Error(`Unknown class name: ${selector.name}`);\n            };\n        }\n    }\n\n    throw new Error(`Unknown selector type: ${selector.type}`);\n}\n\n/**\n * @callback TraverseOptionFallback\n * @param {external:AST} node The given node.\n * @returns {string[]} An array of visitor keys for the given node.\n */\n\n/**\n * @callback ClassMatcher\n * @param {string} className The name of the class to match.\n * @param {external:AST} node The node to match against.\n * @param {Array<external:AST>} ancestry The ancestry of the node.\n * @returns {boolean} True if the node matches the class, false if not.\n */\n\n/**\n * @typedef {object} ESQueryOptions\n * @property {string} [nodeTypeKey=\"type\"] By passing `nodeTypeKey`, we can allow other ASTs to use ESQuery.\n * @property { { [nodeType: string]: string[] } } [visitorKeys] By passing `visitorKeys` mapping, we can extend the properties of the nodes that traverse the node.\n * @property {TraverseOptionFallback} [fallback] By passing `fallback` option, we can control the properties of traversing nodes when encountering unknown nodes.\n * @property {ClassMatcher} [matchClass] By passing `matchClass` option, we can customize the interpretation of classes.\n */\n\n/**\n * Given a `node` and its ancestors, determine if `node` is matched\n * by `selector`.\n * @param {?external:AST} node\n * @param {?SelectorAST} selector\n * @param {external:AST[]} [ancestry=[]]\n * @param {ESQueryOptions} [options]\n * @throws {Error} Unknowns (operator, class name, selector type, or\n * selector value type)\n * @returns {boolean}\n */\nfunction matches(node, selector, ancestry, options) {\n    if (!selector) { return true; }\n    if (!node) { return false; }\n    if (!ancestry) { ancestry = []; }\n\n    return getMatcher(selector)(node, ancestry, options);\n}\n\n/**\n * Get visitor keys of a given node.\n * @param {external:AST} node The AST node to get keys.\n * @param {ESQueryOptions|undefined} options\n * @returns {string[]} Visitor keys of the node.\n */\nfunction getVisitorKeys(node, options) {\n    const nodeTypeKey = (options && options.nodeTypeKey) || 'type';\n\n    const nodeType = node[nodeTypeKey];\n    if (options && options.visitorKeys && options.visitorKeys[nodeType]) {\n        return options.visitorKeys[nodeType];\n    }\n    if (estraverse.VisitorKeys[nodeType]) {\n        return estraverse.VisitorKeys[nodeType];\n    }\n    if (options && typeof options.fallback === 'function') {\n        return options.fallback(node);\n    }\n    // 'iteration' fallback\n    return Object.keys(node).filter(function (key) {\n        return key !== nodeTypeKey;\n    });\n}\n\n\n/**\n * Check whether the given value is an ASTNode or not.\n * @param {any} node The value to check.\n * @param {ESQueryOptions|undefined} options The options to use.\n * @returns {boolean} `true` if the value is an ASTNode.\n */\nfunction isNode(node, options) {\n    const nodeTypeKey = (options && options.nodeTypeKey) || 'type';\n    return node !== null && typeof node === 'object' && typeof node[nodeTypeKey] === 'string';\n}\n\n/**\n * Determines if the given node has a sibling that matches the\n * given selector matcher.\n * @param {external:AST} node\n * @param {SelectorMatcher} matcher\n * @param {external:AST[]} ancestry\n * @param {Side} side\n * @param {ESQueryOptions|undefined} options\n * @returns {boolean}\n */\nfunction sibling(node, matcher, ancestry, side, options) {\n    const [parent] = ancestry;\n    if (!parent) { return false; }\n    const keys = getVisitorKeys(parent, options);\n    for (let i = 0; i < keys.length; ++i) {\n        const listProp = parent[keys[i]];\n        if (Array.isArray(listProp)) {\n            const startIndex = listProp.indexOf(node);\n            if (startIndex < 0) { continue; }\n            let lowerBound, upperBound;\n            if (side === LEFT_SIDE) {\n                lowerBound = 0;\n                upperBound = startIndex;\n            } else {\n                lowerBound = startIndex + 1;\n                upperBound = listProp.length;\n            }\n            for (let k = lowerBound; k < upperBound; ++k) {\n                if (isNode(listProp[k], options) && matcher(listProp[k], ancestry, options)) {\n                    return true;\n                }\n            }\n        }\n    }\n    return false;\n}\n\n/**\n * Determines if the given node has an adjacent sibling that matches\n * the given selector matcher.\n * @param {external:AST} node\n * @param {SelectorMatcher} matcher\n * @param {external:AST[]} ancestry\n * @param {Side} side\n * @param {ESQueryOptions|undefined} options\n * @returns {boolean}\n */\nfunction adjacent(node, matcher, ancestry, side, options) {\n    const [parent] = ancestry;\n    if (!parent) { return false; }\n    const keys = getVisitorKeys(parent, options);\n    for (let i = 0; i < keys.length; ++i) {\n        const listProp = parent[keys[i]];\n        if (Array.isArray(listProp)) {\n            const idx = listProp.indexOf(node);\n            if (idx < 0) { continue; }\n            if (side === LEFT_SIDE && idx > 0 && isNode(listProp[idx - 1], options) && matcher(listProp[idx - 1], ancestry, options)) {\n                return true;\n            }\n            if (side === RIGHT_SIDE && idx < listProp.length - 1 && isNode(listProp[idx + 1], options) &&  matcher(listProp[idx + 1], ancestry, options)) {\n                return true;\n            }\n        }\n    }\n    return false;\n}\n\n/**\n * Determines if the given node is the `nth` child.\n * If `nth` is negative then the position is counted\n * from the end of the list of children.\n * @param {external:AST} node\n * @param {external:AST[]} ancestry\n * @param {Integer} nth\n * @param {ESQueryOptions|undefined} options\n * @returns {boolean}\n */\nfunction nthChild(node, ancestry, nth, options) {\n    if (nth === 0) { return false; }\n    const [parent] = ancestry;\n    if (!parent) { return false; }\n    const keys = getVisitorKeys(parent, options);\n    for (let i = 0; i < keys.length; ++i) {\n        const listProp = parent[keys[i]];\n        if (Array.isArray(listProp)){\n            const idx = nth < 0 ? listProp.length + nth : nth - 1;\n            if (idx >= 0 && idx < listProp.length && listProp[idx] === node) {\n                return true;\n            }\n        }\n    }\n    return false;\n}\n\n/**\n * For each selector node marked as a subject, find the portion of the\n * selector that the subject must match.\n * @param {SelectorAST} selector\n * @param {SelectorAST} [ancestor] Defaults to `selector`\n * @returns {SelectorAST[]}\n */\nfunction subjects(selector, ancestor) {\n    if (selector == null || typeof selector != 'object') { return []; }\n    if (ancestor == null) { ancestor = selector; }\n    const results = selector.subject ? [ancestor] : [];\n    const keys = Object.keys(selector);\n    for (let i = 0; i < keys.length; ++i) {\n        const p = keys[i];\n        const sel = selector[p];\n        results.push(...subjects(sel, p === 'left' ? sel : ancestor));\n    }\n    return results;\n}\n\n/**\n* @callback TraverseVisitor\n* @param {?external:AST} node\n* @param {?external:AST} parent\n* @param {external:AST[]} ancestry\n*/\n\n/**\n * From a JS AST and a selector AST, collect all JS AST nodes that\n * match the selector.\n * @param {external:AST} ast\n * @param {?SelectorAST} selector\n * @param {TraverseVisitor} visitor\n * @param {ESQueryOptions} [options]\n * @returns {external:AST[]}\n */\nfunction traverse(ast, selector, visitor, options) {\n    if (!selector) { return; }\n    const ancestry = [];\n    const matcher = getMatcher(selector);\n    const altSubjects = subjects(selector).map(getMatcher);\n    estraverse.traverse(ast, {\n        enter (node, parent) {\n            if (parent != null) { ancestry.unshift(parent); }\n            if (matcher(node, ancestry, options)) {\n                if (altSubjects.length) {\n                    for (let i = 0, l = altSubjects.length; i < l; ++i) {\n                        if (altSubjects[i](node, ancestry, options)) {\n                            visitor(node, parent, ancestry);\n                        }\n                        for (let k = 0, m = ancestry.length; k < m; ++k) {\n                            const succeedingAncestry = ancestry.slice(k + 1);\n                            if (altSubjects[i](ancestry[k], succeedingAncestry, options)) {\n                                visitor(ancestry[k], parent, succeedingAncestry);\n                            }\n                        }\n                    }\n                } else {\n                    visitor(node, parent, ancestry);\n                }\n            }\n        },\n        leave () { ancestry.shift(); },\n        keys: options && options.visitorKeys,\n        fallback: options && options.fallback || 'iteration'\n    });\n}\n\n\n/**\n * From a JS AST and a selector AST, collect all JS AST nodes that\n * match the selector.\n * @param {external:AST} ast\n * @param {?SelectorAST} selector\n * @param {ESQueryOptions} [options]\n * @returns {external:AST[]}\n */\nfunction match(ast, selector, options) {\n    const results = [];\n    traverse(ast, selector, function (node) {\n        results.push(node);\n    }, options);\n    return results;\n}\n\n/**\n * Parse a selector string and return its AST.\n * @param {string} selector\n * @returns {SelectorAST}\n */\nfunction parse(selector) {\n    return parser.parse(selector);\n}\n\n/**\n * Query the code AST using the selector string.\n * @param {external:AST} ast\n * @param {string} selector\n * @param {ESQueryOptions} [options]\n * @returns {external:AST[]}\n */\nfunction query(ast, selector, options) {\n    return match(ast, parse(selector), options);\n}\n\nquery.parse = parse;\nquery.match = match;\nquery.traverse = traverse;\nquery.matches = matches;\nquery.query = query;\n\nexport default query;\n"], "names": ["clone", "exports", "Syntax", "VisitorOption", "VisitorKeys", "BREAK", "SKIP", "REMOVE", "deepCopy", "obj", "key", "val", "ret", "hasOwnProperty", "Reference", "parent", "this", "Element", "node", "path", "wrap", "ref", "Controller", "isNode", "type", "isProperty", "nodeType", "ObjectExpression", "ObjectPattern", "candidateExistsInLeaveList", "leavelist", "candidate", "i", "length", "traverse", "root", "visitor", "extendCommentRange", "comment", "tokens", "target", "array", "func", "diff", "len", "current", "upperBound", "token", "range", "extendedRange", "AssignmentExpression", "AssignmentPattern", "ArrayExpression", "ArrayPattern", "ArrowFunctionExpression", "AwaitExpression", "BlockStatement", "BinaryExpression", "BreakStatement", "CallExpression", "CatchClause", "ChainExpression", "ClassBody", "ClassDeclaration", "ClassExpression", "ComprehensionBlock", "ComprehensionExpression", "ConditionalExpression", "ContinueStatement", "DebuggerStatement", "DirectiveStatement", "DoWhileStatement", "EmptyStatement", "ExportAllDeclaration", "ExportDefaultDeclaration", "ExportNamedDeclaration", "ExportSpecifier", "ExpressionStatement", "ForStatement", "ForInStatement", "ForOfStatement", "FunctionDeclaration", "FunctionExpression", "GeneratorExpression", "Identifier", "IfStatement", "ImportExpression", "ImportDeclaration", "ImportDefaultSpecifier", "ImportNamespaceSpecifier", "ImportSpecifier", "Literal", "LabeledStatement", "LogicalExpression", "MemberExpression", "MetaProperty", "MethodDefinition", "ModuleSpecifier", "NewExpression", "PrivateIdentifier", "Program", "Property", "PropertyDefinition", "RestElement", "ReturnStatement", "SequenceExpression", "SpreadElement", "Super", "SwitchStatement", "SwitchCase", "TaggedTemplateExpression", "TemplateElement", "TemplateLiteral", "ThisExpression", "ThrowStatement", "TryStatement", "UnaryExpression", "UpdateExpression", "VariableDeclaration", "VariableDeclarator", "WhileStatement", "WithStatement", "YieldExpression", "Break", "<PERSON><PERSON>", "Remove", "prototype", "replace", "remove", "Array", "isArray", "splice", "iz", "j", "jz", "result", "addToPath", "push", "__current", "__leavelist", "parents", "__execute", "callback", "element", "previous", "undefined", "__state", "call", "notify", "flag", "skip", "__initialize", "__worklist", "__fallback", "fallback", "Object", "keys", "__keys", "assign", "create", "worklist", "current2", "candidates", "sentinel", "pop", "enter", "Error", "leave", "outer", "removeElem", "nextElem", "attachComments", "tree", "providedComments", "cursor", "comments", "leadingComments", "trailingComments", "cloneEnvironment", "module", "peg$SyntaxError", "message", "expected", "found", "location", "name", "captureStackTrace", "child", "ctor", "constructor", "peg$subclass", "buildMessage", "DESCRIBE_EXPECTATION_FNS", "literal", "expectation", "literalEscape", "text", "class", "escapedParts", "parts", "classEscape", "inverted", "any", "end", "other", "description", "hex", "ch", "charCodeAt", "toString", "toUpperCase", "s", "descriptions", "sort", "slice", "join", "describeExpected", "describeFound", "SyntaxError", "parse", "input", "options", "peg$result", "peg$FAILED", "peg$startRuleFunctions", "start", "peg$parsestart", "peg$startRuleFunction", "peg$c3", "peg$literalExpectation", "peg$c4", "peg$c5", "peg$classExpectation", "peg$c8", "peg$c11", "peg$c14", "peg$c18", "peg$c19", "ss", "concat", "map", "peg$c23", "peg$c26", "peg$c29", "peg$c32", "peg$c34", "peg$c36", "peg$c37", "peg$c39", "peg$c40", "a", "peg$c41", "peg$c42", "peg$c44", "peg$c46", "op", "value", "operator", "peg$c49", "peg$c50", "peg$c51", "peg$c53", "peg$c54", "peg$c55", "b", "peg$c56", "d", "match", "peg$c58", "peg$c59", "peg$c60", "peg$c61", "peg$c62", "peg$c66", "peg$c67", "peg$c68", "peg$c70", "peg$c72", "peg$c73", "peg$c75", "peg$c76", "peg$c77", "peg$c81", "peg$c84", "peg$c87", "peg$c90", "peg$c93", "peg$c96", "peg$c99", "peg$c102", "peg$currPos", "peg$posDetailsCache", "line", "column", "peg$maxFailPos", "peg$maxFailExpected", "peg$silentFails", "startRule", "ignoreCase", "peg$computePosDetails", "pos", "p", "details", "peg$computeLocation", "startPos", "endPos", "startPosDetails", "endPosDetails", "offset", "peg$fail", "s0", "s1", "s2", "cached", "peg$resultsCache", "nextPos", "peg$parse_", "peg$parseselectors", "selectors", "peg$c1", "peg$parseidentifierName", "test", "char<PERSON>t", "peg$parsebinaryOp", "s3", "s4", "s5", "s6", "s7", "peg$parseselector", "peg$parsehasSelector", "left", "right", "peg$parsesequence", "reduce", "memo", "rhs", "subject", "as", "peg$parseatom", "peg$parsewildcard", "peg$parseidentifier", "peg$parseattrName", "peg$parseattrEqOps", "substr", "peg$parsetype", "flgs", "peg$parseflags", "RegExp", "peg$parseregex", "peg$parseattrOps", "peg$parsestring", "leadingDecimals", "apply", "parseFloat", "peg$parsenumber", "peg$parsepath", "peg$parseattrValue", "peg$parseattr", "peg$parsefield", "peg$parsenegation", "peg$parsematches", "peg$parsehasSelectors", "peg$parsehas", "nth", "peg$parsefirstChild", "nthLast", "peg$parselastChild", "parseInt", "peg$parsenthChild", "peg$parsenthLastChild", "peg$parseclass", "n", "index", "factory", "<PERSON><PERSON><PERSON>", "MATCHER_CACHE", "WeakMap", "getMatcher", "selector", "matcher", "get", "generateMatcher", "set", "toLowerCase", "ancestry", "nodeTypeKey", "split", "inPath", "ancestor", "fromPathIndex", "field", "k", "matchers", "estraverse", "unshift", "shift", "visitorKeys", "l", "sibling", "adjacent", "nthChild", "matchClass", "getVisitorKeys", "filter", "_typeof", "side", "listProp", "startIndex", "indexOf", "lowerBound", "idx", "ast", "altSubjects", "subjects", "results", "sel", "m", "succeedingAncestry", "parser", "query", "matches"], "mappings": "u0DA2BC,SAASA,EAAMC,GAGZ,IAAIC,EACAC,EACAC,EACAC,EACAC,EACAC,EAEJ,SAASC,EAASC,GACd,IAAcC,EAAKC,EAAfC,EAAM,GACV,IAAKF,KAAOD,EACJA,EAAII,eAAeH,KACnBC,EAAMF,EAAIC,GAENE,EAAIF,GADW,iBAARC,GAA4B,OAARA,EAChBH,EAASG,GAETA,GAIvB,OAAOC,EAgMX,SAASE,EAAUC,EAAQL,GACvBM,KAAKD,OAASA,EACdC,KAAKN,IAAMA,EAiBf,SAASO,EAAQC,EAAMC,EAAMC,EAAMC,GAC/BL,KAAKE,KAAOA,EACZF,KAAKG,KAAOA,EACZH,KAAKI,KAAOA,EACZJ,KAAKK,IAAMA,EAGf,SAASC,KAuHT,SAASC,EAAOL,GACZ,OAAY,MAARA,IAGmB,iBAATA,GAA0C,iBAAdA,EAAKM,MAGnD,SAASC,EAAWC,EAAUhB,GAC1B,OAAQgB,IAAaxB,EAAOyB,kBAAoBD,IAAaxB,EAAO0B,gBAAkB,eAAiBlB,EAG3G,SAASmB,EAA2BC,EAAWC,GAC3C,IAAK,IAAIC,EAAIF,EAAUG,OAAS,EAAGD,GAAK,IAAKA,EACzC,GAAIF,EAAUE,GAAGd,OAASa,EACtB,OAAO,EAGf,OAAO,EAwQX,SAASG,EAASC,EAAMC,GAEpB,OADiB,IAAId,GACHY,SAASC,EAAMC,GAQrC,SAASC,EAAmBC,EAASC,GACjC,IAAIC,EAiBJ,OAfAA,EAjnBJ,SAAoBC,EAAOC,GACvB,IAAIC,EAAMC,EAAKZ,EAAGa,EAKlB,IAHAD,EAAMH,EAAMR,OACZD,EAAI,EAEGY,GAGCF,EAAKD,EADTI,EAAUb,GADVW,EAAOC,IAAQ,KAGXA,EAAMD,GAENX,EAAIa,EAAU,EACdD,GAAOD,EAAO,GAGtB,OAAOX,EAimBEc,CAAWP,GAAQ,SAAgBQ,GACxC,OAAOA,EAAMC,MAAM,GAAKV,EAAQU,MAAM,MAG1CV,EAAQW,cAAgB,CAACX,EAAQU,MAAM,GAAIV,EAAQU,MAAM,IAErDR,IAAWD,EAAON,SAClBK,EAAQW,cAAc,GAAKV,EAAOC,GAAQQ,MAAM,KAGpDR,GAAU,IACI,IACVF,EAAQW,cAAc,GAAKV,EAAOC,GAAQQ,MAAM,IAG7CV,EA2GX,OAxtBApC,EAAS,CACLgD,qBAAsB,uBACtBC,kBAAmB,oBACnBC,gBAAiB,kBACjBC,aAAc,eACdC,wBAAyB,0BACzBC,gBAAiB,kBACjBC,eAAgB,iBAChBC,iBAAkB,mBAClBC,eAAgB,iBAChBC,eAAgB,iBAChBC,YAAa,cACbC,gBAAiB,kBACjBC,UAAW,YACXC,iBAAkB,mBAClBC,gBAAiB,kBACjBC,mBAAoB,qBACpBC,wBAAyB,0BACzBC,sBAAuB,wBACvBC,kBAAmB,oBACnBC,kBAAmB,oBACnBC,mBAAoB,qBACpBC,iBAAkB,mBAClBC,eAAgB,iBAChBC,qBAAsB,uBACtBC,yBAA0B,2BAC1BC,uBAAwB,yBACxBC,gBAAiB,kBACjBC,oBAAqB,sBACrBC,aAAc,eACdC,eAAgB,iBAChBC,eAAgB,iBAChBC,oBAAqB,sBACrBC,mBAAoB,qBACpBC,oBAAqB,sBACrBC,WAAY,aACZC,YAAa,cACbC,iBAAkB,mBAClBC,kBAAmB,oBACnBC,uBAAwB,yBACxBC,yBAA0B,2BAC1BC,gBAAiB,kBACjBC,QAAS,UACTC,iBAAkB,mBAClBC,kBAAmB,oBACnBC,iBAAkB,mBAClBC,aAAc,eACdC,iBAAkB,mBAClBC,gBAAiB,kBACjBC,cAAe,gBACfvE,iBAAkB,mBAClBC,cAAe,gBACfuE,kBAAmB,oBACnBC,QAAS,UACTC,SAAU,WACVC,mBAAoB,qBACpBC,YAAa,cACbC,gBAAiB,kBACjBC,mBAAoB,qBACpBC,cAAe,gBACfC,MAAO,QACPC,gBAAiB,kBACjBC,WAAY,aACZC,yBAA0B,2BAC1BC,gBAAiB,kBACjBC,gBAAiB,kBACjBC,eAAgB,iBAChBC,eAAgB,iBAChBC,aAAc,eACdC,gBAAiB,kBACjBC,iBAAkB,mBAClBC,oBAAqB,sBACrBC,mBAAoB,qBACpBC,eAAgB,iBAChBC,cAAe,gBACfC,gBAAiB,mBAGrBtH,EAAc,CACV8C,qBAAsB,CAAC,OAAQ,SAC/BC,kBAAmB,CAAC,OAAQ,SAC5BC,gBAAiB,CAAC,YAClBC,aAAc,CAAC,YACfC,wBAAyB,CAAC,SAAU,QACpCC,gBAAiB,CAAC,YAClBC,eAAgB,CAAC,QACjBC,iBAAkB,CAAC,OAAQ,SAC3BC,eAAgB,CAAC,SACjBC,eAAgB,CAAC,SAAU,aAC3BC,YAAa,CAAC,QAAS,QACvBC,gBAAiB,CAAC,cAClBC,UAAW,CAAC,QACZC,iBAAkB,CAAC,KAAM,aAAc,QACvCC,gBAAiB,CAAC,KAAM,aAAc,QACtCC,mBAAoB,CAAC,OAAQ,SAC7BC,wBAAyB,CAAC,SAAU,SAAU,QAC9CC,sBAAuB,CAAC,OAAQ,aAAc,aAC9CC,kBAAmB,CAAC,SACpBC,kBAAmB,GACnBC,mBAAoB,GACpBC,iBAAkB,CAAC,OAAQ,QAC3BC,eAAgB,GAChBC,qBAAsB,CAAC,UACvBC,yBAA0B,CAAC,eAC3BC,uBAAwB,CAAC,cAAe,aAAc,UACtDC,gBAAiB,CAAC,WAAY,SAC9BC,oBAAqB,CAAC,cACtBC,aAAc,CAAC,OAAQ,OAAQ,SAAU,QACzCC,eAAgB,CAAC,OAAQ,QAAS,QAClCC,eAAgB,CAAC,OAAQ,QAAS,QAClCC,oBAAqB,CAAC,KAAM,SAAU,QACtCC,mBAAoB,CAAC,KAAM,SAAU,QACrCC,oBAAqB,CAAC,SAAU,SAAU,QAC1CC,WAAY,GACZC,YAAa,CAAC,OAAQ,aAAc,aACpCC,iBAAkB,CAAC,UACnBC,kBAAmB,CAAC,aAAc,UAClCC,uBAAwB,CAAC,SACzBC,yBAA0B,CAAC,SAC3BC,gBAAiB,CAAC,WAAY,SAC9BC,QAAS,GACTC,iBAAkB,CAAC,QAAS,QAC5BC,kBAAmB,CAAC,OAAQ,SAC5BC,iBAAkB,CAAC,SAAU,YAC7BC,aAAc,CAAC,OAAQ,YACvBC,iBAAkB,CAAC,MAAO,SAC1BC,gBAAiB,GACjBC,cAAe,CAAC,SAAU,aAC1BvE,iBAAkB,CAAC,cACnBC,cAAe,CAAC,cAChBuE,kBAAmB,GACnBC,QAAS,CAAC,QACVC,SAAU,CAAC,MAAO,SAClBC,mBAAoB,CAAC,MAAO,SAC5BC,YAAa,CAAE,YACfC,gBAAiB,CAAC,YAClBC,mBAAoB,CAAC,eACrBC,cAAe,CAAC,YAChBC,MAAO,GACPC,gBAAiB,CAAC,eAAgB,SAClCC,WAAY,CAAC,OAAQ,cACrBC,yBAA0B,CAAC,MAAO,SAClCC,gBAAiB,GACjBC,gBAAiB,CAAC,SAAU,eAC5BC,eAAgB,GAChBC,eAAgB,CAAC,YACjBC,aAAc,CAAC,QAAS,UAAW,aACnCC,gBAAiB,CAAC,YAClBC,iBAAkB,CAAC,YACnBC,oBAAqB,CAAC,gBACtBC,mBAAoB,CAAC,KAAM,QAC3BC,eAAgB,CAAC,OAAQ,QACzBC,cAAe,CAAC,SAAU,QAC1BC,gBAAiB,CAAC,aAQtBvH,EAAgB,CACZwH,MALJtH,EAAQ,GAMJuH,KALJtH,EAAO,GAMHuH,OALJtH,EAAS,IAaTO,EAAUgH,UAAUC,QAAU,SAAiB7G,GAC3CF,KAAKD,OAAOC,KAAKN,KAAOQ,GAG5BJ,EAAUgH,UAAUE,OAAS,WACzB,OAAIC,MAAMC,QAAQlH,KAAKD,SACnBC,KAAKD,OAAOoH,OAAOnH,KAAKN,IAAK,IACtB,IAEPM,KAAK+G,QAAQ,OACN,IAefzG,EAAWwG,UAAU3G,KAAO,WACxB,IAAIa,EAAGoG,EAAIC,EAAGC,EAAIC,EAElB,SAASC,EAAUD,EAAQpH,GACvB,GAAI8G,MAAMC,QAAQ/G,GACd,IAAKkH,EAAI,EAAGC,EAAKnH,EAAKc,OAAQoG,EAAIC,IAAMD,EACpCE,EAAOE,KAAKtH,EAAKkH,SAGrBE,EAAOE,KAAKtH,GAKpB,IAAKH,KAAK0H,UAAUvH,KAChB,OAAO,KAKX,IADAoH,EAAS,GACJvG,EAAI,EAAGoG,EAAKpH,KAAK2H,YAAY1G,OAAQD,EAAIoG,IAAMpG,EAEhDwG,EAAUD,EADAvH,KAAK2H,YAAY3G,GACDb,MAG9B,OADAqH,EAAUD,EAAQvH,KAAK0H,UAAUvH,MAC1BoH,GAKXjH,EAAWwG,UAAUtG,KAAO,WAExB,OADWR,KAAK6B,UACJrB,MAAQR,KAAK0H,UAAUtH,MAKvCE,EAAWwG,UAAUc,QAAU,WAC3B,IAAI5G,EAAGoG,EAAIG,EAIX,IADAA,EAAS,GACJvG,EAAI,EAAGoG,EAAKpH,KAAK2H,YAAY1G,OAAQD,EAAIoG,IAAMpG,EAChDuG,EAAOE,KAAKzH,KAAK2H,YAAY3G,GAAGd,MAGpC,OAAOqH,GAKXjH,EAAWwG,UAAUjF,QAAU,WAC3B,OAAO7B,KAAK0H,UAAUxH,MAG1BI,EAAWwG,UAAUe,UAAY,SAAmBC,EAAUC,GAC1D,IAAIC,EAAUT,EAYd,OAVAA,OAASU,EAETD,EAAYhI,KAAK0H,UACjB1H,KAAK0H,UAAYK,EACjB/H,KAAKkI,QAAU,KACXJ,IACAP,EAASO,EAASK,KAAKnI,KAAM+H,EAAQ7H,KAAMF,KAAK2H,YAAY3H,KAAK2H,YAAY1G,OAAS,GAAGf,OAE7FF,KAAK0H,UAAYM,EAEVT,GAKXjH,EAAWwG,UAAUsB,OAAS,SAAgBC,GAC1CrI,KAAKkI,QAAUG,GAKnB/H,EAAWwG,UAAUwB,KAAO,WACxBtI,KAAKoI,OAAO9I,IAKhBgB,EAAWwG,UAAiB,MAAI,WAC5B9G,KAAKoI,OAAO/I,IAKhBiB,EAAWwG,UAAUE,OAAS,WAC1BhH,KAAKoI,OAAO7I,IAGhBe,EAAWwG,UAAUyB,aAAe,SAASpH,EAAMC,GAC/CpB,KAAKoB,QAAUA,EACfpB,KAAKmB,KAAOA,EACZnB,KAAKwI,WAAa,GAClBxI,KAAK2H,YAAc,GACnB3H,KAAK0H,UAAY,KACjB1H,KAAKkI,QAAU,KACflI,KAAKyI,WAAa,KACO,cAArBrH,EAAQsH,SACR1I,KAAKyI,WAAaE,OAAOC,KACU,mBAArBxH,EAAQsH,WACtB1I,KAAKyI,WAAarH,EAAQsH,UAG9B1I,KAAK6I,OAASzJ,EACVgC,EAAQwH,OACR5I,KAAK6I,OAASF,OAAOG,OAAOH,OAAOI,OAAO/I,KAAK6I,QAASzH,EAAQwH,QAwBxEtI,EAAWwG,UAAU5F,SAAW,SAAkBC,EAAMC,GACpD,IAAI4H,EACAlI,EACAiH,EACA7H,EACAQ,EACAd,EACAF,EACAmC,EACAoH,EACAC,EACAnI,EACAoI,EAcJ,IAZAnJ,KAAKuI,aAAapH,EAAMC,GAExB+H,EAAW,GAGXH,EAAWhJ,KAAKwI,WAChB1H,EAAYd,KAAK2H,YAGjBqB,EAASvB,KAAK,IAAIxH,EAAQkB,EAAM,KAAM,KAAM,OAC5CL,EAAU2G,KAAK,IAAIxH,EAAQ,KAAM,KAAM,KAAM,OAEtC+I,EAAS/H,QAGZ,IAFA8G,EAAUiB,EAASI,SAEHD,GAWhB,GAAIpB,EAAQ7H,KAAM,CAId,GAFAN,EAAMI,KAAK6H,UAAUzG,EAAQiI,MAAOtB,GAEhC/H,KAAKkI,UAAY7I,GAASO,IAAQP,EAClC,OAMJ,GAHA2J,EAASvB,KAAK0B,GACdrI,EAAU2G,KAAKM,GAEX/H,KAAKkI,UAAY5I,GAAQM,IAAQN,EACjC,SAMJ,GAFAoB,GADAR,EAAO6H,EAAQ7H,MACCM,MAAQuH,EAAQ3H,OAChC8I,EAAalJ,KAAK6I,OAAOnI,IACR,CACb,IAAIV,KAAKyI,WAGL,MAAM,IAAIa,MAAM,qBAAuB5I,EAAW,KAFlDwI,EAAalJ,KAAKyI,WAAWvI,GAOrC,IADA2B,EAAUqH,EAAWjI,QACbY,GAAW,IAAM,GAGrB,GADAd,EAAYb,EADZR,EAAMwJ,EAAWrH,IAMjB,GAAIoF,MAAMC,QAAQnG,IAEd,IADAkI,EAAWlI,EAAUE,QACbgI,GAAY,IAAM,GACtB,GAAKlI,EAAUkI,KAIXpI,EAA2BC,EAAWC,EAAUkI,IAApD,CAIA,GAAIxI,EAAWC,EAAUwI,EAAWrH,IAChCkG,EAAU,IAAI9H,EAAQc,EAAUkI,GAAW,CAACvJ,EAAKuJ,GAAW,WAAY,UACrE,CAAA,IAAI1I,EAAOQ,EAAUkI,IAGxB,SAFAlB,EAAU,IAAI9H,EAAQc,EAAUkI,GAAW,CAACvJ,EAAKuJ,GAAW,KAAM,MAItED,EAASvB,KAAKM,SAEf,GAAIxH,EAAOQ,GAAY,CAC1B,GAAIF,EAA2BC,EAAWC,GACxC,SAGFiI,EAASvB,KAAK,IAAIxH,EAAQc,EAAWrB,EAAK,KAAM,cAjExD,GAJAqI,EAAUjH,EAAUsI,MAEpBxJ,EAAMI,KAAK6H,UAAUzG,EAAQmI,MAAOxB,GAEhC/H,KAAKkI,UAAY7I,GAASO,IAAQP,EAClC,QAuEhBiB,EAAWwG,UAAUC,QAAU,SAAiB5F,EAAMC,GAClD,IAAI4H,EACAlI,EACAZ,EACAQ,EACAc,EACAuG,EACAlG,EACAoH,EACAC,EACAnI,EACAoI,EACAK,EACA9J,EAEJ,SAAS+J,EAAW1B,GAChB,IAAI/G,EACAtB,EACAgK,EACA3J,EAEJ,GAAIgI,EAAQ1H,IAAI2G,SAOZ,IALAtH,EAAMqI,EAAQ1H,IAAIX,IAClBK,EAASgI,EAAQ1H,IAAIN,OAGrBiB,EAAIgI,EAAS/H,OACND,KAEH,IADA0I,EAAWV,EAAShI,IACPX,KAAOqJ,EAASrJ,IAAIN,SAAWA,EAAQ,CAChD,GAAK2J,EAASrJ,IAAIX,IAAMA,EACpB,QAEFgK,EAASrJ,IAAIX,KAsB/B,IAhBAM,KAAKuI,aAAapH,EAAMC,GAExB+H,EAAW,GAGXH,EAAWhJ,KAAKwI,WAChB1H,EAAYd,KAAK2H,YAMjBI,EAAU,IAAI9H,EAAQkB,EAAM,KAAM,KAAM,IAAIrB,EAH5C0J,EAAQ,CACJrI,KAAMA,GAEmD,SAC7D6H,EAASvB,KAAKM,GACdjH,EAAU2G,KAAKM,GAERiB,EAAS/H,QAGZ,IAFA8G,EAAUiB,EAASI,SAEHD,EAAhB,CAqCA,QAXelB,KAJfzG,EAASxB,KAAK6H,UAAUzG,EAAQiI,MAAOtB,KAIXvG,IAAWnC,GAASmC,IAAWlC,GAAQkC,IAAWjC,IAE1EwI,EAAQ1H,IAAI0G,QAAQvF,GACpBuG,EAAQ7H,KAAOsB,GAGfxB,KAAKkI,UAAY3I,GAAUiC,IAAWjC,IACtCkK,EAAW1B,GACXA,EAAQ7H,KAAO,MAGfF,KAAKkI,UAAY7I,GAASmC,IAAWnC,EACrC,OAAOmK,EAAMrI,KAKjB,IADAjB,EAAO6H,EAAQ7H,QAKf8I,EAASvB,KAAK0B,GACdrI,EAAU2G,KAAKM,GAEX/H,KAAKkI,UAAY5I,GAAQkC,IAAWlC,GAAxC,CAMA,GAFAoB,EAAWR,EAAKM,MAAQuH,EAAQ3H,OAChC8I,EAAalJ,KAAK6I,OAAOnI,IACR,CACb,IAAIV,KAAKyI,WAGL,MAAM,IAAIa,MAAM,qBAAuB5I,EAAW,KAFlDwI,EAAalJ,KAAKyI,WAAWvI,GAOrC,IADA2B,EAAUqH,EAAWjI,QACbY,GAAW,IAAM,GAGrB,GADAd,EAAYb,EADZR,EAAMwJ,EAAWrH,IAMjB,GAAIoF,MAAMC,QAAQnG,IAEd,IADAkI,EAAWlI,EAAUE,QACbgI,GAAY,IAAM,GACtB,GAAKlI,EAAUkI,GAAf,CAGA,GAAIxI,EAAWC,EAAUwI,EAAWrH,IAChCkG,EAAU,IAAI9H,EAAQc,EAAUkI,GAAW,CAACvJ,EAAKuJ,GAAW,WAAY,IAAInJ,EAAUiB,EAAWkI,QAC9F,CAAA,IAAI1I,EAAOQ,EAAUkI,IAGxB,SAFAlB,EAAU,IAAI9H,EAAQc,EAAUkI,GAAW,CAACvJ,EAAKuJ,GAAW,KAAM,IAAInJ,EAAUiB,EAAWkI,IAI/FD,EAASvB,KAAKM,SAEXxH,EAAOQ,IACdiI,EAASvB,KAAK,IAAIxH,EAAQc,EAAWrB,EAAK,KAAM,IAAII,EAAUI,EAAMR,WAxExE,GAfAqI,EAAUjH,EAAUsI,WAMLnB,KAJfzG,EAASxB,KAAK6H,UAAUzG,EAAQmI,MAAOxB,KAIXvG,IAAWnC,GAASmC,IAAWlC,GAAQkC,IAAWjC,GAE1EwI,EAAQ1H,IAAI0G,QAAQvF,GAGpBxB,KAAKkI,UAAY3I,GAAUiC,IAAWjC,GACtCkK,EAAW1B,GAGX/H,KAAKkI,UAAY7I,GAASmC,IAAWnC,EACrC,OAAOmK,EAAMrI,KA4EzB,OAAOqI,EAAMrI,MAiIjBlC,EAAQC,OAASA,EACjBD,EAAQiC,SAAWA,EACnBjC,EAAQ8H,QA3HR,SAAiB5F,EAAMC,GAEnB,OADiB,IAAId,GACHyG,QAAQ5F,EAAMC,IA0HpCnC,EAAQ0K,eAlGR,SAAwBC,EAAMC,EAAkBtI,GAE5C,IAAmBD,EAASM,EAAKZ,EAAG8I,EAAhCC,EAAW,GAEf,IAAKH,EAAK5H,MACN,MAAM,IAAIsH,MAAM,0CAIpB,IAAK/H,EAAON,OAAQ,CAChB,GAAI4I,EAAiB5I,OAAQ,CACzB,IAAKD,EAAI,EAAGY,EAAMiI,EAAiB5I,OAAQD,EAAIY,EAAKZ,GAAK,GACrDM,EAAU9B,EAASqK,EAAiB7I,KAC5BiB,cAAgB,CAAC,EAAG2H,EAAK5H,MAAM,IACvC+H,EAAStC,KAAKnG,GAElBsI,EAAKI,gBAAkBD,EAE3B,OAAOH,EAGX,IAAK5I,EAAI,EAAGY,EAAMiI,EAAiB5I,OAAQD,EAAIY,EAAKZ,GAAK,EACrD+I,EAAStC,KAAKpG,EAAmB7B,EAASqK,EAAiB7I,IAAKO,IAsEpE,OAlEAuI,EAAS,EACT5I,EAAS0I,EAAM,CACXP,MAAO,SAAUnJ,GAGb,IAFA,IAAIoB,EAEGwI,EAASC,EAAS9I,WACrBK,EAAUyI,EAASD,IACP7H,cAAc,GAAK/B,EAAK8B,MAAM,KAItCV,EAAQW,cAAc,KAAO/B,EAAK8B,MAAM,IACnC9B,EAAK8J,kBACN9J,EAAK8J,gBAAkB,IAE3B9J,EAAK8J,gBAAgBvC,KAAKnG,GAC1ByI,EAAS5C,OAAO2C,EAAQ,IAExBA,GAAU,EAKlB,OAAIA,IAAWC,EAAS9I,OACb9B,EAAcwH,MAGrBoD,EAASD,GAAQ7H,cAAc,GAAK/B,EAAK8B,MAAM,GACxC7C,EAAcyH,UADzB,KAMRkD,EAAS,EACT5I,EAAS0I,EAAM,CACXL,MAAO,SAAUrJ,GAGb,IAFA,IAAIoB,EAEGwI,EAASC,EAAS9I,SACrBK,EAAUyI,EAASD,KACf5J,EAAK8B,MAAM,GAAKV,EAAQW,cAAc,MAItC/B,EAAK8B,MAAM,KAAOV,EAAQW,cAAc,IACnC/B,EAAK+J,mBACN/J,EAAK+J,iBAAmB,IAE5B/J,EAAK+J,iBAAiBxC,KAAKnG,GAC3ByI,EAAS5C,OAAO2C,EAAQ,IAExBA,GAAU,EAKlB,OAAIA,IAAWC,EAAS9I,OACb9B,EAAcwH,MAGrBoD,EAASD,GAAQ7H,cAAc,GAAK/B,EAAK8B,MAAM,GACxC7C,EAAcyH,UADzB,KAMDgD,GAOX3K,EAAQG,YAAcA,EACtBH,EAAQE,cAAgBA,EACxBF,EAAQqB,WAAaA,EACrBrB,EAAQiL,iBAAmB,WAAc,OAAOlL,EAAM,KAE/CC,EAvwBV,CAwwBCA,uBC3xByCkL,EAAOlL,UAC9CkL,UAEK,WASP,SAASC,EAAgBC,EAASC,EAAUC,EAAOC,GACjDxK,KAAKqK,QAAWA,EAChBrK,KAAKsK,SAAWA,EAChBtK,KAAKuK,MAAWA,EAChBvK,KAAKwK,SAAWA,EAChBxK,KAAKyK,KAAW,cAEuB,mBAA5BnB,MAAMoB,mBACfpB,MAAMoB,kBAAkB1K,KAAMoK,GAqmFlC,OAnnFA,SAAsBO,EAAO5K,GAC3B,SAAS6K,IAAS5K,KAAK6K,YAAcF,EACrCC,EAAK9D,UAAY/G,EAAO+G,UACxB6D,EAAM7D,UAAY,IAAI8D,EAexBE,CAAaV,EAAiBd,OAE9Bc,EAAgBW,aAAe,SAAST,EAAUC,GAChD,IAAIS,EAA2B,CACzBC,QAAS,SAASC,GAChB,MAAO,IAAOC,EAAcD,EAAYE,MAAQ,KAGlDC,MAAS,SAASH,GAChB,IACIlK,EADAsK,EAAe,GAGnB,IAAKtK,EAAI,EAAGA,EAAIkK,EAAYK,MAAMtK,OAAQD,IACxCsK,GAAgBJ,EAAYK,MAAMvK,aAAciG,MAC5CuE,EAAYN,EAAYK,MAAMvK,GAAG,IAAM,IAAMwK,EAAYN,EAAYK,MAAMvK,GAAG,IAC9EwK,EAAYN,EAAYK,MAAMvK,IAGpC,MAAO,KAAOkK,EAAYO,SAAW,IAAM,IAAMH,EAAe,KAGlEI,IAAK,SAASR,GACZ,MAAO,iBAGTS,IAAK,SAAST,GACZ,MAAO,gBAGTU,MAAO,SAASV,GACd,OAAOA,EAAYW,cAI3B,SAASC,EAAIC,GACX,OAAOA,EAAGC,WAAW,GAAGC,SAAS,IAAIC,cAGvC,SAASf,EAAcgB,GACrB,OAAOA,EACJpF,QAAQ,MAAO,QACfA,QAAQ,KAAO,OACfA,QAAQ,MAAO,OACfA,QAAQ,MAAO,OACfA,QAAQ,MAAO,OACfA,QAAQ,MAAO,OACfA,QAAQ,gBAAyB,SAASgF,GAAM,MAAO,OAASD,EAAIC,MACpEhF,QAAQ,yBAAyB,SAASgF,GAAM,MAAO,MAASD,EAAIC,MAGzE,SAASP,EAAYW,GACnB,OAAOA,EACJpF,QAAQ,MAAO,QACfA,QAAQ,MAAO,OACfA,QAAQ,MAAO,OACfA,QAAQ,KAAO,OACfA,QAAQ,MAAO,OACfA,QAAQ,MAAO,OACfA,QAAQ,MAAO,OACfA,QAAQ,MAAO,OACfA,QAAQ,gBAAyB,SAASgF,GAAM,MAAO,OAASD,EAAIC,MACpEhF,QAAQ,yBAAyB,SAASgF,GAAM,MAAO,MAASD,EAAIC,MA6CzE,MAAO,YAtCP,SAA0BzB,GACxB,IACItJ,EAAGqG,EANoB6D,EAKvBkB,EAAe,IAAInF,MAAMqD,EAASrJ,QAGtC,IAAKD,EAAI,EAAGA,EAAIsJ,EAASrJ,OAAQD,IAC/BoL,EAAapL,IATYkK,EASaZ,EAAStJ,GAR1CgK,EAAyBE,EAAY1K,MAAM0K,IAalD,GAFAkB,EAAaC,OAETD,EAAanL,OAAS,EAAG,CAC3B,IAAKD,EAAI,EAAGqG,EAAI,EAAGrG,EAAIoL,EAAanL,OAAQD,IACtCoL,EAAapL,EAAI,KAAOoL,EAAapL,KACvCoL,EAAa/E,GAAK+E,EAAapL,GAC/BqG,KAGJ+E,EAAanL,OAASoG,EAGxB,OAAQ+E,EAAanL,QACnB,KAAK,EACH,OAAOmL,EAAa,GAEtB,KAAK,EACH,OAAOA,EAAa,GAAK,OAASA,EAAa,GAEjD,QACE,OAAOA,EAAaE,MAAM,GAAI,GAAGC,KAAK,MAClC,QACAH,EAAaA,EAAanL,OAAS,IAQxBuL,CAAiBlC,GAAY,QAJlD,SAAuBC,GACrB,OAAOA,EAAQ,IAAOY,EAAcZ,GAAS,IAAO,eAGMkC,CAAclC,GAAS,WAu/E9E,CACLmC,YAAatC,EACbuC,MAt/EF,SAAmBC,EAAOC,GACxBA,OAAsB,IAAZA,EAAqBA,EAAU,GAEzC,IAsJIC,EAwH8BxC,EAAUC,EAAOC,EA9Q/CuC,EAAa,GAEbC,EAAyB,CAAEC,MAAOC,IAClCC,EAAyBD,GAOzBE,EAASC,GAAuB,KAAK,GACrCC,EAAS,uBACTC,EAASC,GAAqB,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,MAAM,GAAM,GAGjHC,EAASJ,GAAuB,KAAK,GAGrCK,EAAUL,GAAuB,KAAK,GAGtCM,EAAUN,GAAuB,KAAK,GAItCO,EAAUP,GAAuB,KAAK,GACtCQ,EAAU,SAAS1B,EAAG2B,GACpB,MAAO,CAAC3B,GAAG4B,OAAOD,EAAGE,KAAI,SAAU7B,GAAK,OAAOA,EAAE,QAYnD8B,EAAUZ,GAAuB,KAAK,GAOtCa,EAAUb,GAAuB,KAAK,GAGtCc,EAAUd,GAAuB,KAAK,GAGtCe,EAAUf,GAAuB,KAAK,GAEtCgB,EAAUhB,GAAuB,KAAK,GAEtCiB,EAAU,SACVC,EAAUf,GAAqB,CAAC,IAAK,IAAK,MAAM,GAAO,GAEvDgB,EAAUnB,GAAuB,KAAK,GACtCoB,EAAU,SAASC,GAAK,OAAQA,GAAK,IAAM,KAC3CC,EAAU,QACVC,EAAUpB,GAAqB,CAAC,IAAK,MAAM,GAAO,GAElDqB,EAAUxB,GAAuB,KAAK,GAItCyB,EAAU,SAASrE,EAAMsE,EAAIC,GACvB,MAAO,CAAExO,KAAM,YAAaiK,KAAMA,EAAMwE,SAAUF,EAAIC,MAAOA,IAInEE,EAAU7B,GAAuB,KAAM,GACvC8B,EAAU,UACVC,EAAU5B,GAAqB,CAAC,KAAM,MAAO,GAAM,GAEnD6B,EAAUhC,GAAuB,MAAM,GACvCiC,EAmHK,CAAE9O,KAAM,OAlHb+O,EAAU,SAASb,EAAGc,GAAK,OAAOd,EAAIc,GACtCC,EAAU,SAASC,GACX,MAAO,CAAElP,KAAM,UAAWwO,OA83Ef7C,EA93EkCuD,EAAEnD,KAAK,IA+3ErDJ,EAAEpF,QAAQ,UAAU,SAAS4I,EAAO5D,GACzC,OAAOA,GACL,IAAK,IAAK,MAAO,KACjB,IAAK,IAAK,MAAO,KACjB,IAAK,IAAK,MAAO,KACjB,IAAK,IAAK,MAAO,KACjB,IAAK,IAAK,MAAO,KACjB,IAAK,IAAK,MAAO,KACjB,QAAS,OAAOA,QATtB,IAAqBI,GA33EnByD,EAAUvC,GAAuB,KAAK,GACtCwC,EAAU,UACVC,EAAUtC,GAAqB,CAAC,KAAM,MAAM,GAAM,GAClDuC,EAAU,SACVC,EAAUxC,GAAqB,CAAC,CAAC,IAAK,OAAO,GAAO,GAQpDyC,EAAU5C,GAAuB,SAAS,GAC1C6C,EAAU,SACVC,EAAU3C,GAAqB,CAAC,IAAK,MAAM,GAAM,GAEjD4C,EAAU/C,GAAuB,KAAK,GAEtCgD,EAAU,UACVC,EAAU9C,GAAqB,CAAC,IAAK,IAAK,IAAK,MAAM,GAAO,GAE5D+C,EAAUlD,GAAuB,KAAK,GACtCmD,EAAU,SACVC,EAAUjD,GAAqB,CAAC,MAAM,GAAM,GAQ5CkD,EAAUrD,GAAuB,SAAS,GAG1CsD,EAAUtD,GAAuB,aAAa,GAG9CuD,GAAUvD,GAAuB,SAAS,GAG1CwD,GAAUxD,GAAuB,gBAAgB,GAGjDyD,GAAUzD,GAAuB,eAAe,GAGhD0D,GAAU1D,GAAuB,eAAe,GAGhD2D,GAAU3D,GAAuB,oBAAoB,GAGrD4D,GAAW5D,GAAuB,KAAK,GAKvC6D,GAAuB,EAEvBC,GAAuB,CAAC,CAAEC,KAAM,EAAGC,OAAQ,IAC3CC,GAAuB,EACvBC,GAAuB,GACvBC,GAEmB,GAIvB,GAAI,cAAe3E,EAAS,CAC1B,KAAMA,EAAQ4E,aAAazE,GACzB,MAAM,IAAI1D,MAAM,mCAAqCuD,EAAQ4E,UAAY,MAG3EtE,EAAwBH,EAAuBH,EAAQ4E,WA2BzD,SAASpE,GAAuBjC,EAAMsG,GACpC,MAAO,CAAElR,KAAM,UAAW4K,KAAMA,EAAMsG,WAAYA,GAGpD,SAASlE,GAAqBjC,EAAOE,EAAUiG,GAC7C,MAAO,CAAElR,KAAM,QAAS+K,MAAOA,EAAOE,SAAUA,EAAUiG,WAAYA,GAexE,SAASC,GAAsBC,GAC7B,IAAwCC,EAApCC,EAAUX,GAAoBS,GAElC,GAAIE,EACF,OAAOA,EAGP,IADAD,EAAID,EAAM,GACFT,GAAoBU,IAC1BA,IASF,IALAC,EAAU,CACRV,MAFFU,EAAUX,GAAoBU,IAEZT,KAChBC,OAAQS,EAAQT,QAGXQ,EAAID,GACmB,KAAxBhF,EAAMZ,WAAW6F,IACnBC,EAAQV,OACRU,EAAQT,OAAS,GAEjBS,EAAQT,SAGVQ,IAIF,OADAV,GAAoBS,GAAOE,EACpBA,EAIX,SAASC,GAAoBC,EAAUC,GACrC,IAAIC,EAAkBP,GAAsBK,GACxCG,EAAkBR,GAAsBM,GAE5C,MAAO,CACLhF,MAAO,CACLmF,OAAQJ,EACRZ,KAAQc,EAAgBd,KACxBC,OAAQa,EAAgBb,QAE1B1F,IAAK,CACHyG,OAAQH,EACRb,KAAQe,EAAcf,KACtBC,OAAQc,EAAcd,SAK5B,SAASgB,GAAS/H,GACZ4G,GAAcI,KAEdJ,GAAcI,KAChBA,GAAiBJ,GACjBK,GAAsB,IAGxBA,GAAoB9J,KAAK6C,IAgB3B,SAAS4C,KACP,IAAIoF,EAAIC,EAAIC,EAnRQ1E,EAqRhBpO,EAAuB,GAAdwR,GAAmB,EAC5BuB,EAASC,GAAiBhT,GAE9B,OAAI+S,GACFvB,GAAcuB,EAAOE,QAEdF,EAAOlL,SAGhB+K,EAAKpB,IACLqB,EAAKK,QACM7F,IACTyF,EAAKK,QACM9F,GACJ6F,OACM7F,EAGTuF,EADAC,EArSqB,KADPzE,EAsSF0E,GArSFvR,OAAe6M,EAAG,GAAK,CAAEtN,KAAM,UAAWsS,UAAWhF,IAgTnEoD,GAAcoB,EACdA,EAAKvF,GAEHuF,IAAOvF,IACTuF,EAAKpB,IACLqB,EAAKK,QACM7F,IAETwF,OAAKQ,GAEPT,EAAKC,GAGPG,GAAiBhT,GAAO,CAAEiT,QAASzB,GAAa3J,OAAQ+K,GAEjDA,GAGT,SAASM,KACP,IAAIN,EAAIC,EAEJ7S,EAAuB,GAAdwR,GAAmB,EAC5BuB,EAASC,GAAiBhT,GAE9B,GAAI+S,EAGF,OAFAvB,GAAcuB,EAAOE,QAEdF,EAAOlL,OAWhB,IARA+K,EAAK,GACiC,KAAlC1F,EAAMZ,WAAWkF,KACnBqB,EA7US,IA8UTrB,OAEAqB,EAAKxF,EACwBsF,GAASjF,IAEjCmF,IAAOxF,GACZuF,EAAG7K,KAAK8K,GAC8B,KAAlC3F,EAAMZ,WAAWkF,KACnBqB,EAtVO,IAuVPrB,OAEAqB,EAAKxF,EACwBsF,GAASjF,IAM1C,OAFAsF,GAAiBhT,GAAO,CAAEiT,QAASzB,GAAa3J,OAAQ+K,GAEjDA,EAGT,SAASU,KACP,IAAIV,EAAIC,EAAIC,EAER9S,EAAuB,GAAdwR,GAAmB,EAC5BuB,EAASC,GAAiBhT,GAE9B,GAAI+S,EAGF,OAFAvB,GAAcuB,EAAOE,QAEdF,EAAOlL,OAYhB,GARAgL,EAAK,GACDjF,EAAO2F,KAAKrG,EAAMsG,OAAOhC,MAC3BsB,EAAK5F,EAAMsG,OAAOhC,IAClBA,OAEAsB,EAAKzF,EACwBsF,GAAS9E,IAEpCiF,IAAOzF,EACT,KAAOyF,IAAOzF,GACZwF,EAAG9K,KAAK+K,GACJlF,EAAO2F,KAAKrG,EAAMsG,OAAOhC,MAC3BsB,EAAK5F,EAAMsG,OAAOhC,IAClBA,OAEAsB,EAAKzF,EACwBsF,GAAS9E,SAI1CgF,EAAKxF,EAUP,OARIwF,IAAOxF,IAETwF,EAAYA,EApYoBhG,KAAK,KAsYvC+F,EAAKC,EAELG,GAAiBhT,GAAO,CAAEiT,QAASzB,GAAa3J,OAAQ+K,GAEjDA,EAGT,SAASa,KACP,IAAIb,EAAIC,EAAIC,EAER9S,EAAuB,GAAdwR,GAAmB,EAC5BuB,EAASC,GAAiBhT,GAE9B,OAAI+S,GACFvB,GAAcuB,EAAOE,QAEdF,EAAOlL,SAGhB+K,EAAKpB,IACLqB,EAAKK,QACM7F,GAC6B,KAAlCH,EAAMZ,WAAWkF,KACnBsB,EA5ZO,IA6ZPtB,OAEAsB,EAAKzF,EACwBsF,GAAS5E,IAEpC+E,IAAOzF,GACJ6F,OACM7F,EAGTuF,EADAC,EApayB,SA2a3BrB,GAAcoB,EACdA,EAAKvF,KAGPmE,GAAcoB,EACdA,EAAKvF,GAEHuF,IAAOvF,IACTuF,EAAKpB,IACLqB,EAAKK,QACM7F,GAC6B,MAAlCH,EAAMZ,WAAWkF,KACnBsB,EAtbM,IAubNtB,OAEAsB,EAAKzF,EACwBsF,GAAS3E,IAEpC8E,IAAOzF,GACJ6F,OACM7F,EAGTuF,EADAC,EA9bwB,WAqc1BrB,GAAcoB,EACdA,EAAKvF,KAGPmE,GAAcoB,EACdA,EAAKvF,GAEHuF,IAAOvF,IACTuF,EAAKpB,IACLqB,EAAKK,QACM7F,GAC6B,KAAlCH,EAAMZ,WAAWkF,KACnBsB,EAhdI,IAidJtB,OAEAsB,EAAKzF,EACwBsF,GAAS1E,IAEpC6E,IAAOzF,GACJ6F,OACM7F,EAGTuF,EADAC,EAxdsB,YA+dxBrB,GAAcoB,EACdA,EAAKvF,KAGPmE,GAAcoB,EACdA,EAAKvF,GAEHuF,IAAOvF,IACTuF,EAAKpB,GACiC,KAAlCtE,EAAMZ,WAAWkF,KACnBqB,EAtfG,IAufHrB,OAEAqB,EAAKxF,EACwBsF,GAASjF,IAEpCmF,IAAOxF,IACTyF,EAAKI,QACM7F,EAGTuF,EADAC,EAlfsB,cAyfxBrB,GAAcoB,EACdA,EAAKvF,MAMb2F,GAAiBhT,GAAO,CAAEiT,QAASzB,GAAa3J,OAAQ+K,GAEjDA,GA0GT,SAASO,KACP,IAAIP,EAAIC,EAAIC,EAAIY,EAAIC,EAAIC,EAAIC,EAAIC,EAE5B9T,EAAuB,GAAdwR,GAAmB,EAC5BuB,EAASC,GAAiBhT,GAE9B,GAAI+S,EAGF,OAFAvB,GAAcuB,EAAOE,QAEdF,EAAOlL,OAKhB,GAFA+K,EAAKpB,IACLqB,EAAKkB,QACM1G,EAAY,CAmCrB,IAlCAyF,EAAK,GACLY,EAAKlC,IACLmC,EAAKT,QACM7F,GAC6B,KAAlCH,EAAMZ,WAAWkF,KACnBoC,EA/nBM,IAgoBNpC,OAEAoC,EAAKvG,EACwBsF,GAASzE,IAEpC0F,IAAOvG,IACTwG,EAAKX,QACM7F,IACTyG,EAAKC,QACM1G,EAETqG,EADAC,EAAK,CAACA,EAAIC,EAAIC,EAAIC,IAWtBtC,GAAckC,EACdA,EAAKrG,KAGPmE,GAAckC,EACdA,EAAKrG,GAEAqG,IAAOrG,GACZyF,EAAG/K,KAAK2L,GACRA,EAAKlC,IACLmC,EAAKT,QACM7F,GAC6B,KAAlCH,EAAMZ,WAAWkF,KACnBoC,EAlqBI,IAmqBJpC,OAEAoC,EAAKvG,EACwBsF,GAASzE,IAEpC0F,IAAOvG,IACTwG,EAAKX,QACM7F,IACTyG,EAAKC,QACM1G,EAETqG,EADAC,EAAK,CAACA,EAAIC,EAAIC,EAAIC,IAWtBtC,GAAckC,EACdA,EAAKrG,KAGPmE,GAAckC,EACdA,EAAKrG,GAGLyF,IAAOzF,EAGTuF,EADAC,EAAK1E,EAAQ0E,EAAIC,IAGjBtB,GAAcoB,EACdA,EAAKvF,QAGPmE,GAAcoB,EACdA,EAAKvF,EAKP,OAFA2F,GAAiBhT,GAAO,CAAEiT,QAASzB,GAAa3J,OAAQ+K,GAEjDA,EAGT,SAASoB,KACP,IAAIpB,EAAIC,EAAIC,EA9sBSzD,EAAI5C,EAgtBrBzM,EAAuB,GAAdwR,GAAmB,EAC5BuB,EAASC,GAAiBhT,GAE9B,OAAI+S,GACFvB,GAAcuB,EAAOE,QAEdF,EAAOlL,SAGhB+K,EAAKpB,IACLqB,EAAKY,QACMpG,IACTwF,EAAK,MAEHA,IAAOxF,IACTyF,EAAKiB,QACM1G,GAhuBYZ,EAkuBJqG,EACjBF,EADAC,GAluBiBxD,EAkuBJwD,GAhuBJ,CAAE/R,KAAMuO,EAAI4E,KAAM,CAAEnT,KAAM,aAAeoT,MAAOzH,GADvCA,IAwuBpB+E,GAAcoB,EACdA,EAAKvF,GAGP2F,GAAiBhT,GAAO,CAAEiT,QAASzB,GAAa3J,OAAQ+K,GAEjDA,GAGT,SAASmB,KACP,IAAInB,EAAIC,EAAIC,EAAIY,EAAIC,EAAIC,EA/uBH5E,EAivBjBhP,EAAuB,GAAdwR,GAAmB,EAC5BuB,EAASC,GAAiBhT,GAE9B,GAAI+S,EAGF,OAFAvB,GAAcuB,EAAOE,QAEdF,EAAOlL,OAKhB,GAFA+K,EAAKpB,IACLqB,EAAKsB,QACM9G,EAAY,CAiBrB,IAhBAyF,EAAK,GACLY,EAAKlC,IACLmC,EAAKF,QACMpG,IACTuG,EAAKO,QACM9G,EAETqG,EADAC,EAAK,CAACA,EAAIC,IAOZpC,GAAckC,EACdA,EAAKrG,GAEAqG,IAAOrG,GACZyF,EAAG/K,KAAK2L,GACRA,EAAKlC,IACLmC,EAAKF,QACMpG,IACTuG,EAAKO,QACM9G,EAETqG,EADAC,EAAK,CAACA,EAAIC,IAOZpC,GAAckC,EACdA,EAAKrG,GAGLyF,IAAOzF,GA/xBQ2B,EAiyBJ6D,EACbD,EADAC,EAAiBC,EAhyBJsB,QAAO,SAAUC,EAAMC,GAChC,MAAO,CAAExT,KAAMwT,EAAI,GAAIL,KAAMI,EAAMH,MAAOI,EAAI,MAC7CtF,KAiyBLwC,GAAcoB,EACdA,EAAKvF,QAGPmE,GAAcoB,EACdA,EAAKvF,EAKP,OAFA2F,GAAiBhT,GAAO,CAAEiT,QAASzB,GAAa3J,OAAQ+K,GAEjDA,EAGT,SAASuB,KACP,IAAIvB,EAAIC,EAAIC,EAAIY,EA3yBKa,EAASC,EAClB1E,EA4yBR9P,EAAuB,GAAdwR,GAAmB,EAC5BuB,EAASC,GAAiBhT,GAE9B,GAAI+S,EAGF,OAFAvB,GAAcuB,EAAOE,QAEdF,EAAOlL,OAchB,GAXA+K,EAAKpB,GACiC,KAAlCtE,EAAMZ,WAAWkF,KACnBqB,EA1zBU,IA2zBVrB,OAEAqB,EAAKxF,EACwBsF,GAASpE,IAEpCsE,IAAOxF,IACTwF,EAAK,MAEHA,IAAOxF,EAAY,CAGrB,GAFAyF,EAAK,IACLY,EAAKe,QACMpH,EACT,KAAOqG,IAAOrG,GACZyF,EAAG/K,KAAK2L,GACRA,EAAKe,UAGP3B,EAAKzF,EAEHyF,IAAOzF,GA50BQkH,EA80BJ1B,EA70BL/C,EAAkB,KADA0E,EA80BT1B,GA70BFvR,OAAeiT,EAAG,GAAK,CAAE1T,KAAM,WAAYsS,UAAWoB,GAChED,IAASzE,EAAEyE,SAAU,GA60B1B3B,EADAC,EA30BS/C,IA80BT0B,GAAcoB,EACdA,EAAKvF,QAGPmE,GAAcoB,EACdA,EAAKvF,EAKP,OAFA2F,GAAiBhT,GAAO,CAAEiT,QAASzB,GAAa3J,OAAQ+K,GAEjDA,EAGT,SAAS6B,KACP,IAAI7B,EAEA5S,EAAuB,GAAdwR,GAAmB,EAC5BuB,EAASC,GAAiBhT,GAE9B,OAAI+S,GACFvB,GAAcuB,EAAOE,QAEdF,EAAOlL,UAGhB+K,EAwCF,WACE,IAAIA,EAAIC,EAEJ7S,EAAuB,GAAdwR,GAAmB,GAC5BuB,EAASC,GAAiBhT,GAE9B,OAAI+S,GACFvB,GAAcuB,EAAOE,QAEdF,EAAOlL,SAIsB,KAAlCqF,EAAMZ,WAAWkF,KACnBqB,EA35BU,IA45BVrB,OAEAqB,EAAKxF,EACwBsF,GAASnE,IAEpCqE,IAAOxF,IAETwF,EAj6B+B,CAAE/R,KAAM,WAAYwO,MAi6BtCuD,IAEfD,EAAKC,EAELG,GAAiBhT,GAAO,CAAEiT,QAASzB,GAAa3J,OAAQ+K,GAEjDA,GApEF8B,MACMrH,IACTuF,EAqEJ,WACE,IAAIA,EAAIC,EAAIC,EAER9S,EAAuB,GAAdwR,GAAmB,GAC5BuB,EAASC,GAAiBhT,GAE9B,OAAI+S,GACFvB,GAAcuB,EAAOE,QAEdF,EAAOlL,SAGhB+K,EAAKpB,GACiC,KAAlCtE,EAAMZ,WAAWkF,KACnBqB,EAv7BU,IAw7BVrB,OAEAqB,EAAKxF,EACwBsF,GAASlE,IAEpCoE,IAAOxF,IACTwF,EAAK,MAEHA,IAAOxF,IACTyF,EAAKQ,QACMjG,EAGTuF,EADAC,EAl8B6B,CAAE/R,KAAM,aAAcwO,MAk8BtCwD,IAOftB,GAAcoB,EACdA,EAAKvF,GAGP2F,GAAiBhT,GAAO,CAAEiT,QAASzB,GAAa3J,OAAQ+K,GAEjDA,GA7GA+B,MACMtH,IACTuF,EA8GN,WACE,IAAIA,EAAIC,EAAQa,EAAQE,EAEpB5T,EAAuB,GAAdwR,GAAmB,GAC5BuB,EAASC,GAAiBhT,GAE9B,OAAI+S,GACFvB,GAAcuB,EAAOE,QAEdF,EAAOlL,SAGhB+K,EAAKpB,GACiC,KAAlCtE,EAAMZ,WAAWkF,KACnBqB,EA/9BU,IAg+BVrB,OAEAqB,EAAKxF,EACwBsF,GAASjE,IAEpCmE,IAAOxF,GACJ6F,OACM7F,IACTqG,EAmON,WACE,IAAId,EAAIC,EAAQa,EAAQE,EAEpB5T,EAAuB,GAAdwR,GAAmB,GAC5BuB,EAASC,GAAiBhT,GAE9B,OAAI+S,GACFvB,GAAcuB,EAAOE,QAEdF,EAAOlL,SAGhB+K,EAAKpB,IACLqB,EAAK+B,QACMvH,GACJ6F,OACM7F,IACTqG,EAjJN,WACE,IAAId,EAAIC,EAAIC,EAER9S,EAAuB,GAAdwR,GAAmB,GAC5BuB,EAASC,GAAiBhT,GAE9B,OAAI+S,GACFvB,GAAcuB,EAAOE,QAEdF,EAAOlL,SAGhB+K,EAAKpB,GACiC,KAAlCtE,EAAMZ,WAAWkF,KACnBqB,EAtmCU,IAumCVrB,OAEAqB,EAAKxF,EACwBsF,GAASpE,IAEpCsE,IAAOxF,IACTwF,EAAK,MAEHA,IAAOxF,GAC6B,KAAlCH,EAAMZ,WAAWkF,KACnBsB,EA7lCQ,IA8lCRtB,OAEAsB,EAAKzF,EACwBsF,GAAS7D,IAEpCgE,IAAOzF,GAETwF,EAAK9D,EAAQ8D,GACbD,EAAKC,IAELrB,GAAcoB,EACdA,EAAKvF,KAGPmE,GAAcoB,EACdA,EAAKvF,GAGP2F,GAAiBhT,GAAO,CAAEiT,QAASzB,GAAa3J,OAAQ+K,GAEjDA,GAmGEiC,MACMxH,GACJ6F,OACM7F,IACTuG,EA+bV,WACE,IAAIhB,EAAIC,EAAQa,EAAIC,EAAIC,EAEpB5T,EAAuB,GAAdwR,GAAmB,GAC5BuB,EAASC,GAAiBhT,GAE9B,GAAI+S,EAGF,OAFAvB,GAAcuB,EAAOE,QAEdF,EAAOlL,OAWhB,GARA+K,EAAKpB,GA/nDO,UAgoDRtE,EAAM4H,OAAOtD,GAAa,IAC5BqB,EAjoDU,QAkoDVrB,IAAe,IAEfqB,EAAKxF,EACwBsF,GAASpC,IAEpCsC,IAAOxF,EAET,GADK6F,OACM7F,EAAY,CASrB,GARAqG,EAAK,GACDlD,EAAQ+C,KAAKrG,EAAMsG,OAAOhC,MAC5BmC,EAAKzG,EAAMsG,OAAOhC,IAClBA,OAEAmC,EAAKtG,EACwBsF,GAASlC,IAEpCkD,IAAOtG,EACT,KAAOsG,IAAOtG,GACZqG,EAAG3L,KAAK4L,GACJnD,EAAQ+C,KAAKrG,EAAMsG,OAAOhC,MAC5BmC,EAAKzG,EAAMsG,OAAOhC,IAClBA,OAEAmC,EAAKtG,EACwBsF,GAASlC,SAI1CiD,EAAKrG,EAEHqG,IAAOrG,IACTsG,EAAKT,QACM7F,GAC6B,KAAlCH,EAAMZ,WAAWkF,KACnBoC,EAhqDE,IAiqDFpC,OAEAoC,EAAKvG,EACwBsF,GAASjC,IAEpCkD,IAAOvG,GAETwF,EAtqDuB,CAAE/R,KAAM,OAAQwO,MAsqD1BoE,EAtqDmC7G,KAAK,KAuqDrD+F,EAAKC,IAELrB,GAAcoB,EACdA,EAAKvF,KAOTmE,GAAcoB,EACdA,EAAKvF,QAGPmE,GAAcoB,EACdA,EAAKvF,OAGPmE,GAAcoB,EACdA,EAAKvF,EAKP,OAFA2F,GAAiBhT,GAAO,CAAEiT,QAASzB,GAAa3J,OAAQ+K,GAEjDA,EAjhBMmC,MACM1H,IACTuG,EA0jBZ,WACE,IAAIhB,EAAIC,EAAIC,EAAIY,EAAIC,EApuDIqB,EAsuDpBhV,EAAuB,GAAdwR,GAAmB,GAC5BuB,EAASC,GAAiBhT,GAE9B,GAAI+S,EAGF,OAFAvB,GAAcuB,EAAOE,QAEdF,EAAOlL,OAWhB,GARA+K,EAAKpB,GACiC,KAAlCtE,EAAMZ,WAAWkF,KACnBqB,EArvDU,IAsvDVrB,OAEAqB,EAAKxF,EACwBsF,GAAS9B,IAEpCgC,IAAOxF,EAAY,CASrB,GARAyF,EAAK,GACDhC,EAAQyC,KAAKrG,EAAMsG,OAAOhC,MAC5BkC,EAAKxG,EAAMsG,OAAOhC,IAClBA,OAEAkC,EAAKrG,EACwBsF,GAAS5B,IAEpC2C,IAAOrG,EACT,KAAOqG,IAAOrG,GACZyF,EAAG/K,KAAK2L,GACJ5C,EAAQyC,KAAKrG,EAAMsG,OAAOhC,MAC5BkC,EAAKxG,EAAMsG,OAAOhC,IAClBA,OAEAkC,EAAKrG,EACwBsF,GAAS5B,SAI1C+B,EAAKzF,EAEHyF,IAAOzF,GAC6B,KAAlCH,EAAMZ,WAAWkF,KACnBkC,EApxDM,IAqxDNlC,OAEAkC,EAAKrG,EACwBsF,GAAS9B,IAEpC6C,IAAOrG,IACTsG,EA5FR,WACE,IAAIf,EAAIC,EAEJ7S,EAAuB,GAAdwR,GAAmB,GAC5BuB,EAASC,GAAiBhT,GAE9B,GAAI+S,EAGF,OAFAvB,GAAcuB,EAAOE,QAEdF,EAAOlL,OAWhB,GARA+K,EAAK,GACDjC,EAAQ4C,KAAKrG,EAAMsG,OAAOhC,MAC5BqB,EAAK3F,EAAMsG,OAAOhC,IAClBA,OAEAqB,EAAKxF,EACwBsF,GAAS/B,IAEpCiC,IAAOxF,EACT,KAAOwF,IAAOxF,GACZuF,EAAG7K,KAAK8K,GACJlC,EAAQ4C,KAAKrG,EAAMsG,OAAOhC,MAC5BqB,EAAK3F,EAAMsG,OAAOhC,IAClBA,OAEAqB,EAAKxF,EACwBsF,GAAS/B,SAI1CgC,EAAKvF,EAKP,OAFA2F,GAAiBhT,GAAO,CAAEiT,QAASzB,GAAa3J,OAAQ+K,GAEjDA,EAuDIqC,MACM5H,IACTsG,EAAK,MAEHA,IAAOtG,GA3xDO2H,EA6xDCrB,EAAjBd,EA7xD+B,CAC/B/R,KAAM,SAAUwO,MAAO,IAAI4F,OA4xDdpC,EA5xDuBjG,KAAK,IAAKmI,EAAOA,EAAKnI,KAAK,IAAM,KA6xDrE+F,EAAKC,IAELrB,GAAcoB,EACdA,EAAKvF,KAGPmE,GAAcoB,EACdA,EAAKvF,KAGPmE,GAAcoB,EACdA,EAAKvF,QAGPmE,GAAcoB,EACdA,EAAKvF,EAKP,OAFA2F,GAAiBhT,GAAO,CAAEiT,QAASzB,GAAa3J,OAAQ+K,GAEjDA,EAzoBQuC,IAEHvB,IAAOvG,GAETwF,EAAKzD,EAAQyD,EAAIa,EAAIE,GACrBhB,EAAKC,IAELrB,GAAcoB,EACdA,EAAKvF,KAebmE,GAAcoB,EACdA,EAAKvF,GAEHuF,IAAOvF,IACTuF,EAAKpB,IACLqB,EAAK+B,QACMvH,GACJ6F,OACM7F,IACTqG,EAjPR,WACE,IAAId,EAAIC,EAAIC,EAER9S,EAAuB,GAAdwR,GAAmB,GAC5BuB,EAASC,GAAiBhT,GAE9B,OAAI+S,GACFvB,GAAcuB,EAAOE,QAEdF,EAAOlL,SAGhB+K,EAAKpB,GACD5C,EAAQ2E,KAAKrG,EAAMsG,OAAOhC,MAC5BqB,EAAK3F,EAAMsG,OAAOhC,IAClBA,OAEAqB,EAAKxF,EACwBsF,GAAS9D,IAEpCgE,IAAOxF,IACTwF,EAAK,MAEHA,IAAOxF,GAC6B,KAAlCH,EAAMZ,WAAWkF,KACnBsB,EAniCQ,IAoiCRtB,OAEAsB,EAAKzF,EACwBsF,GAAS7D,IAEpCgE,IAAOzF,GAETwF,EAAK9D,EAAQ8D,GACbD,EAAKC,IAELrB,GAAcoB,EACdA,EAAKvF,KAGPmE,GAAcoB,EACdA,EAAKvF,GAEHuF,IAAOvF,IACL4B,EAAQsE,KAAKrG,EAAMsG,OAAOhC,MAC5BoB,EAAK1F,EAAMsG,OAAOhC,IAClBA,OAEAoB,EAAKvF,EACwBsF,GAASzD,KAI1C8D,GAAiBhT,GAAO,CAAEiT,QAASzB,GAAa3J,OAAQ+K,GAEjDA,GA0LIwC,MACM/H,GACJ6F,OACM7F,IACTuG,EA+CZ,WACE,IAAIhB,EAAIC,EAAIC,EAAIY,EAAIC,EAAIC,EAEpB5T,EAAuB,GAAdwR,GAAmB,GAC5BuB,EAASC,GAAiBhT,GAE9B,GAAI+S,EAGF,OAFAvB,GAAcuB,EAAOE,QAEdF,EAAOlL,OAWhB,GARA+K,EAAKpB,GACiC,KAAlCtE,EAAMZ,WAAWkF,KACnBqB,EA9yCU,IA+yCVrB,OAEAqB,EAAKxF,EACwBsF,GAASnD,IAEpCqD,IAAOxF,EAAY,CAuCrB,IAtCAyF,EAAK,GACDrD,EAAQ8D,KAAKrG,EAAMsG,OAAOhC,MAC5BkC,EAAKxG,EAAMsG,OAAOhC,IAClBA,OAEAkC,EAAKrG,EACwBsF,GAASjD,IAEpCgE,IAAOrG,IACTqG,EAAKlC,GACiC,KAAlCtE,EAAMZ,WAAWkF,KACnBmC,EA5zCM,KA6zCNnC,OAEAmC,EAAKtG,EACwBsF,GAAShD,IAEpCgE,IAAOtG,GACLH,EAAM3L,OAASiQ,IACjBoC,EAAK1G,EAAMsG,OAAOhC,IAClBA,OAEAoC,EAAKvG,EACwBsF,GAAS/C,IAEpCgE,IAAOvG,GAETsG,EAAK9D,EAAQ8D,EAAIC,GACjBF,EAAKC,IAELnC,GAAckC,EACdA,EAAKrG,KAGPmE,GAAckC,EACdA,EAAKrG,IAGFqG,IAAOrG,GACZyF,EAAG/K,KAAK2L,GACJjE,EAAQ8D,KAAKrG,EAAMsG,OAAOhC,MAC5BkC,EAAKxG,EAAMsG,OAAOhC,IAClBA,OAEAkC,EAAKrG,EACwBsF,GAASjD,IAEpCgE,IAAOrG,IACTqG,EAAKlC,GACiC,KAAlCtE,EAAMZ,WAAWkF,KACnBmC,EAn2CI,KAo2CJnC,OAEAmC,EAAKtG,EACwBsF,GAAShD,IAEpCgE,IAAOtG,GACLH,EAAM3L,OAASiQ,IACjBoC,EAAK1G,EAAMsG,OAAOhC,IAClBA,OAEAoC,EAAKvG,EACwBsF,GAAS/C,IAEpCgE,IAAOvG,GAETsG,EAAK9D,EAAQ8D,EAAIC,GACjBF,EAAKC,IAELnC,GAAckC,EACdA,EAAKrG,KAGPmE,GAAckC,EACdA,EAAKrG,IAIPyF,IAAOzF,GAC6B,KAAlCH,EAAMZ,WAAWkF,KACnBkC,EAr4CM,IAs4CNlC,OAEAkC,EAAKrG,EACwBsF,GAASnD,IAEpCkE,IAAOrG,GAETwF,EAAK9C,EAAQ+C,GACbF,EAAKC,IAELrB,GAAcoB,EACdA,EAAKvF,KAGPmE,GAAcoB,EACdA,EAAKvF,QAGPmE,GAAcoB,EACdA,EAAKvF,EAEP,GAAIuF,IAAOvF,EAST,GARAuF,EAAKpB,GACiC,KAAlCtE,EAAMZ,WAAWkF,KACnBqB,EAn5CQ,IAo5CRrB,OAEAqB,EAAKxF,EACwBsF,GAASzC,IAEpC2C,IAAOxF,EAAY,CAuCrB,IAtCAyF,EAAK,GACD3C,EAAQoD,KAAKrG,EAAMsG,OAAOhC,MAC5BkC,EAAKxG,EAAMsG,OAAOhC,IAClBA,OAEAkC,EAAKrG,EACwBsF,GAASvC,IAEpCsD,IAAOrG,IACTqG,EAAKlC,GACiC,KAAlCtE,EAAMZ,WAAWkF,KACnBmC,EA56CI,KA66CJnC,OAEAmC,EAAKtG,EACwBsF,GAAShD,IAEpCgE,IAAOtG,GACLH,EAAM3L,OAASiQ,IACjBoC,EAAK1G,EAAMsG,OAAOhC,IAClBA,OAEAoC,EAAKvG,EACwBsF,GAAS/C,IAEpCgE,IAAOvG,GAETsG,EAAK9D,EAAQ8D,EAAIC,GACjBF,EAAKC,IAELnC,GAAckC,EACdA,EAAKrG,KAGPmE,GAAckC,EACdA,EAAKrG,IAGFqG,IAAOrG,GACZyF,EAAG/K,KAAK2L,GACJvD,EAAQoD,KAAKrG,EAAMsG,OAAOhC,MAC5BkC,EAAKxG,EAAMsG,OAAOhC,IAClBA,OAEAkC,EAAKrG,EACwBsF,GAASvC,IAEpCsD,IAAOrG,IACTqG,EAAKlC,GACiC,KAAlCtE,EAAMZ,WAAWkF,KACnBmC,EAn9CE,KAo9CFnC,OAEAmC,EAAKtG,EACwBsF,GAAShD,IAEpCgE,IAAOtG,GACLH,EAAM3L,OAASiQ,IACjBoC,EAAK1G,EAAMsG,OAAOhC,IAClBA,OAEAoC,EAAKvG,EACwBsF,GAAS/C,IAEpCgE,IAAOvG,GAETsG,EAAK9D,EAAQ8D,EAAIC,GACjBF,EAAKC,IAELnC,GAAckC,EACdA,EAAKrG,KAGPmE,GAAckC,EACdA,EAAKrG,IAIPyF,IAAOzF,GAC6B,KAAlCH,EAAMZ,WAAWkF,KACnBkC,EA1+CI,IA2+CJlC,OAEAkC,EAAKrG,EACwBsF,GAASzC,IAEpCwD,IAAOrG,GAETwF,EAAK9C,EAAQ+C,GACbF,EAAKC,IAELrB,GAAcoB,EACdA,EAAKvF,KAGPmE,GAAcoB,EACdA,EAAKvF,QAGPmE,GAAcoB,EACdA,EAAKvF,EAMT,OAFA2F,GAAiBhT,GAAO,CAAEiT,QAASzB,GAAa3J,OAAQ+K,GAEjDA,EA9RQyC,MACMhI,IACTuG,EA+Rd,WACE,IAAIhB,EAAIC,EAAIC,EAAIY,EAlgDK1E,EAAGc,EAERwF,EAkgDZtV,EAAuB,GAAdwR,GAAmB,GAC5BuB,EAASC,GAAiBhT,GAE9B,GAAI+S,EAGF,OAFAvB,GAAcuB,EAAOE,QAEdF,EAAOlL,OAahB,IAVA+K,EAAKpB,GACLqB,EAAKrB,GACLsB,EAAK,GACDzC,EAAQkD,KAAKrG,EAAMsG,OAAOhC,MAC5BkC,EAAKxG,EAAMsG,OAAOhC,IAClBA,OAEAkC,EAAKrG,EACwBsF,GAASrC,IAEjCoD,IAAOrG,GACZyF,EAAG/K,KAAK2L,GACJrD,EAAQkD,KAAKrG,EAAMsG,OAAOhC,MAC5BkC,EAAKxG,EAAMsG,OAAOhC,IAClBA,OAEAkC,EAAKrG,EACwBsF,GAASrC,IAyB1C,GAtBIwC,IAAOzF,GAC6B,KAAlCH,EAAMZ,WAAWkF,KACnBkC,EA7jDQ,IA8jDRlC,OAEAkC,EAAKrG,EACwBsF,GAASxD,IAEpCuE,IAAOrG,EAETwF,EADAC,EAAK,CAACA,EAAIY,IAGVlC,GAAcqB,EACdA,EAAKxF,KAGPmE,GAAcqB,EACdA,EAAKxF,GAEHwF,IAAOxF,IACTwF,EAAK,MAEHA,IAAOxF,EAAY,CASrB,GARAyF,EAAK,GACDzC,EAAQkD,KAAKrG,EAAMsG,OAAOhC,MAC5BkC,EAAKxG,EAAMsG,OAAOhC,IAClBA,OAEAkC,EAAKrG,EACwBsF,GAASrC,IAEpCoD,IAAOrG,EACT,KAAOqG,IAAOrG,GACZyF,EAAG/K,KAAK2L,GACJrD,EAAQkD,KAAKrG,EAAMsG,OAAOhC,MAC5BkC,EAAKxG,EAAMsG,OAAOhC,IAClBA,OAEAkC,EAAKrG,EACwBsF,GAASrC,SAI1CwC,EAAKzF,EAEHyF,IAAOzF,GA9kDWyC,EAglDHgD,EA9kDLwC,GAFKtG,EAglDJ6D,GA9kDqB,GAAGxE,OAAOkH,MAAM,GAAIvG,GAAGnC,KAAK,IAAM,GA8kDpEgG,EA7kDa,CAAE/R,KAAM,UAAWwO,MAAOkG,WAAWF,EAAkBxF,EAAEjD,KAAK,MA8kD3E+F,EAAKC,IAELrB,GAAcoB,EACdA,EAAKvF,QAGPmE,GAAcoB,EACdA,EAAKvF,EAKP,OAFA2F,GAAiBhT,GAAO,CAAEiT,QAASzB,GAAa3J,OAAQ+K,GAEjDA,EA3XU6C,MACMpI,IACTuG,EA4XhB,WACE,IAAIhB,EAAIC,EAEJ7S,EAAuB,GAAdwR,GAAmB,GAC5BuB,EAASC,GAAiBhT,GAE9B,OAAI+S,GACFvB,GAAcuB,EAAOE,QAEdF,EAAOlL,UAIhBgL,EAAKS,QACMjG,IAETwF,EA3mD+B,CAAE/R,KAAM,UAAWwO,MA2mDrCuD,IAEfD,EAAKC,EAELG,GAAiBhT,GAAO,CAAEiT,QAASzB,GAAa3J,OAAQ+K,GAEjDA,GAlZY8C,IAGL9B,IAAOvG,GAETwF,EAAKzD,EAAQyD,EAAIa,EAAIE,GACrBhB,EAAKC,IAELrB,GAAcoB,EACdA,EAAKvF,KAebmE,GAAcoB,EACdA,EAAKvF,GAEHuF,IAAOvF,IACTuF,EAAKpB,IACLqB,EAAK+B,QACMvH,IAETwF,EAtxC8B,CAAE/R,KAAM,YAAaiK,KAsxCtC8H,IAEfD,EAAKC,IAITG,GAAiBhT,GAAO,CAAEiT,QAASzB,GAAa3J,OAAQ+K,GAEjDA,GA1UE+C,MACMtI,GACJ6F,OACM7F,GAC6B,KAAlCH,EAAMZ,WAAWkF,KACnBoC,EA3+BE,IA4+BFpC,OAEAoC,EAAKvG,EACwBsF,GAAShE,IAEpCiF,IAAOvG,EAGTuF,EADAC,EAAaa,GAGblC,GAAcoB,EACdA,EAAKvF,KAebmE,GAAcoB,EACdA,EAAKvF,GAGP2F,GAAiBhT,GAAO,CAAEiT,QAASzB,GAAa3J,OAAQ+K,GAEjDA,GA3KEgD,MACMvI,IACTuF,EAygCR,WACE,IAAIA,EAAIC,EAAIC,EAAIY,EAAIC,EAAIC,EAAIC,EAnzDPvS,EAqzDjBtB,EAAuB,GAAdwR,GAAmB,GAC5BuB,EAASC,GAAiBhT,GAE9B,GAAI+S,EAGF,OAFAvB,GAAcuB,EAAOE,QAEdF,EAAOlL,OAWhB,GARA+K,EAAKpB,GACiC,KAAlCtE,EAAMZ,WAAWkF,KACnBqB,EAh3DU,IAi3DVrB,OAEAqB,EAAKxF,EACwBsF,GAASxD,IAEpC0D,IAAOxF,EAET,IADAyF,EAAKQ,QACMjG,EAAY,CAuBrB,IAtBAqG,EAAK,GACLC,EAAKnC,GACiC,KAAlCtE,EAAMZ,WAAWkF,KACnBoC,EA53DM,IA63DNpC,OAEAoC,EAAKvG,EACwBsF,GAASxD,IAEpCyE,IAAOvG,IACTwG,EAAKP,QACMjG,EAETsG,EADAC,EAAK,CAACA,EAAIC,IAOZrC,GAAcmC,EACdA,EAAKtG,GAEAsG,IAAOtG,GACZqG,EAAG3L,KAAK4L,GACRA,EAAKnC,GACiC,KAAlCtE,EAAMZ,WAAWkF,KACnBoC,EAn5DI,IAo5DJpC,OAEAoC,EAAKvG,EACwBsF,GAASxD,IAEpCyE,IAAOvG,IACTwG,EAAKP,QACMjG,EAETsG,EADAC,EAAK,CAACA,EAAIC,IAOZrC,GAAcmC,EACdA,EAAKtG,GAGLqG,IAAOrG,GAv3DM/L,EAy3DFwR,EAAbD,EAx3DK,CAAE/R,KAAM,QAASiK,KAw3DL2I,EAx3DcU,QAAO,SAASC,EAAMlC,GAAI,OAAOkC,EAAOlC,EAAE,GAAKA,EAAE,KAAO7Q,IAy3DvFsR,EAAKC,IAELrB,GAAcoB,EACdA,EAAKvF,QAGPmE,GAAcoB,EACdA,EAAKvF,OAGPmE,GAAcoB,EACdA,EAAKvF,EAKP,OAFA2F,GAAiBhT,GAAO,CAAEiT,QAASzB,GAAa3J,OAAQ+K,GAEjDA,EAjmCIiD,MACMxI,IACTuF,EAkmCV,WACE,IAAIA,EAAIC,EAAQa,EAAQE,EAEpB5T,EAAuB,GAAdwR,GAAmB,GAC5BuB,EAASC,GAAiBhT,GAE9B,OAAI+S,GACFvB,GAAcuB,EAAOE,QAEdF,EAAOlL,SAGhB+K,EAAKpB,GAt5DO,UAu5DRtE,EAAM4H,OAAOtD,GAAa,IAC5BqB,EAx5DU,QAy5DVrB,IAAe,IAEfqB,EAAKxF,EACwBsF,GAAS3B,IAEpC6B,IAAOxF,GACJ6F,OACM7F,IACTqG,EAAKP,QACM9F,GACJ6F,OACM7F,GAC6B,KAAlCH,EAAMZ,WAAWkF,KACnBoC,EAr7DE,IAs7DFpC,OAEAoC,EAAKvG,EACwBsF,GAASjC,IAEpCkD,IAAOvG,EAGTuF,EADAC,EA56DwB,CAAE/R,KAAM,MAAOsS,UA46D1BM,IAGblC,GAAcoB,EACdA,EAAKvF,KAebmE,GAAcoB,EACdA,EAAKvF,GAGP2F,GAAiBhT,GAAO,CAAEiT,QAASzB,GAAa3J,OAAQ+K,GAEjDA,GA/pCMkD,MACMzI,IACTuF,EAgqCZ,WACE,IAAIA,EAAIC,EAAQa,EAAQE,EAEpB5T,EAAuB,GAAdwR,GAAmB,GAC5BuB,EAASC,GAAiBhT,GAE9B,OAAI+S,GACFvB,GAAcuB,EAAOE,QAEdF,EAAOlL,SAGhB+K,EAAKpB,GAn9DO,cAo9DRtE,EAAM4H,OAAOtD,GAAa,IAC5BqB,EAr9DU,YAs9DVrB,IAAe,IAEfqB,EAAKxF,EACwBsF,GAAS1B,IAEpC4B,IAAOxF,GACJ6F,OACM7F,IACTqG,EAAKP,QACM9F,GACJ6F,OACM7F,GAC6B,KAAlCH,EAAMZ,WAAWkF,KACnBoC,EAr/DE,IAs/DFpC,OAEAoC,EAAKvG,EACwBsF,GAASjC,IAEpCkD,IAAOvG,EAGTuF,EADAC,EAz+DwB,CAAE/R,KAAM,UAAWsS,UAy+D9BM,IAGblC,GAAcoB,EACdA,EAAKvF,KAebmE,GAAcoB,EACdA,EAAKvF,GAGP2F,GAAiBhT,GAAO,CAAEiT,QAASzB,GAAa3J,OAAQ+K,GAEjDA,GA7tCQmD,MACM1I,IACTuF,EA8tCd,WACE,IAAIA,EAAIC,EAAQa,EAAQE,EAEpB5T,EAAuB,GAAdwR,GAAmB,GAC5BuB,EAASC,GAAiBhT,GAE9B,OAAI+S,GACFvB,GAAcuB,EAAOE,QAEdF,EAAOlL,SAGhB+K,EAAKpB,GAhhEO,UAihERtE,EAAM4H,OAAOtD,GAAa,IAC5BqB,EAlhEU,QAmhEVrB,IAAe,IAEfqB,EAAKxF,EACwBsF,GAASzB,KAEpC2B,IAAOxF,GACJ6F,OACM7F,IACTqG,EAvnDN,WACE,IAAId,EAAIC,EAAIC,EAAIY,EAAIC,EAAIC,EAAIC,EAAIC,EAE5B9T,EAAuB,GAAdwR,GAAmB,EAC5BuB,EAASC,GAAiBhT,GAE9B,GAAI+S,EAGF,OAFAvB,GAAcuB,EAAOE,QAEdF,EAAOlL,OAKhB,GAFA+K,EAAKpB,IACLqB,EAAKmB,QACM3G,EAAY,CAmCrB,IAlCAyF,EAAK,GACLY,EAAKlC,IACLmC,EAAKT,QACM7F,GAC6B,KAAlCH,EAAMZ,WAAWkF,KACnBoC,EAxhBM,IAyhBNpC,OAEAoC,EAAKvG,EACwBsF,GAASzE,IAEpC0F,IAAOvG,IACTwG,EAAKX,QACM7F,IACTyG,EAAKE,QACM3G,EAETqG,EADAC,EAAK,CAACA,EAAIC,EAAIC,EAAIC,IAWtBtC,GAAckC,EACdA,EAAKrG,KAGPmE,GAAckC,EACdA,EAAKrG,GAEAqG,IAAOrG,GACZyF,EAAG/K,KAAK2L,GACRA,EAAKlC,IACLmC,EAAKT,QACM7F,GAC6B,KAAlCH,EAAMZ,WAAWkF,KACnBoC,EA3jBI,IA4jBJpC,OAEAoC,EAAKvG,EACwBsF,GAASzE,IAEpC0F,IAAOvG,IACTwG,EAAKX,QACM7F,IACTyG,EAAKE,QACM3G,EAETqG,EADAC,EAAK,CAACA,EAAIC,EAAIC,EAAIC,IAWtBtC,GAAckC,EACdA,EAAKrG,KAGPmE,GAAckC,EACdA,EAAKrG,GAGLyF,IAAOzF,EAGTuF,EADAC,EAAK1E,EAAQ0E,EAAIC,IAGjBtB,GAAcoB,EACdA,EAAKvF,QAGPmE,GAAcoB,EACdA,EAAKvF,EAKP,OAFA2F,GAAiBhT,GAAO,CAAEiT,QAASzB,GAAa3J,OAAQ+K,GAEjDA,EAmhDEoD,MACM3I,GACJ6F,OACM7F,GAC6B,KAAlCH,EAAMZ,WAAWkF,KACnBoC,EArjEE,IAsjEFpC,OAEAoC,EAAKvG,EACwBsF,GAASjC,IAEpCkD,IAAOvG,EAGTuF,EADAC,EAtiEwB,CAAE/R,KAAM,MAAOsS,UAsiE1BM,IAGblC,GAAcoB,EACdA,EAAKvF,KAebmE,GAAcoB,EACdA,EAAKvF,GAGP2F,GAAiBhT,GAAO,CAAEiT,QAASzB,GAAa3J,OAAQ+K,GAEjDA,GA3xCUqD,MACM5I,IACTuF,EA4xChB,WACE,IAAIA,EAAIC,EAEJ7S,EAAuB,GAAdwR,GAAmB,GAC5BuB,EAASC,GAAiBhT,GAE9B,OAAI+S,GACFvB,GAAcuB,EAAOE,QAEdF,EAAOlL,SA1kEJ,iBA8kERqF,EAAM4H,OAAOtD,GAAa,KAC5BqB,EA/kEU,eAglEVrB,IAAe,KAEfqB,EAAKxF,EACwBsF,GAASxB,KAEpC0B,IAAOxF,IAETwF,EArlE8BqD,GAAI,IAulEpCtD,EAAKC,EAELG,GAAiBhT,GAAO,CAAEiT,QAASzB,GAAa3J,OAAQ+K,GAEjDA,GAxzCYuD,MACM9I,IACTuF,EAyzClB,WACE,IAAIA,EAAIC,EAEJ7S,EAAuB,GAAdwR,GAAmB,GAC5BuB,EAASC,GAAiBhT,GAE9B,OAAI+S,GACFvB,GAAcuB,EAAOE,QAEdF,EAAOlL,SAtmEJ,gBA0mERqF,EAAM4H,OAAOtD,GAAa,KAC5BqB,EA3mEU,cA4mEVrB,IAAe,KAEfqB,EAAKxF,EACwBsF,GAASvB,KAEpCyB,IAAOxF,IAETwF,EAjnE8BuD,GAAQ,IAmnExCxD,EAAKC,EAELG,GAAiBhT,GAAO,CAAEiT,QAASzB,GAAa3J,OAAQ+K,GAEjDA,GAr1CcyD,MACMhJ,IACTuF,EAs1CpB,WACE,IAAIA,EAAIC,EAAQa,EAAIC,EAAIC,EAEpB5T,EAAuB,GAAdwR,GAAmB,GAC5BuB,EAASC,GAAiBhT,GAE9B,GAAI+S,EAGF,OAFAvB,GAAcuB,EAAOE,QAEdF,EAAOlL,OAWhB,GARA+K,EAAKpB,GAroEO,gBAsoERtE,EAAM4H,OAAOtD,GAAa,KAC5BqB,EAvoEU,cAwoEVrB,IAAe,KAEfqB,EAAKxF,EACwBsF,GAAStB,KAEpCwB,IAAOxF,EAET,GADK6F,OACM7F,EAAY,CASrB,GARAqG,EAAK,GACDrD,EAAQkD,KAAKrG,EAAMsG,OAAOhC,MAC5BmC,EAAKzG,EAAMsG,OAAOhC,IAClBA,OAEAmC,EAAKtG,EACwBsF,GAASrC,IAEpCqD,IAAOtG,EACT,KAAOsG,IAAOtG,GACZqG,EAAG3L,KAAK4L,GACJtD,EAAQkD,KAAKrG,EAAMsG,OAAOhC,MAC5BmC,EAAKzG,EAAMsG,OAAOhC,IAClBA,OAEAmC,EAAKtG,EACwBsF,GAASrC,SAI1CoD,EAAKrG,EAEHqG,IAAOrG,IACTsG,EAAKT,QACM7F,GAC6B,KAAlCH,EAAMZ,WAAWkF,KACnBoC,EAxsEE,IAysEFpC,OAEAoC,EAAKvG,EACwBsF,GAASjC,IAEpCkD,IAAOvG,GAETwF,EAhrEuBqD,GAAII,SAgrEd5C,EAhrEyB7G,KAAK,IAAK,KAirEhD+F,EAAKC,IAELrB,GAAcoB,EACdA,EAAKvF,KAOTmE,GAAcoB,EACdA,EAAKvF,QAGPmE,GAAcoB,EACdA,EAAKvF,OAGPmE,GAAcoB,EACdA,EAAKvF,EAKP,OAFA2F,GAAiBhT,GAAO,CAAEiT,QAASzB,GAAa3J,OAAQ+K,GAEjDA,EAx6CgB2D,MACMlJ,IACTuF,EAy6CtB,WACE,IAAIA,EAAIC,EAAQa,EAAIC,EAAIC,EAEpB5T,EAAuB,GAAdwR,GAAmB,GAC5BuB,EAASC,GAAiBhT,GAE9B,GAAI+S,EAGF,OAFAvB,GAAcuB,EAAOE,QAEdF,EAAOlL,OAWhB,GARA+K,EAAKpB,GAvtEO,qBAwtERtE,EAAM4H,OAAOtD,GAAa,KAC5BqB,EAztEU,mBA0tEVrB,IAAe,KAEfqB,EAAKxF,EACwBsF,GAASrB,KAEpCuB,IAAOxF,EAET,GADK6F,OACM7F,EAAY,CASrB,GARAqG,EAAK,GACDrD,EAAQkD,KAAKrG,EAAMsG,OAAOhC,MAC5BmC,EAAKzG,EAAMsG,OAAOhC,IAClBA,OAEAmC,EAAKtG,EACwBsF,GAASrC,IAEpCqD,IAAOtG,EACT,KAAOsG,IAAOtG,GACZqG,EAAG3L,KAAK4L,GACJtD,EAAQkD,KAAKrG,EAAMsG,OAAOhC,MAC5BmC,EAAKzG,EAAMsG,OAAOhC,IAClBA,OAEAmC,EAAKtG,EACwBsF,GAASrC,SAI1CoD,EAAKrG,EAEHqG,IAAOrG,IACTsG,EAAKT,QACM7F,GAC6B,KAAlCH,EAAMZ,WAAWkF,KACnBoC,EA7xEE,IA8xEFpC,OAEAoC,EAAKvG,EACwBsF,GAASjC,IAEpCkD,IAAOvG,GAETwF,EAlwEwBuD,GAAQE,SAkwElB5C,EAlwE6B7G,KAAK,IAAK,KAmwErD+F,EAAKC,IAELrB,GAAcoB,EACdA,EAAKvF,KAOTmE,GAAcoB,EACdA,EAAKvF,QAGPmE,GAAcoB,EACdA,EAAKvF,OAGPmE,GAAcoB,EACdA,EAAKvF,EAKP,OAFA2F,GAAiBhT,GAAO,CAAEiT,QAASzB,GAAa3J,OAAQ+K,GAEjDA,EA3/CkB4D,MACMnJ,IACTuF,EA4/CxB,WACE,IAAIA,EAAIC,EAAIC,EAER9S,EAAuB,GAAdwR,GAAmB,GAC5BuB,EAASC,GAAiBhT,GAE9B,OAAI+S,GACFvB,GAAcuB,EAAOE,QAEdF,EAAOlL,SAGhB+K,EAAKpB,GACiC,KAAlCtE,EAAMZ,WAAWkF,KACnBqB,EA3yEW,IA4yEXrB,OAEAqB,EAAKxF,EACwBsF,GAASpB,KAEpCsB,IAAOxF,IACTyF,EAAKQ,QACMjG,EAGTuF,EADAC,EAlzEO,CAAE/R,KAAM,QAASiK,KAkzEV+H,IAOhBtB,GAAcoB,EACdA,EAAKvF,GAGP2F,GAAiBhT,GAAO,CAAEiT,QAASzB,GAAa3J,OAAQ+K,GAEjDA,GAjiDoB6D,IAa3BzD,GAAiBhT,GAAO,CAAEiT,QAASzB,GAAa3J,OAAQ+K,GAEjDA,GAwPT,SAASgC,KACP,IAAIhC,EAAIC,EAAIC,EAAIY,EAAIC,EAAIC,EA/mCH5E,EAAGwF,EAinCpBxU,EAAuB,GAAdwR,GAAmB,GAC5BuB,EAASC,GAAiBhT,GAE9B,GAAI+S,EAGF,OAFAvB,GAAcuB,EAAOE,QAEdF,EAAOlL,OAKhB,GAFA+K,EAAKpB,IACLqB,EAAKS,QACMjG,EAAY,CAuBrB,IAtBAyF,EAAK,GACLY,EAAKlC,GACiC,KAAlCtE,EAAMZ,WAAWkF,KACnBmC,EAloCQ,IAmoCRnC,OAEAmC,EAAKtG,EACwBsF,GAASxD,IAEpCwE,IAAOtG,IACTuG,EAAKN,QACMjG,EAETqG,EADAC,EAAK,CAACA,EAAIC,IAOZpC,GAAckC,EACdA,EAAKrG,GAEAqG,IAAOrG,GACZyF,EAAG/K,KAAK2L,GACRA,EAAKlC,GACiC,KAAlCtE,EAAMZ,WAAWkF,KACnBmC,EAzpCM,IA0pCNnC,OAEAmC,EAAKtG,EACwBsF,GAASxD,IAEpCwE,IAAOtG,IACTuG,EAAKN,QACMjG,EAETqG,EADAC,EAAK,CAACA,EAAIC,IAOZpC,GAAckC,EACdA,EAAKrG,GAGLyF,IAAOzF,GA3qCQ2B,EA6qCJ6D,EA7qCO2B,EA6qCH1B,EACjBF,EADAC,EA5qCS,GAAGxE,OAAOkH,MAAM,CAACvG,GAAIwF,GAAI3H,KAAK,MA+qCvC2E,GAAcoB,EACdA,EAAKvF,QAGPmE,GAAcoB,EACdA,EAAKvF,EAKP,OAFA2F,GAAiBhT,GAAO,CAAEiT,QAASzB,GAAa3J,OAAQ+K,GAEjDA,EAktCP,SAASsD,GAAIQ,GAAK,MAAO,CAAE5V,KAAM,YAAa6V,MAAO,CAAE7V,KAAM,UAAWwO,MAAOoH,IAC/E,SAASN,GAAQM,GAAK,MAAO,CAAE5V,KAAM,iBAAkB6V,MAAO,CAAE7V,KAAM,UAAWwO,MAAOoH,IAkB1F,IAFAtJ,EAAaK,OAEMJ,GAAcmE,KAAgBtE,EAAM3L,OACrD,OAAO6L,EAMP,MAJIA,IAAeC,GAAcmE,GAActE,EAAM3L,QACnDoR,GA/xEK,CAAE7R,KAAM,QAyEiB8J,EA0tE9BiH,GA1tEwChH,EA2tExC+G,GAAiB1E,EAAM3L,OAAS2L,EAAMsG,OAAO5B,IAAkB,KA3tEhB9G,EA4tE/C8G,GAAiB1E,EAAM3L,OACnB8Q,GAAoBT,GAAgBA,GAAiB,GACrDS,GAAoBT,GAAgBA,IA7tEnC,IAAIlH,EACTA,EAAgBW,aAAaT,EAAUC,GACvCD,EACAC,EACAC,KA1Za8L,OCyBrB,SAASC,EAAQ9W,EAAKmJ,GAClB,IAAK,IAAI5H,EAAI,EAAGA,EAAI4H,EAAK3H,SAAUD,EAAG,CAClC,GAAW,MAAPvB,EAAe,OAAOA,EAC1BA,EAAMA,EAAImJ,EAAK5H,IAEnB,OAAOvB,EA6CX,IAAM+W,EAAmC,mBAAZC,QAAyB,IAAIA,QAAU,KASpE,SAASC,EAAWC,GAChB,GAAgB,MAAZA,EACA,OAAO,WAAA,OAAM,GAGjB,GAAqB,MAAjBH,EAAuB,CACvB,IAAII,EAAUJ,EAAcK,IAAIF,GAChC,OAAe,MAAXC,IAGJA,EAAUE,EAAgBH,GAC1BH,EAAcO,IAAIJ,EAAUC,IAHjBA,EAOf,OAAOE,EAAgBH,GAQ3B,SAASG,EAAgBH,GACrB,OAAOA,EAASnW,MACZ,IAAK,WACD,OAAO,WAAA,OAAM,GAEjB,IAAK,aACD,IAAMwO,EAAQ2H,EAAS3H,MAAMgI,cAC7B,OAAO,SAAC9W,EAAM+W,EAAUpK,GACpB,IAAMqK,EAAerK,GAAWA,EAAQqK,aAAgB,OACxD,OAAOlI,IAAU9O,EAAKgX,GAAaF,eAI3C,IAAK,YACD,OAAO,SAAC9W,EAAM+W,GACV,OAA2B,IAApBA,EAAShW,QAGxB,IAAK,QACD,IAAMd,EAAOwW,EAASlM,KAAK0M,MAAM,KACjC,OAAO,SAACjX,EAAM+W,GAEV,OAvFhB,SAASG,EAAOlX,EAAMmX,EAAUlX,EAAMmX,GAElC,IADA,IAAIzV,EAAUwV,EACLrW,EAAIsW,EAAetW,EAAIb,EAAKc,SAAUD,EAAG,CAC9C,GAAe,MAAXa,EACA,OAAO,EAEX,IAAM0V,EAAQ1V,EAAQ1B,EAAKa,IAC3B,GAAIiG,MAAMC,QAAQqQ,GAAQ,CACtB,IAAK,IAAIC,EAAI,EAAGA,EAAID,EAAMtW,SAAUuW,EAChC,GAAIJ,EAAOlX,EAAMqX,EAAMC,GAAIrX,EAAMa,EAAI,GACjC,OAAO,EAGf,OAAO,EAEXa,EAAU0V,EAEd,OAAOrX,IAAS2B,EAsEGuV,CAAOlX,EADG+W,EAAS9W,EAAKc,OAAS,GACVd,EAAM,IAI5C,IAAK,UACD,IAAMsX,EAAWd,EAAS7D,UAAU9E,IAAI0I,GACxC,OAAO,SAACxW,EAAM+W,EAAUpK,GACpB,IAAK,IAAI7L,EAAI,EAAGA,EAAIyW,EAASxW,SAAUD,EACnC,GAAIyW,EAASzW,GAAGd,EAAM+W,EAAUpK,GAAY,OAAO,EAEvD,OAAO,GAIf,IAAK,WACD,IAAM4K,EAAWd,EAAS7D,UAAU9E,IAAI0I,GACxC,OAAO,SAACxW,EAAM+W,EAAUpK,GACpB,IAAK,IAAI7L,EAAI,EAAGA,EAAIyW,EAASxW,SAAUD,EACnC,IAAKyW,EAASzW,GAAGd,EAAM+W,EAAUpK,GAAY,OAAO,EAExD,OAAO,GAIf,IAAK,MACD,IAAM4K,EAAWd,EAAS7D,UAAU9E,IAAI0I,GACxC,OAAO,SAACxW,EAAM+W,EAAUpK,GACpB,IAAK,IAAI7L,EAAI,EAAGA,EAAIyW,EAASxW,SAAUD,EACnC,GAAIyW,EAASzW,GAAGd,EAAM+W,EAAUpK,GAAY,OAAO,EAEvD,OAAO,GAIf,IAAK,MACD,IAAM4K,EAAWd,EAAS7D,UAAU9E,IAAI0I,GACxC,OAAO,SAACxW,EAAM+W,EAAUpK,GACpB,IAAItF,GAAS,EAEPmH,EAAI,GAkBV,OAjBAgJ,EAAWxW,SAAShB,EAAM,CACtBmJ,eAAOnJ,EAAMH,GACK,MAAVA,GAAkB2O,EAAEiJ,QAAQ5X,GAEhC,IAAK,IAAIiB,EAAI,EAAGA,EAAIyW,EAASxW,SAAUD,EACnC,GAAIyW,EAASzW,GAAGd,EAAMwO,EAAG7B,GAGrB,OAFAtF,GAAS,OACTvH,cAKZuJ,iBAAWmF,EAAEkJ,SACbhP,KAAMiE,GAAWA,EAAQgL,YACzBnP,SAAUmE,GAAWA,EAAQnE,UAAY,cAGtCnB,GAIf,IAAK,QACD,IAAMoM,EAAO+C,EAAWC,EAAShD,MAC3BC,EAAQ8C,EAAWC,EAAS/C,OAClC,OAAO,SAAC1T,EAAM+W,EAAUpK,GACpB,SAAIoK,EAAShW,OAAS,GAAK2S,EAAM1T,EAAM+W,EAAUpK,KACtC8G,EAAKsD,EAAS,GAAIA,EAAS3K,MAAM,GAAIO,IAMxD,IAAK,aACD,IAAM8G,EAAO+C,EAAWC,EAAShD,MAC3BC,EAAQ8C,EAAWC,EAAS/C,OAClC,OAAO,SAAC1T,EAAM+W,EAAUpK,GACpB,GAAI+G,EAAM1T,EAAM+W,EAAUpK,GACtB,IAAK,IAAI7L,EAAI,EAAG8W,EAAIb,EAAShW,OAAQD,EAAI8W,IAAK9W,EAC1C,GAAI2S,EAAKsD,EAASjW,GAAIiW,EAAS3K,MAAMtL,EAAI,GAAI6L,GACzC,OAAO,EAInB,OAAO,GAIf,IAAK,YACD,IAAM1M,EAAOwW,EAASlM,KAAK0M,MAAM,KACjC,OAAQR,EAAS1H,UACb,UAAK,EACD,OAAO,SAAC/O,GAAI,OAA4B,MAAvBqW,EAAQrW,EAAMC,IACnC,IAAK,IACD,OAAQwW,EAAS3H,MAAMxO,MACnB,IAAK,SACD,OAAO,SAACN,GACJ,IAAM2R,EAAI0E,EAAQrW,EAAMC,GACxB,MAAoB,iBAAN0R,GAAkB8E,EAAS3H,MAAMA,MAAMiE,KAAKpB,IAElE,IAAK,UACD,IAAM5G,YAAa0L,EAAS3H,MAAMA,OAClC,OAAO,SAAC9O,GAAI,OAAK+K,cAAesL,EAAQrW,EAAMC,KAElD,IAAK,OACD,OAAO,SAACD,GAAI,OAAKyW,EAAS3H,MAAMA,UAAiBuH,EAAQrW,EAAMC,KAEvE,MAAM,IAAImJ,6CAAsCqN,EAAS3H,MAAMxO,OACnE,IAAK,KACD,OAAQmW,EAAS3H,MAAMxO,MACnB,IAAK,SACD,OAAO,SAACN,GAAI,OAAMyW,EAAS3H,MAAMA,MAAMiE,KAAKsD,EAAQrW,EAAMC,KAC9D,IAAK,UACD,IAAM8K,YAAa0L,EAAS3H,MAAMA,OAClC,OAAO,SAAC9O,GAAI,OAAK+K,cAAesL,EAAQrW,EAAMC,KAElD,IAAK,OACD,OAAO,SAACD,GAAI,OAAKyW,EAAS3H,MAAMA,UAAiBuH,EAAQrW,EAAMC,KAEvE,MAAM,IAAImJ,6CAAsCqN,EAAS3H,MAAMxO,OACnE,IAAK,KACD,OAAO,SAACN,GAAI,OAAKqW,EAAQrW,EAAMC,IAASwW,EAAS3H,MAAMA,OAC3D,IAAK,IACD,OAAO,SAAC9O,GAAI,OAAKqW,EAAQrW,EAAMC,GAAQwW,EAAS3H,MAAMA,OAC1D,IAAK,IACD,OAAO,SAAC9O,GAAI,OAAKqW,EAAQrW,EAAMC,GAAQwW,EAAS3H,MAAMA,OAC1D,IAAK,KACD,OAAO,SAAC9O,GAAI,OAAKqW,EAAQrW,EAAMC,IAASwW,EAAS3H,MAAMA,OAE/D,MAAM,IAAI1F,kCAA2BqN,EAAS1H,WAGlD,IAAK,UACD,IAAM0E,EAAO+C,EAAWC,EAAShD,MAC3BC,EAAQ8C,EAAWC,EAAS/C,OAClC,OAAO,SAAC1T,EAAM+W,EAAUpK,GAAO,OAC3B+G,EAAM1T,EAAM+W,EAAUpK,IAClBkL,EAAQ7X,EAAMyT,EAAMsD,EA1QtB,YA0Q2CpK,IACzC8J,EAAShD,KAAKM,SACdN,EAAKzT,EAAM+W,EAAUpK,IACrBkL,EAAQ7X,EAAM0T,EAAOqD,EA5QtB,aA4Q4CpK,IAGvD,IAAK,WACD,IAAM8G,EAAO+C,EAAWC,EAAShD,MAC3BC,EAAQ8C,EAAWC,EAAS/C,OAClC,OAAO,SAAC1T,EAAM+W,EAAUpK,GAAO,OAC3B+G,EAAM1T,EAAM+W,EAAUpK,IAClBmL,EAAS9X,EAAMyT,EAAMsD,EArRvB,YAqR4CpK,IAC1C8J,EAAS/C,MAAMK,SACfN,EAAKzT,EAAM+W,EAAUpK,IACrBmL,EAAS9X,EAAM0T,EAAOqD,EAvRvB,aAuR6CpK,IAGxD,IAAK,YACD,IAAM+I,EAAMe,EAASN,MAAMrH,MACrB4E,EAAQ8C,EAAWC,EAAS/C,OAClC,OAAO,SAAC1T,EAAM+W,EAAUpK,GAAO,OAC3B+G,EAAM1T,EAAM+W,EAAUpK,IAClBoL,EAAS/X,EAAM+W,EAAUrB,EAAK/I,IAG1C,IAAK,iBACD,IAAM+I,GAAOe,EAASN,MAAMrH,MACtB4E,EAAQ8C,EAAWC,EAAS/C,OAClC,OAAO,SAAC1T,EAAM+W,EAAUpK,GAAO,OAC3B+G,EAAM1T,EAAM+W,EAAUpK,IAClBoL,EAAS/X,EAAM+W,EAAUrB,EAAK/I,IAG1C,IAAK,QAED,IAAMpC,EAAOkM,EAASlM,KAAKuM,cAE3B,OAAO,SAAC9W,EAAM+W,EAAUpK,GAEpB,GAAIA,GAAWA,EAAQqL,WACnB,OAAOrL,EAAQqL,WAAWvB,EAASlM,KAAMvK,EAAM+W,GAGnD,GAAIpK,GAAWA,EAAQqK,YAAa,OAAO,EAE3C,OAAOzM,GACH,IAAK,YACD,GAA2B,cAAxBvK,EAAKM,KAAK8L,OAAO,GAAoB,OAAO,EAEnD,IAAK,cACD,MAAgC,gBAAzBpM,EAAKM,KAAK8L,OAAO,IAC5B,IAAK,UACD,GAA2B,YAAxBpM,EAAKM,KAAK8L,OAAO,GAAkB,OAAO,EAEjD,IAAK,aACD,MAAgC,eAAzBpM,EAAKM,KAAK8L,OAAO,KACI,YAAxBpM,EAAKM,KAAK8L,OAAO,IAEC,eAAdpM,EAAKM,OACgB,IAApByW,EAAShW,QAAqC,iBAArBgW,EAAS,GAAGzW,OAE5B,iBAAdN,EAAKM,KACb,IAAK,WACD,MAAqB,wBAAdN,EAAKM,MACM,uBAAdN,EAAKM,MACS,4BAAdN,EAAKM,KAEjB,MAAM,IAAI8I,oCAA6BqN,EAASlM,QAK5D,MAAM,IAAInB,uCAAgCqN,EAASnW,OAkDvD,SAAS2X,EAAejY,EAAM2M,GAC1B,IAAMqK,EAAerK,GAAWA,EAAQqK,aAAgB,OAElDxW,EAAWR,EAAKgX,GACtB,OAAIrK,GAAWA,EAAQgL,aAAehL,EAAQgL,YAAYnX,GAC/CmM,EAAQgL,YAAYnX,GAE3BgX,EAAWtY,YAAYsB,GAChBgX,EAAWtY,YAAYsB,GAE9BmM,GAAuC,mBAArBA,EAAQnE,SACnBmE,EAAQnE,SAASxI,GAGrByI,OAAOC,KAAK1I,GAAMkY,QAAO,SAAU1Y,GACtC,OAAOA,IAAQwX,KAWvB,SAAS3W,EAAOL,EAAM2M,GAClB,IAAMqK,EAAerK,GAAWA,EAAQqK,aAAgB,OACxD,OAAgB,OAAThX,GAAiC,WAAhBmY,EAAOnY,IAAkD,iBAAtBA,EAAKgX,GAapE,SAASa,EAAQ7X,EAAM0W,EAASK,EAAUqB,EAAMzL,GAC5C,IAAO9M,IAAUkX,QACjB,IAAKlX,EAAU,OAAO,EAEtB,IADA,IAAM6I,EAAOuP,EAAepY,EAAQ8M,GAC3B7L,EAAI,EAAGA,EAAI4H,EAAK3H,SAAUD,EAAG,CAClC,IAAMuX,EAAWxY,EAAO6I,EAAK5H,IAC7B,GAAIiG,MAAMC,QAAQqR,GAAW,CACzB,IAAMC,EAAaD,EAASE,QAAQvY,GACpC,GAAIsY,EAAa,EAAK,SACtB,IAAIE,SAAY5W,SAtbV,cAubFwW,GACAI,EAAa,EACb5W,EAAa0W,IAEbE,EAAaF,EAAa,EAC1B1W,EAAayW,EAAStX,QAE1B,IAAK,IAAIuW,EAAIkB,EAAYlB,EAAI1V,IAAc0V,EACvC,GAAIjX,EAAOgY,EAASf,GAAI3K,IAAY+J,EAAQ2B,EAASf,GAAIP,EAAUpK,GAC/D,OAAO,GAKvB,OAAO,EAaX,SAASmL,EAAS9X,EAAM0W,EAASK,EAAUqB,EAAMzL,GAC7C,IAAO9M,IAAUkX,QACjB,IAAKlX,EAAU,OAAO,EAEtB,IADA,IAAM6I,EAAOuP,EAAepY,EAAQ8M,GAC3B7L,EAAI,EAAGA,EAAI4H,EAAK3H,SAAUD,EAAG,CAClC,IAAMuX,EAAWxY,EAAO6I,EAAK5H,IAC7B,GAAIiG,MAAMC,QAAQqR,GAAW,CACzB,IAAMI,EAAMJ,EAASE,QAAQvY,GAC7B,GAAIyY,EAAM,EAAK,SACf,GA3dM,cA2dFL,GAAsBK,EAAM,GAAKpY,EAAOgY,EAASI,EAAM,GAAI9L,IAAY+J,EAAQ2B,EAASI,EAAM,GAAI1B,EAAUpK,GAC5G,OAAO,EAEX,GA7dO,eA6dHyL,GAAuBK,EAAMJ,EAAStX,OAAS,GAAKV,EAAOgY,EAASI,EAAM,GAAI9L,IAAa+J,EAAQ2B,EAASI,EAAM,GAAI1B,EAAUpK,GAChI,OAAO,GAInB,OAAO,EAaX,SAASoL,EAAS/X,EAAM+W,EAAUrB,EAAK/I,GACnC,GAAY,IAAR+I,EAAa,OAAO,EACxB,IAAO7V,IAAUkX,QACjB,IAAKlX,EAAU,OAAO,EAEtB,IADA,IAAM6I,EAAOuP,EAAepY,EAAQ8M,GAC3B7L,EAAI,EAAGA,EAAI4H,EAAK3H,SAAUD,EAAG,CAClC,IAAMuX,EAAWxY,EAAO6I,EAAK5H,IAC7B,GAAIiG,MAAMC,QAAQqR,GAAU,CACxB,IAAMI,EAAM/C,EAAM,EAAI2C,EAAStX,OAAS2U,EAAMA,EAAM,EACpD,GAAI+C,GAAO,GAAKA,EAAMJ,EAAStX,QAAUsX,EAASI,KAASzY,EACvD,OAAO,GAInB,OAAO,EAuCX,SAASgB,EAAS0X,EAAKjC,EAAUvV,EAASyL,GACtC,GAAK8J,EAAL,CACA,IAAMM,EAAW,GACXL,EAAUF,EAAWC,GACrBkC,EAjCV,SAASC,EAASnC,EAAUU,GACxB,GAAgB,MAAZV,GAAuC,UAAnB0B,EAAO1B,GAAwB,MAAO,GAC9C,MAAZU,IAAoBA,EAAWV,GAGnC,IAFA,IAAMoC,EAAUpC,EAAS1C,QAAU,CAACoD,GAAY,GAC1CzO,EAAOD,OAAOC,KAAK+N,GAChB3V,EAAI,EAAGA,EAAI4H,EAAK3H,SAAUD,EAAG,CAClC,IAAM6Q,EAAIjJ,EAAK5H,GACTgY,EAAMrC,EAAS9E,GACrBkH,EAAQtR,WAARsR,IAAgBD,EAASE,EAAW,SAANnH,EAAemH,EAAM3B,KAEvD,OAAO0B,EAuBaD,CAASnC,GAAU3I,IAAI0I,GAC3CgB,EAAWxW,SAAS0X,EAAK,CACrBvP,eAAOnJ,EAAMH,GAET,GADc,MAAVA,GAAkBkX,EAASU,QAAQ5X,GACnC6W,EAAQ1W,EAAM+W,EAAUpK,GACxB,GAAIgM,EAAY5X,OACZ,IAAK,IAAID,EAAI,EAAG8W,EAAIe,EAAY5X,OAAQD,EAAI8W,IAAK9W,EAAG,CAC5C6X,EAAY7X,GAAGd,EAAM+W,EAAUpK,IAC/BzL,EAAQlB,EAAMH,EAAQkX,GAE1B,IAAK,IAAIO,EAAI,EAAGyB,EAAIhC,EAAShW,OAAQuW,EAAIyB,IAAKzB,EAAG,CAC7C,IAAM0B,EAAqBjC,EAAS3K,MAAMkL,EAAI,GAC1CqB,EAAY7X,GAAGiW,EAASO,GAAI0B,EAAoBrM,IAChDzL,EAAQ6V,EAASO,GAAIzX,EAAQmZ,SAKzC9X,EAAQlB,EAAMH,EAAQkX,IAIlC1N,iBAAW0N,EAASW,SACpBhP,KAAMiE,GAAWA,EAAQgL,YACzBnP,SAAUmE,GAAWA,EAAQnE,UAAY,eAajD,SAASiH,EAAMiJ,EAAKjC,EAAU9J,GAC1B,IAAMkM,EAAU,GAIhB,OAHA7X,EAAS0X,EAAKjC,GAAU,SAAUzW,GAC9B6Y,EAAQtR,KAAKvH,KACd2M,GACIkM,EAQX,SAASpM,EAAMgK,GACX,OAAOwC,EAAOxM,MAAMgK,GAUxB,SAASyC,EAAMR,EAAKjC,EAAU9J,GAC1B,OAAO8C,EAAMiJ,EAAKjM,EAAMgK,GAAW9J,GAGvCuM,EAAMzM,MAAQA,EACdyM,EAAMzJ,MAAQA,EACdyJ,EAAMlY,SAAWA,EACjBkY,EAAMC,QAvPN,SAAiBnZ,EAAMyW,EAAUM,EAAUpK,GACvC,OAAK8J,KACAzW,IACA+W,IAAYA,EAAW,IAErBP,EAAWC,EAAXD,CAAqBxW,EAAM+W,EAAUpK,KAmPhDuM,EAAMA,MAAQA"}