import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

// GET - Fetch all cafeteria applications
export async function GET(request: NextRequest) {
  try {
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    const { data: applications, error } = await supabase
      .from('cafeteria_applications')
      .select('*')
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching applications:', error)
      return NextResponse.json(
        { error: 'Failed to fetch applications' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      applications: applications || []
    })

  } catch (error) {
    console.error('Applications fetch error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST - Submit new cafeteria application
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { 
      business_name, 
      owner_name, 
      contact_email, 
      contact_phone, 
      location, 
      description 
    } = body

    if (!business_name || !owner_name || !contact_email || !contact_phone) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    const supabase = createClient(supabaseUrl, supabaseAnonKey)

    // Generate temporary password
    const temp_password = Math.random().toString(36).slice(-8)

    const { data: application, error } = await supabase
      .from('cafeteria_applications')
      .insert({
        business_name,
        owner_name,
        contact_email,
        contact_phone,
        location,
        description,
        temp_password,
        status: 'pending'
      })
      .select()
      .single()

    if (error) {
      console.error('Error creating application:', error)
      return NextResponse.json(
        { error: 'Failed to submit application' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Application submitted successfully',
      application
    })

  } catch (error) {
    console.error('Application submission error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PATCH - Update application status (approve/reject)
export async function PATCH(request: NextRequest) {
  try {
    const body = await request.json()
    const { applicationId, status, reviewNotes } = body

    if (!applicationId || !status) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Update application status
    const { data: application, error } = await supabase
      .from('cafeteria_applications')
      .update({
        status,
        review_notes: reviewNotes,
        reviewed_at: new Date().toISOString()
      })
      .eq('id', applicationId)
      .select()
      .single()

    if (error) {
      console.error('Error updating application:', error)
      return NextResponse.json(
        { error: 'Failed to update application' },
        { status: 500 }
      )
    }

    // If approved, create user account and cafeteria record
    if (status === 'approved') {
      try {
        // Create user account
        const { data: authData, error: authError } = await supabase.auth.admin.createUser({
          email: application.contact_email,
          password: application.temp_password || 'temppass123',
          email_confirm: true,
          user_metadata: {
            full_name: application.owner_name,
            role: 'cafeteria_manager'
          }
        })

        if (authError) {
          console.error('Error creating auth user:', authError)
        } else if (authData.user) {
          // Create profile
          await supabase
            .from('profiles')
            .insert({
              id: authData.user.id,
              full_name: application.owner_name,
              phone: application.contact_phone,
              role: 'cafeteria_manager',
              status: 'active',
              is_active: true
            })

          // Create cafeteria record
          await supabase
            .from('cafeterias')
            .insert({
              name: application.business_name,
              location: application.location,
              description: application.description,
              owner_id: authData.user.id,
              approval_status: 'approved',
              is_active: true,
              is_open: true,
              rating: 0
            })

          console.log('Successfully created user and cafeteria for:', application.business_name)
        }

        // Clear temporary password
        await supabase
          .from('cafeteria_applications')
          .update({ temp_password: null })
          .eq('id', applicationId)

      } catch (approvalError) {
        console.error('Error in approval process:', approvalError)
        // Don't fail the status update if approval process fails
      }
    }

    return NextResponse.json({
      success: true,
      message: `Application ${status} successfully`,
      application
    })

  } catch (error) {
    console.error('Application update error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
