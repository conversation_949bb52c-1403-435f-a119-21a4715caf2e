import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

// GET - Fetch all reports
export async function GET(request: NextRequest) {
  try {
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    const { data: reports, error } = await supabase
      .from('reports')
      .select('*')
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching reports:', error)
      return NextResponse.json(
        { error: 'Failed to fetch reports' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      reports: reports || []
    })

  } catch (error) {
    console.error('Reports fetch error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST - Generate new report
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { reportType, reportPeriod, reportFormat } = body

    if (!reportType || !reportPeriod) {
      return NextResponse.json(
        { error: 'Missing required fields: reportType, reportPeriod' },
        { status: 400 }
      )
    }

    // Generate report name
    const reportName = `${reportType.charAt(0).toUpperCase() + reportType.slice(1)} Report - ${reportPeriod}`

    // Generate mock CSV data based on report type
    let csvData = ''

    switch (reportType) {
      case 'orders':
        csvData = `Order ID,Customer,Cafeteria,Items,Total,Status,Date
ORD001,John Doe,Main Cafeteria,Burger + Fries,25.50 EGP,Completed,2025-01-15
ORD002,Jane Smith,Science Cafeteria,Pizza Slice,15.00 EGP,Completed,2025-01-15
ORD003,Mike Johnson,Library Cafe,Coffee + Sandwich,18.75 EGP,Pending,2025-01-16`
        break
      case 'revenue':
        csvData = `Date,Cafeteria,Orders,Revenue,Commission
2025-01-15,Main Cafeteria,25,1250.00 EGP,125.00 EGP
2025-01-15,Science Cafeteria,18,890.00 EGP,89.00 EGP
2025-01-16,Library Cafe,12,560.00 EGP,56.00 EGP`
        break
      case 'users':
        csvData = `User ID,Name,Email,Role,Status,Join Date
USR001,John Doe,<EMAIL>,Student,Active,2025-01-10
USR002,Jane Smith,<EMAIL>,Student,Active,2025-01-12
USR003,Mike Johnson,<EMAIL>,Cafeteria Manager,Active,2025-01-08`
        break
      default:
        csvData = `Report Type,Period,Generated
${reportType},${reportPeriod},${new Date().toISOString()}`
    }

    // Create data URL for CSV
    const reportDataUrl = `data:text/csv;charset=utf-8,${encodeURIComponent(csvData)}`

    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Save report to database
    const { data: report, error: reportError } = await supabase
      .from('reports')
      .insert({
        name: reportName,
        type: reportType,
        period: reportPeriod,
        format: reportFormat || 'CSV',
        file_url: reportDataUrl,
        status: 'completed'
      })
      .select()
      .single()

    if (reportError) {
      console.error('Error creating report:', reportError)
      return NextResponse.json(
        { error: `Failed to create report: ${reportError.message}` },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Report generated successfully',
      report: {
        id: report.id,
        name: report.name,
        type: report.type,
        period: report.period,
        format: report.format,
        file_url: report.file_url,
        generated: new Date(report.created_at).toLocaleDateString(),
        status: report.status
      }
    })

  } catch (error) {
    console.error('Report generation error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
