import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:unieatsappv0/models/menu_item.dart';
import 'package:unieatsappv0/providers/cart_provider.dart';
import 'package:unieatsappv0/models/cart_item.dart';
import 'package:unieatsappv0/theme/app_theme.dart';
import 'package:unieatsappv0/data/sample_data.dart';
import 'package:unieatsappv0/utils/snackbar_utils.dart';

class ItemDetailsScreenNew extends StatefulWidget {
  final MenuItem menuItem;

  const ItemDetailsScreenNew({
    super.key,
    required this.menuItem,
  });

  @override
  State<ItemDetailsScreenNew> createState() => _ItemDetailsScreenNewState();
}

class _ItemDetailsScreenNewState extends State<ItemDetailsScreenNew> {
  int _quantity = 1;
  String _selectedTab = 'Details';
  final List<String> _tabs = ['Details', 'Ingredients', 'Nutrition'];
  final TextEditingController _notesController = TextEditingController();

  @override
  void dispose() {
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final cartProvider = Provider.of<CartProvider>(context);

    return Scaffold(
      resizeToAvoidBottomInset: true,
      body: SafeArea(
        child: Stack(
          children: [
            // Main content - scrollable
            Padding(
              padding:
                  const EdgeInsets.only(bottom: 80), // Space for bottom bar
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Back button
                    Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 8),
                      child: Row(
                        children: [
                          IconButton(
                            icon: const Icon(
                              Icons.arrow_back,
                              color: Colors.black87,
                            ),
                            onPressed: () => Navigator.of(context).pop(),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Back to Menu',
                            style: theme.textTheme.titleMedium?.copyWith(
                              color: theme.primaryColor,
                            ),
                          ),
                          const Spacer(),
                          IconButton(
                            icon: const Icon(
                              Icons.favorite_border,
                              color: Colors.black87,
                            ),
                            onPressed: () {
                              // Toggle favorite
                            },
                          ),
                        ],
                      ),
                    ),

                    // Item image
                    Stack(
                      children: [
                        SizedBox(
                          width: double.infinity,
                          height: 200,
                          child: Image.asset(
                            widget.menuItem.image,
                            fit: BoxFit.cover,
                          ),
                        ),
                      ],
                    ),

                    // Item details
                    Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Item name and price
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Expanded(
                                child: Text(
                                  widget.menuItem.name,
                                  style:
                                      theme.textTheme.headlineMedium?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                              Text(
                                '${widget.menuItem.price.toStringAsFixed(2)} EGP',
                                style: theme.textTheme.titleLarge?.copyWith(
                                  color: theme.primaryColor,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          // Get cafeteria info
                          Builder(
                            builder: (context) {
                              final cafeteria = cafeterias.firstWhere(
                                (c) => c.id == widget.menuItem.cafeteriaId,
                                orElse: () => cafeterias.first,
                              );

                              return Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    cafeteria.name,
                                    style: theme.textTheme.bodyMedium?.copyWith(
                                      color: theme.primaryColor,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  const SizedBox(height: 2),
                                  Text(
                                    cafeteria.location,
                                    style: theme.textTheme.bodySmall?.copyWith(
                                      color: AppTheme.textSecondaryColor,
                                    ),
                                  ),
                                ],
                              );
                            },
                          ),

                          // Category tag
                          if (widget.menuItem.category.isNotEmpty)
                            Container(
                              margin: const EdgeInsets.symmetric(vertical: 12),
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 12, vertical: 6),
                              decoration: BoxDecoration(
                                color:
                                    theme.colorScheme.surfaceContainerHighest,
                                borderRadius: BorderRadius.circular(16),
                              ),
                              child: Text(
                                widget.menuItem.category,
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: theme.colorScheme.onSurfaceVariant,
                                ),
                              ),
                            ),

                          // Tabs
                          SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: Row(
                              children: _tabs.map((tab) {
                                final isSelected = _selectedTab == tab;
                                return Padding(
                                  padding: const EdgeInsets.only(right: 8),
                                  child: ElevatedButton(
                                    onPressed: () {
                                      setState(() {
                                        _selectedTab = tab;
                                      });
                                    },
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: isSelected
                                          ? theme.primaryColor
                                          : theme.colorScheme.surface,
                                      foregroundColor: isSelected
                                          ? Colors.white
                                          : AppTheme.textColor,
                                      elevation: 0,
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 20, vertical: 12),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(20),
                                        side: BorderSide(
                                          color: isSelected
                                              ? Colors.transparent
                                              : theme.dividerColor,
                                        ),
                                      ),
                                    ),
                                    child: Text(tab),
                                  ),
                                );
                              }).toList(),
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Tab content
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: _buildTabContent(),
                    ),
                  ],
                ),
              ),
            ),

            // Quantity and add to cart - fixed at bottom
            Positioned(
              left: 0,
              right: 0,
              bottom: 0,
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withAlpha(10),
                      blurRadius: 8,
                      offset: const Offset(0, -2),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    // Quantity selector
                    Container(
                      decoration: BoxDecoration(
                        border: Border.all(color: theme.dividerColor),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        children: [
                          IconButton(
                            icon: const Icon(Icons.remove),
                            onPressed: () {
                              if (_quantity > 1) {
                                setState(() {
                                  _quantity--;
                                });
                              }
                            },
                          ),
                          Text(
                            '$_quantity',
                            style: theme.textTheme.titleMedium,
                          ),
                          IconButton(
                            icon: const Icon(Icons.add),
                            onPressed: () {
                              setState(() {
                                _quantity++;
                              });
                            },
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(width: 16),

                    // Add to cart button
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () {
                          // Get cafeteria info
                          final cafeteria = cafeterias.firstWhere(
                            (c) => c.id == widget.menuItem.cafeteriaId,
                            orElse: () => cafeterias.first,
                          );

                          final cartItem = CartItem(
                            id: widget.menuItem.id,
                            name: widget.menuItem.name,
                            price: widget.menuItem.price,
                            image: widget.menuItem.image,
                            cafeteriaName: cafeteria.name,
                            buildingName: cafeteria.location,
                            quantity: _quantity,
                            customizations: {},
                            notes: _notesController.text.isNotEmpty
                                ? _notesController.text
                                : null,
                            menuItem: widget.menuItem,
                          );
                          cartProvider.addItem(cartItem);

                          // Show custom cart notification
                          SnackBarUtils.showCartSnackBar(
                            context: context,
                            message: '${widget.menuItem.name} added to cart',
                            onViewCart: () {
                              Navigator.of(context).pushNamed('/cart');
                            },
                          );
                        },
                        child: const Text('Add to Cart'),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTabContent() {
    final theme = Theme.of(context);

    switch (_selectedTab) {
      case 'Details':
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              widget.menuItem.description,
              style: theme.textTheme.bodyMedium?.copyWith(
                height: 1.5,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                const Icon(
                  Icons.access_time,
                  size: 16,
                  color: AppTheme.textSecondaryColor,
                ),
                const SizedBox(width: 8),
                Text(
                  'Preparation time: 10-12 minutes',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                const Icon(
                  Icons.warning_amber_rounded,
                  size: 16,
                  color: AppTheme.textSecondaryColor,
                ),
                const SizedBox(width: 8),
                Text(
                  'Allergens: Gluten, Dairy',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // Special instructions/notes
            Text(
              'Special Instructions',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: _notesController,
              decoration: InputDecoration(
                hintText: 'Add notes (e.g., no onions, extra sauce, etc.)',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
              maxLines: 3,
            ),
            const SizedBox(height: 24),
          ],
        );
      case 'Ingredients':
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Ingredients list will be displayed here.',
              style: theme.textTheme.bodyMedium,
            ),
            const SizedBox(height: 24),
          ],
        );
      case 'Nutrition':
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Nutrition information will be displayed here.',
              style: theme.textTheme.bodyMedium,
            ),
            const SizedBox(height: 24),
          ],
        );
      default:
        return const SizedBox.shrink();
    }
  }
}
