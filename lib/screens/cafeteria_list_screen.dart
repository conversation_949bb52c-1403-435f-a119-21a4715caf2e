import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:unieatsappv0/models/supabase_models.dart';
import 'package:unieatsappv0/providers/supabase_provider.dart';
import 'package:unieatsappv0/theme/app_theme.dart';

class CafeteriaListScreen extends StatefulWidget {
  const CafeteriaListScreen({super.key});

  @override
  State<CafeteriaListScreen> createState() => _CafeteriaListScreenState();
}

class _CafeteriaListScreenState extends State<CafeteriaListScreen> {
  String _selectedCategory = 'Main';
  List<String> _categories = ['Main', 'Sides', 'Drinks'];
  SupabaseCafeteria? _selectedCafeteria;

  @override
  void initState() {
    super.initState();
    // Load data from Supabase when the screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadCafeteriaData();
    });
  }

  Future<void> _loadCafeteriaData() async {
    final supabaseProvider =
        Provider.of<SupabaseProvider>(context, listen: false);

    // Get the selected cafeteria
    if (supabaseProvider.selectedCafeteria != null) {
      setState(() {
        _selectedCafeteria = supabaseProvider.selectedCafeteria;
      });

      // Load menu items for this cafeteria
      await supabaseProvider.loadMenuItems(_selectedCafeteria!.id);

      // Get menu categories
      final categories = supabaseProvider.menuItemsByCategory.keys.toList();
      if (categories.isNotEmpty) {
        setState(() {
          _categories = categories;
          _selectedCategory = categories.first;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final supabaseProvider = Provider.of<SupabaseProvider>(context);

    return Scaffold(
      body: SafeArea(
        child: supabaseProvider.isLoading
            ? const Center(child: CircularProgressIndicator())
            : Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Back button and header
                  Padding(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    child: Row(
                      children: [
                        IconButton(
                          icon: const Icon(
                            Icons.arrow_back,
                            color: AppTheme.textColor,
                          ),
                          onPressed: () => Navigator.of(context).pop(),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Back',
                          style: theme.textTheme.titleMedium?.copyWith(
                            color: theme.primaryColor,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Cafeteria header with image
                  Stack(
                    children: [
                      // Header image
                      SizedBox(
                        width: double.infinity,
                        height: 180,
                        child: _selectedCafeteria?.imageUrl != null
                            ? Image.network(
                                _selectedCafeteria!.imageUrl!,
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) {
                                  return Image.asset(
                                    'assets/images/cafeteria-header.png',
                                    fit: BoxFit.cover,
                                  );
                                },
                              )
                            : Image.asset(
                                'assets/images/cafeteria-header.png',
                                fit: BoxFit.cover,
                              ),
                      ),

                      // Open badge
                      Positioned(
                        top: 16,
                        right: 16,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 12, vertical: 6),
                          decoration: BoxDecoration(
                            color: _selectedCafeteria?.isActive == true
                                ? theme.colorScheme.primary
                                : Colors.grey,
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: Text(
                            _selectedCafeteria?.isActive == true
                                ? 'Open'
                                : 'Closed',
                            style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),

                  // Cafeteria name and info
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          _selectedCafeteria?.name ?? 'Loading...',
                          style: theme.textTheme.headlineMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          _selectedCafeteria?.location ??
                              'Location not available',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: AppTheme.textSecondaryColor,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Est. pick-up: 10-15 min',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: AppTheme.textSecondaryColor,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Category tabs
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      child: Row(
                        children: _categories.map((category) {
                          final isSelected = _selectedCategory == category;
                          return Padding(
                            padding: const EdgeInsets.only(right: 8),
                            child: ElevatedButton(
                              onPressed: () {
                                setState(() {
                                  _selectedCategory = category;
                                });
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: isSelected
                                    ? theme.primaryColor
                                    : theme.colorScheme.surface,
                                foregroundColor: isSelected
                                    ? Colors.white
                                    : AppTheme.textColor,
                                elevation: 0,
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 20, vertical: 12),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(20),
                                  side: BorderSide(
                                    color: isSelected
                                        ? Colors.transparent
                                        : theme.dividerColor,
                                  ),
                                ),
                              ),
                              child: Text(category),
                            ),
                          );
                        }).toList(),
                      ),
                    ),
                  ),

                  // Menu items
                  Expanded(
                    child: Consumer<SupabaseProvider>(
                      builder: (context, supabaseProvider, _) {
                        final menuItems = supabaseProvider
                                .menuItemsByCategory[_selectedCategory] ??
                            [];

                        if (menuItems.isEmpty) {
                          return const Center(
                            child: Text('No menu items found in this category'),
                          );
                        }

                        return ListView.builder(
                          padding: const EdgeInsets.all(16),
                          itemCount: menuItems.length,
                          itemBuilder: (context, index) {
                            final item = menuItems[index];
                            return _buildMenuItem(
                              item.name,
                              item.description ?? 'No description available',
                              item.price,
                              item.imageUrl ??
                                  'assets/images/food-placeholder.png',
                            );
                          },
                        );
                      },
                    ),
                  ),
                ],
              ),
      ),
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        selectedItemColor: theme.primaryColor,
        unselectedItemColor: AppTheme.textSecondaryColor,
        showSelectedLabels: true,
        showUnselectedLabels: true,
        currentIndex: 0,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.home_outlined),
            activeIcon: Icon(Icons.home),
            label: 'Home',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.favorite_outline),
            activeIcon: Icon(Icons.favorite),
            label: 'Favorites',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.shopping_cart_outlined),
            activeIcon: Icon(Icons.shopping_cart),
            label: 'Cart',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.person_outline),
            activeIcon: Icon(Icons.person),
            label: 'Profile',
          ),
        ],
      ),
    );
  }

  Widget _buildMenuItem(
      String name, String description, double price, String image) {
    final theme = Theme.of(context);
    final supabaseProvider =
        Provider.of<SupabaseProvider>(context, listen: false);
    final menuItems = supabaseProvider.menuItems;

    return GestureDetector(
      onTap: () {
        final item = menuItems.firstWhere(
          (item) => item.name == name,
          orElse: () => menuItems.first,
        );
        Navigator.of(context).pushNamed(
          '/item_details',
          arguments: item,
        );
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 16),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: theme.dividerColor),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Item details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    name,
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    description,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: AppTheme.textSecondaryColor,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 12),
                  Text(
                    '${price.toStringAsFixed(2)} EGP',
                    style: theme.textTheme.titleMedium?.copyWith(
                      color: theme.primaryColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(width: 16),

            // Item image and actions
            Stack(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: image.startsWith('http') || image.startsWith('https')
                      ? Image.network(
                          image,
                          width: 80,
                          height: 80,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Image.asset(
                              'assets/images/food-placeholder.png',
                              width: 80,
                              height: 80,
                              fit: BoxFit.cover,
                            );
                          },
                        )
                      : Image.asset(
                          image,
                          width: 80,
                          height: 80,
                          fit: BoxFit.cover,
                        ),
                ),
                Positioned(
                  bottom: 0,
                  right: 0,
                  child: Container(
                    decoration: BoxDecoration(
                      color: theme.primaryColor,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.add,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
