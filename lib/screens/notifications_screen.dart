import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:unieatsappv0/providers/notification_provider.dart';
import 'package:unieatsappv0/providers/order_provider.dart';
import 'package:unieatsappv0/providers/order_history_provider.dart';
import 'package:unieatsappv0/models/order.dart';
import 'package:intl/intl.dart';

class NotificationsScreen extends StatelessWidget {
  const NotificationsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final notificationProvider = Provider.of<NotificationProvider>(context);
    final notifications = notificationProvider.notifications;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Notifications'),
        actions: [
          if (notifications.isNotEmpty)
            IconButton(
              icon: const Icon(Icons.done_all),
              tooltip: 'Mark all as read',
              onPressed: () {
                notificationProvider.markAllAsRead();
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                      content: Text('All notifications marked as read')),
                );
              },
            ),
          if (notifications.isNotEmpty)
            IconButton(
              icon: const Icon(Icons.delete_sweep),
              tooltip: 'Clear all',
              onPressed: () {
                showDialog(
                  context: context,
                  builder: (ctx) => AlertDialog(
                    title: const Text('Clear All Notifications'),
                    content: const Text(
                        'Are you sure you want to clear all notifications?'),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.of(ctx).pop(),
                        child: const Text('CANCEL'),
                      ),
                      TextButton(
                        onPressed: () {
                          notificationProvider.clearAll();
                          Navigator.of(ctx).pop();
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                                content: Text('All notifications cleared')),
                          );
                        },
                        child: const Text('CLEAR'),
                      ),
                    ],
                  ),
                );
              },
            ),
        ],
      ),
      body: notifications.isEmpty
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.notifications_none,
                    size: 80,
                    color: theme.colorScheme.onSurface
                        .withAlpha(102), // ~0.4 opacity
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'No Notifications',
                    style: theme.textTheme.headlineMedium,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'You don\'t have any notifications yet',
                    style: theme.textTheme.bodyLarge?.copyWith(
                      color: theme.colorScheme.onSurface
                          .withAlpha(153), // ~0.6 opacity
                    ),
                  ),
                ],
              ),
            )
          : ListView.builder(
              itemCount: notifications.length,
              itemBuilder: (context, index) {
                final notification = notifications[index];
                return Dismissible(
                  key: Key(notification.id),
                  background: Container(
                    color: theme.colorScheme.error,
                    alignment: Alignment.centerRight,
                    padding: const EdgeInsets.only(right: 20),
                    child: const Icon(
                      Icons.delete,
                      color: Colors.white,
                    ),
                  ),
                  direction: DismissDirection.endToStart,
                  onDismissed: (direction) {
                    notificationProvider.removeNotification(notification.id);
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Notification removed')),
                    );
                  },
                  child: Card(
                    margin:
                        const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    child: ListTile(
                      leading: _buildNotificationIcon(notification, theme),
                      title: Text(
                        notification.title,
                        style: TextStyle(
                          fontWeight: notification.isRead
                              ? FontWeight.normal
                              : FontWeight.bold,
                        ),
                      ),
                      subtitle: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const SizedBox(height: 4),
                          Text(notification.message),
                          const SizedBox(height: 4),
                          Text(
                            _formatTimestamp(notification.timestamp),
                            style: theme.textTheme.bodySmall,
                          ),
                        ],
                      ),
                      isThreeLine: true,
                      onTap: () {
                        if (!notification.isRead) {
                          notificationProvider.markAsRead(notification.id);
                        }

                        // Handle notification tap based on type
                        if (notification.type == 'order' &&
                            notification.orderId != null) {
                          // Get the order from the provider
                          final orderProvider = Provider.of<OrderProvider>(
                              context,
                              listen: false);
                          final orderHistoryProvider = Provider.of<OrderHistoryProvider>(
                              context,
                              listen: false);

                          // Try to find order in current orders first
                          Order? order = orderProvider
                              .getOrderByNumber(notification.orderId!);

                          // If not found in current orders, try order history
                          if (order == null) {
                            final allOrders = orderHistoryProvider.orders;
                            try {
                              order = allOrders.firstWhere(
                                (o) => o.orderNumber == notification.orderId!
                              );
                            } catch (e) {
                              // Order not found in history either
                              order = null;
                            }
                          }

                          if (order != null) {
                            // Navigate to order tracking screen
                            Navigator.of(context).pushNamed(
                              '/order_tracking',
                              arguments: order,
                            );

                            // Mark notification as read
                            notificationProvider.markAsRead(notification.id);
                          } else {
                            // Order not found - show more helpful message
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text('Order #${notification.orderId} not found in your order history'),
                                duration: const Duration(seconds: 3),
                              ),
                            );
                            // Still mark as read
                            notificationProvider.markAsRead(notification.id);
                          }
                        } else {
                          // Mark notification as read for other types
                          notificationProvider.markAsRead(notification.id);
                        }
                      },
                    ),
                  ),
                );
              },
            ),
    );
  }

  Widget _buildNotificationIcon(AppNotification notification, ThemeData theme) {
    IconData iconData;
    Color iconColor;

    switch (notification.type) {
      case 'order':
        iconData = Icons.receipt;
        iconColor = theme.colorScheme.primary;
        break;
      case 'promo':
        iconData = Icons.local_offer;
        iconColor = theme.colorScheme.secondary;
        break;
      case 'system':
      default:
        iconData = Icons.info;
        iconColor = theme.colorScheme.tertiary;
        break;
    }

    return CircleAvatar(
      backgroundColor: iconColor.withAlpha(25), // ~0.1 opacity
      child: Icon(
        iconData,
        color: iconColor,
      ),
    );
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();

    // Convert timestamp to local time if it's in UTC
    final localTimestamp = timestamp.isUtc ? timestamp.toLocal() : timestamp;
    final difference = now.difference(localTimestamp);

    debugPrint('Notification timestamp: $timestamp (UTC: ${timestamp.isUtc})');
    debugPrint('Local timestamp: $localTimestamp');
    debugPrint('Current time: $now');
    debugPrint('Difference: ${difference.inMinutes} minutes');

    if (difference.inDays > 0) {
      return DateFormat('MMM d, h:mm a').format(localTimestamp);
    } else if (difference.inHours > 0) {
      return '${difference.inHours} ${difference.inHours == 1 ? 'hour' : 'hours'} ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} ${difference.inMinutes == 1 ? 'minute' : 'minutes'} ago';
    } else {
      return 'Just now';
    }
  }
}
