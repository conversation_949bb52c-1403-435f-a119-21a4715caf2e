import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:unieatsappv0/models/supabase_models.dart';
import 'package:unieatsappv0/providers/cart_provider.dart';
import 'package:unieatsappv0/models/cart_item.dart';
import 'package:unieatsappv0/providers/favorites_provider.dart';
import 'package:unieatsappv0/providers/cafeteria_ratings_provider.dart';
import 'package:unieatsappv0/screens/cafeteria_ratings_screen.dart';
import 'package:unieatsappv0/utils/snackbar_utils.dart';
import 'package:unieatsappv0/providers/supabase_provider.dart';

class CafeteriaScreen extends StatefulWidget {
  final String cafeteriaId;
  final SupabaseCafeteria? cafeteria;

  const CafeteriaScreen({
    super.key,
    required this.cafeteriaId,
    this.cafeteria,
  });

  @override
  State<CafeteriaScreen> createState() => _CafeteriaScreenState();
}

class _CafeteriaScreenState extends State<CafeteriaScreen> {
  String? selectedCategory;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final favoritesProvider = Provider.of<FavoritesProvider>(context);
    final supabaseProvider = Provider.of<SupabaseProvider>(context);

    // Get cafeteria from Supabase
    final displayCafeteria = widget.cafeteria ??
        supabaseProvider.cafeterias.firstWhere(
          (c) => c.id == widget.cafeteriaId,
          orElse: () => supabaseProvider.cafeterias.first,
        );

    // We don't need to select the cafeteria here anymore
    // It should already be selected from the dashboard screen

    // Get menu items from Supabase
    final List<SupabaseMenuItem> cafeteriaMenuItems = supabaseProvider.menuItems
        .where((item) => item.cafeteriaId == displayCafeteria.id)
        .toList();

    // Get categories
    final List<String> categories = [
      'All',
      ...supabaseProvider.menuItemsByCategory.keys.toList()
    ];

    // Filter items by category
    final filteredItems = selectedCategory == null || selectedCategory == 'All'
        ? cafeteriaMenuItems
        : cafeteriaMenuItems
            .where((item) => item.category == selectedCategory)
            .toList();

    return Scaffold(
      body: CustomScrollView(
        slivers: [
          SliverAppBar(
            expandedHeight: 200,
            pinned: true,
            actions: [
              IconButton(
                icon: Icon(
                  favoritesProvider.isCafeteriaFavorite(displayCafeteria.id)
                      ? Icons.favorite
                      : Icons.favorite_border,
                  color:
                      favoritesProvider.isCafeteriaFavorite(displayCafeteria.id)
                          ? Colors.red
                          : null,
                ),
                onPressed: () async {
                  await favoritesProvider.toggleSupabaseCafeteriaFavorite(displayCafeteria);

                  // Show feedback to user
                  if (mounted) {
                    final isFavorite = favoritesProvider.isCafeteriaFavorite(displayCafeteria.id);
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(isFavorite
                            ? 'Added ${displayCafeteria.name} to favorites!'
                            : 'Removed ${displayCafeteria.name} from favorites!'),
                        duration: const Duration(seconds: 2),
                        backgroundColor: isFavorite ? Colors.green : Colors.orange,
                      ),
                    );
                  }
                },
                tooltip:
                    favoritesProvider.isCafeteriaFavorite(displayCafeteria.id)
                        ? 'Remove from favorites'
                        : 'Add to favorites',
              ),
            ],
            flexibleSpace: FlexibleSpaceBar(
              title: Text(displayCafeteria.name),
              background: displayCafeteria.imageUrl != null
                  ? Image.network(
                      displayCafeteria.imageUrl!,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          color: theme.colorScheme.surface,
                          child: Icon(Icons.restaurant,
                              color: theme.textTheme.bodySmall?.color,
                              size: 80),
                        );
                      },
                    )
                  : Container(
                      color: theme.colorScheme.surface,
                      child: Icon(Icons.restaurant,
                          color: theme.textTheme.bodySmall?.color, size: 80),
                    ),
            ),
          ),
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Status and Hours
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: displayCafeteria.isActive
                              ? Colors.green.withAlpha(25)
                              : Colors.red.withAlpha(25),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          displayCafeteria.isActive ? 'Open' : 'Closed',
                          style: TextStyle(
                            color: displayCafeteria.isActive
                                ? Colors.green
                                : Colors.red,
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        '8:00 AM - 6:00 PM', // Default hours since not in Supabase model
                        style: theme.textTheme.bodySmall,
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Description
                  Text(
                    displayCafeteria.description ?? 'No description available',
                    style: theme.textTheme.bodyMedium,
                  ),
                  const SizedBox(height: 16),

                  // Location
                  Row(
                    children: [
                      Icon(
                        Icons.location_on_outlined,
                        size: 16,
                        color: theme.textTheme.bodySmall?.color,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        displayCafeteria.location ?? 'Location not available',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.textTheme.bodySmall?.color,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Rating
                  Consumer<CafeteriaRatingsProvider>(
                    builder: (context, ratingsProvider, child) {
                      final avgRating = ratingsProvider
                          .getAverageRatingForCafeteria(displayCafeteria.name);
                      final ratings = ratingsProvider
                          .getRatingsForCafeteria(displayCafeteria.name);
                      final displayRating = avgRating > 0
                          ? avgRating
                          : (displayCafeteria.rating ?? 4.0);

                      return InkWell(
                        onTap: () {
                          Navigator.of(context).push(
                            MaterialPageRoute(
                              builder: (context) => CafeteriaRatingsScreen(
                                cafeteriaId: displayCafeteria.name,
                                cafeteriaName: displayCafeteria.name,
                              ),
                            ),
                          );
                        },
                        child: Row(
                          children: [
                            Row(
                              children: List.generate(5, (index) {
                                return Icon(
                                  index < displayRating.floor()
                                      ? Icons.star
                                      : (index < displayRating
                                          ? Icons.star_half
                                          : Icons.star_border),
                                  color: Colors.amber,
                                  size: 20,
                                );
                              }),
                            ),
                            const SizedBox(width: 8),
                            Text(
                              displayRating.toStringAsFixed(1),
                              style: theme.textTheme.bodyMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(width: 8),
                            Text(
                              '(${ratings.length})',
                              style: theme.textTheme.bodyMedium?.copyWith(
                                color: theme.textTheme.bodySmall?.color,
                              ),
                            ),
                            const Spacer(),
                            Text(
                              'See all ratings',
                              style: theme.textTheme.bodyMedium?.copyWith(
                                color: theme.primaryColor,
                              ),
                            ),
                            Icon(
                              Icons.chevron_right,
                              color: theme.primaryColor,
                              size: 16,
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                  const SizedBox(height: 24),

                  // Menu Title
                  Text(
                    'Menu',
                    style: theme.textTheme.displaySmall,
                  ),
                  const SizedBox(height: 12),
                  // Category Chips
                  SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      children: categories
                          .map((cat) => Padding(
                                padding: const EdgeInsets.only(right: 8),
                                child: ChoiceChip(
                                  label: Text(cat),
                                  selected: selectedCategory == cat ||
                                      (selectedCategory == null &&
                                          cat == 'All'),
                                  onSelected: (_) {
                                    setState(() {
                                      selectedCategory =
                                          cat == 'All' ? null : cat;
                                    });
                                  },
                                ),
                              ))
                          .toList(),
                    ),
                  ),
                ],
              ),
            ),
          ),
          SliverList(
            delegate: SliverChildBuilderDelegate(
              (context, index) => _buildMenuItem(
                  context, filteredItems[index], displayCafeteria),
              childCount: filteredItems.length,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMenuItem(BuildContext context, SupabaseMenuItem item,
      SupabaseCafeteria cafeteria) {
    final theme = Theme.of(context);
    final cartProvider = Provider.of<CartProvider>(context, listen: false);
    final favoritesProvider = Provider.of<FavoritesProvider>(context);

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: InkWell(
        onTap: () {
          Navigator.of(context).pushNamed(
            '/item_details',
            arguments: item,
          );
        },
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Item image
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: item.imageUrl != null
                    ? Image.network(
                        item.imageUrl!,
                        width: 80,
                        height: 80,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            width: 80,
                            height: 80,
                            color: theme.colorScheme.surface,
                            child: Icon(Icons.fastfood,
                                color: theme.textTheme.bodySmall?.color),
                          );
                        },
                      )
                    : Container(
                        width: 80,
                        height: 80,
                        color: theme.colorScheme.surface,
                        child: Icon(Icons.fastfood,
                            color: theme.textTheme.bodySmall?.color),
                      ),
              ),
              const SizedBox(width: 16),

              // Item details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      item.name,
                      style: theme.textTheme.titleMedium,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      item.description ?? 'No description available',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.textTheme.bodySmall?.color,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          '${item.price.toStringAsFixed(2)} EGP',
                          style: theme.textTheme.titleMedium?.copyWith(
                            color: theme.primaryColor,
                          ),
                        ),
                        Row(
                          children: [
                            IconButton(
                              icon: Icon(
                                favoritesProvider.isMenuItemFavorite(item.id)
                                    ? Icons.favorite
                                    : Icons.favorite_border,
                                color: favoritesProvider
                                        .isMenuItemFavorite(item.id)
                                    ? Colors.red
                                    : null,
                              ),
                              onPressed: () {
                                favoritesProvider.toggleMenuItemFavorite(item.id);
                              },
                              tooltip: 'Add to favorites',
                            ),
                            IconButton(
                              icon: const Icon(Icons.add_circle),
                              color: theme.primaryColor,
                              onPressed: () {
                                // Create a CartItem
                                final cartItem = CartItem(
                                  id: item.id,
                                  name: item.name,
                                  price: item.price,
                                  image: item.imageUrl ??
                                      'assets/images/food-placeholder.png',
                                  cafeteriaName: cafeteria.name,
                                  buildingName:
                                      cafeteria.location ?? 'Unknown location',
                                  quantity: 1,
                                  customizations: {},
                                  notes: null,
                                  // We'll need to update the CartItem class to work with SupabaseMenuItem
                                  // For now, we'll just pass null
                                  menuItem: null,
                                );

                                // Add to cart
                                cartProvider.addItem(cartItem);

                                // Show custom cart notification
                                SnackBarUtils.showCartSnackBar(
                                  context: context,
                                  message: '${item.name} added to cart',
                                  onViewCart: () {
                                    Navigator.of(context).pushNamed('/cart');
                                  },
                                );
                              },
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
