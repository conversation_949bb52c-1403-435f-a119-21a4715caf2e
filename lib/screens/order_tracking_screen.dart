import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:unieatsappv0/models/order.dart';
import 'package:unieatsappv0/providers/order_provider.dart';
import 'package:unieatsappv0/providers/order_history_provider.dart';
import 'package:unieatsappv0/providers/cafeteria_ratings_provider.dart';
import 'package:unieatsappv0/providers/menu_item_ratings_provider.dart';
import 'package:unieatsappv0/providers/auth_provider.dart';
import 'package:unieatsappv0/providers/supabase_provider.dart';
import 'package:unieatsappv0/models/cafeteria_rating.dart';
import 'package:unieatsappv0/services/rating_service.dart';
import 'package:unieatsappv0/utils/order_status_utils.dart';
import 'package:intl/intl.dart';
import 'package:unieatsappv0/theme/app_theme.dart';
import 'package:unieatsappv0/widgets/order_rating_dialog.dart';
import 'package:unieatsappv0/screens/support_chat_screen.dart';

class OrderTrackingScreen extends StatefulWidget {
  final Order order;

  const OrderTrackingScreen({
    super.key,
    required this.order,
  });

  @override
  State<OrderTrackingScreen> createState() => _OrderTrackingScreenState();
}

class _OrderTrackingScreenState extends State<OrderTrackingScreen> {
  Timer? _refreshTimer;

  @override
  void initState() {
    super.initState();
    // Set up periodic refresh to check for order status updates
    _refreshTimer = Timer.periodic(const Duration(seconds: 10), (timer) {
      _refreshOrderStatus();
    });
  }

  @override
  void dispose() {
    _refreshTimer?.cancel();
    super.dispose();
  }

  // Refresh order status from Supabase
  void _refreshOrderStatus() async {
    try {
      final orderProvider = Provider.of<OrderProvider>(context, listen: false);
      final supabaseProvider = Provider.of<SupabaseProvider>(context, listen: false);

      // Reload user orders to get the latest status
      await supabaseProvider.loadUserOrders();

      // Get the updated order
      final updatedOrder = orderProvider.getOrderByNumber(widget.order.orderNumber);
      if (updatedOrder != null && updatedOrder.status != widget.order.status) {
        debugPrint('Order status updated: ${widget.order.status} -> ${updatedOrder.status}');
        // The Consumer will automatically rebuild with the new status
      }
    } catch (e) {
      debugPrint('Error refreshing order status: $e');
    }
  }

  void _confirmPickup() {
    // Get providers outside the dialog
    final orderProvider = Provider.of<OrderProvider>(context, listen: false);
    final orderHistoryProvider =
        Provider.of<OrderHistoryProvider>(context, listen: false);
    final cafeteriaRatingsProvider =
        Provider.of<CafeteriaRatingsProvider>(context, listen: false);
    final menuItemRatingsProvider =
        Provider.of<MenuItemRatingsProvider>(context, listen: false);
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final navigator = Navigator.of(context);

    // Show rating dialog
    showDialog(
      context: context,
      barrierDismissible: false, // Prevent dismissing by tapping outside
      builder: (dialogContext) => OrderRatingDialog(
        onSubmit: (rating, comment) async {
          // First close the rating dialog
          Navigator.of(dialogContext).pop();

          // Update order status to completed in Supabase first
          final supabaseProvider = Provider.of<SupabaseProvider>(context, listen: false);
          try {
            await supabaseProvider.updateOrderStatus(widget.order.id, 'completed');
            debugPrint('Order status updated to completed in Supabase');
          } catch (e) {
            debugPrint('Error updating order status in Supabase: $e');
          }

          // Update order status with rating and comment locally
          final updatedOrder = widget.order.copyWith(
            status: OrderStatusUtils.statusCompletedDisplay,
            rating: rating,
            comment: comment,
          );

          // Find and update the order in the providers
          if (mounted) {
            orderProvider.updateOrder(updatedOrder, context: context);
            orderHistoryProvider.updateOrder(updatedOrder);
          }

          // Save order rating to Supabase
          if (mounted) {
            await RatingService.submitOrderRating(
              orderId: widget.order.orderNumber,
              rating: rating,
              comment: comment,
              context: context,
            );
          }

          // Save rating to cafeteria if rating is provided
          if (rating > 0 && widget.order.items.isNotEmpty) {
            final cafeteriaId = widget.order.items.first.cafeteriaName;

            // Add cafeteria rating using the provider (which now uses RatingService)
            if (mounted) {
              final currentContext = context;
              if (currentContext.mounted) {
                cafeteriaRatingsProvider.addRating(
                  CafeteriaRating(
                    id: 'rating_${DateTime.now().millisecondsSinceEpoch}',
                    cafeteriaId: cafeteriaId,
                    rating: rating.toDouble(),
                    comment: comment,
                    date: DateTime.now(),
                    orderId: widget.order.orderNumber,
                    userId: authProvider.currentUser?.id,
                    username: authProvider.currentUser?.name ?? 'Anonymous User',
                  ),
                  context: currentContext,
                );
              }
            }

            // Also rate individual menu items
            for (final item in widget.order.items) {
              final itemId = item.id;
              if (itemId.isNotEmpty) {
                if (mounted) {
                  final currentContext = context;
                  if (currentContext.mounted) {
                    await menuItemRatingsProvider.addRating(
                      menuItemId: itemId,
                      rating: rating.toDouble(),
                      comment: comment,
                      orderId: widget.order.orderNumber,
                      context: currentContext,
                    );
                  }
                }
              }
            }
          }

          // Show thank you dialog after the rating dialog is closed
          if (mounted) {
            showDialog(
              context: context,
              barrierDismissible: false,
              builder: (context) => AlertDialog(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                title: const Text('Thank You!'),
                content: const Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.check_circle,
                      color: Colors.green,
                      size: 64,
                    ),
                    SizedBox(height: 16),
                    Text(
                      'Thank you for your feedback! Your order has been marked as completed.',
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
                actions: [
                  TextButton(
                    onPressed: () {
                      Navigator.of(context).pop();

                      // Navigate to dashboard immediately
                      navigator.pushReplacementNamed('/dashboard');
                    },
                    child: const Text('OK'),
                  ),
                ],
              ),
            );
          }
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final now = DateTime.now();
    final orderDate = DateFormat('MMMM d, yyyy').format(now);
    final orderTime = DateFormat('h:mm a').format(now);

    // Listen to order updates from OrderProvider
    return Consumer<OrderProvider>(
      builder: (context, orderProvider, child) {
        // Get the most up-to-date order from the provider
        final currentOrder = orderProvider.getOrderByNumber(widget.order.orderNumber) ?? widget.order;

        // Debug logging for order status
        debugPrint('Order tracking - Current order status: "${currentOrder.status}"');
        debugPrint('Order tracking - Status lowercase: "${currentOrder.status.toLowerCase()}"');
        debugPrint('Order tracking - StatusReadyForPickup: "${OrderStatusUtils.statusReadyForPickup}"');
        debugPrint('Order tracking - Button should show: ${currentOrder.status.toLowerCase() == 'ready' || currentOrder.status == OrderStatusUtils.statusReadyForPickup}');

        // Calculate progress based on current order status
        double progressValue = OrderStatusUtils.getProgressValue(currentOrder.status);

        return Scaffold(
      body: SafeArea(
        child: Stack(
          children: [
            // Main content - scrollable
            Padding(
              padding: const EdgeInsets.only(
                  bottom: 200), // Space for bottom bar with buttons
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Back button and refresh button
                    Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 8),
                      child: Row(
                        children: [
                          IconButton(
                            icon: const Icon(
                              Icons.arrow_back,
                              color: AppTheme.textColor,
                            ),
                            onPressed: () => Navigator.of(context).pop(),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              'Back to Home',
                              style: theme.textTheme.titleMedium?.copyWith(
                                color: theme.primaryColor,
                              ),
                            ),
                          ),
                          IconButton(
                            icon: const Icon(
                              Icons.refresh,
                              color: AppTheme.textColor,
                            ),
                            onPressed: () {
                              debugPrint('Manual refresh triggered');
                              _refreshOrderStatus();
                            },
                            tooltip: 'Refresh order status',
                          ),
                        ],
                      ),
                    ),

                    // Order tracking header
                    Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Order Tracking',
                            style: theme.textTheme.headlineMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'Track your current order',
                            style: theme.textTheme.bodyMedium?.copyWith(
                              color: AppTheme.textSecondaryColor,
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Order number and date
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Order #${currentOrder.orderNumber}',
                            style: theme.textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            '$orderDate, $orderTime',
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: AppTheme.textSecondaryColor,
                            ),
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 8),

                    // Current status display
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: OrderStatusUtils.getStatusColor(currentOrder.status, context).withAlpha(30),
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(
                            color: OrderStatusUtils.getStatusColor(currentOrder.status, context),
                            width: 1,
                          ),
                        ),
                        child: Text(
                          'Status: ${currentOrder.status}',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: OrderStatusUtils.getStatusColor(currentOrder.status, context),
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Progress bar
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: LinearProgressIndicator(
                          value: progressValue,
                          backgroundColor:
                              theme.colorScheme.surfaceContainerHighest,
                          color: theme.primaryColor,
                          minHeight: 8,
                        ),
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Status icons
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 32),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceAround,
                        children: [
                          _buildStatusIcon(
                            context,
                            OrderStatusUtils.getStatusIcon('preparing'),
                            'Preparing',
                            isActive: true, // Always active as first step
                            isCompleted: true, // Always completed as first step
                          ),
                          _buildStatusIcon(
                            context,
                            OrderStatusUtils.getStatusIcon('ready'),
                            'Ready',
                            isActive: currentOrder.status.toLowerCase() == 'ready' ||
                                      currentOrder.status == OrderStatusUtils.statusReadyForPickup ||
                                      currentOrder.status.toLowerCase() == 'completed' ||
                                      currentOrder.status == OrderStatusUtils.statusCompletedDisplay,
                            isCompleted: currentOrder.status.toLowerCase() == 'ready' ||
                                         currentOrder.status == OrderStatusUtils.statusReadyForPickup ||
                                         currentOrder.status.toLowerCase() == 'completed' ||
                                         currentOrder.status == OrderStatusUtils.statusCompletedDisplay,
                          ),
                          _buildStatusIcon(
                            context,
                            OrderStatusUtils.getStatusIcon('completed'),
                            'Completed',
                            isActive: currentOrder.status.toLowerCase() == 'completed' ||
                                      currentOrder.status == OrderStatusUtils.statusCompletedDisplay,
                            isCompleted: currentOrder.status.toLowerCase() == 'completed' ||
                                         currentOrder.status == OrderStatusUtils.statusCompletedDisplay,
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Pickup time
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: theme.colorScheme.surfaceContainerHighest
                              .withAlpha(30),
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.access_time,
                              color: theme.primaryColor,
                            ),
                            const SizedBox(width: 16),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Pick-up Time',
                                  style: theme.textTheme.bodySmall?.copyWith(
                                    color: AppTheme.textSecondaryColor,
                                  ),
                                ),
                                Text(
                                  currentOrder.pickupTime,
                                  style: theme.textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Pickup location
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: theme.colorScheme.surfaceContainerHighest
                              .withAlpha(30),
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.location_on_outlined,
                              color: theme.primaryColor,
                            ),
                            const SizedBox(width: 16),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Pick-up Location',
                                  style: theme.textTheme.bodySmall?.copyWith(
                                    color: AppTheme.textSecondaryColor,
                                  ),
                                ),
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      currentOrder.items.isNotEmpty
                                          ? currentOrder.items.first.cafeteriaName
                                          : 'Cafeteria',
                                      style:
                                          theme.textTheme.titleMedium?.copyWith(
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    if (currentOrder.items.isNotEmpty &&
                                        currentOrder.items.first.buildingName
                                            .isNotEmpty)
                                      Text(
                                        currentOrder.items.first.buildingName,
                                        style:
                                            theme.textTheme.bodySmall?.copyWith(
                                          color: AppTheme.textSecondaryColor,
                                        ),
                                      ),
                                  ],
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Order details
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Text(
                        'Order Details',
                        style: theme.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Order items
                    ListView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      itemCount: currentOrder.items.length,
                      itemBuilder: (context, index) {
                        final item = currentOrder.items[index];
                        return Padding(
                          padding: const EdgeInsets.only(bottom: 16),
                          child: Row(
                            children: [
                              Text(
                                '${item.quantity}x',
                                style: theme.textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      item.name,
                                      style: theme.textTheme.bodyLarge,
                                    ),
                                    if (item.notes != null &&
                                        item.notes!.isNotEmpty)
                                      Text(
                                        'Note: ${item.notes}',
                                        style:
                                            theme.textTheme.bodySmall?.copyWith(
                                          fontStyle: FontStyle.italic,
                                          color: AppTheme.textSecondaryColor,
                                        ),
                                      ),
                                  ],
                                ),
                              ),
                              Text(
                                'EGP${item.calculateItemTotal().toStringAsFixed(2)}',
                                style: theme.textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),

            // Payment info - fixed at bottom
            Positioned(
              left: 0,
              right: 0,
              bottom: 0,
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                decoration: BoxDecoration(
                  color: Colors.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withAlpha(10),
                      blurRadius: 8,
                      offset: const Offset(0, -2),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Payment Method',
                          style: theme.textTheme.bodyMedium,
                        ),
                        Text(
                          widget.order.paymentMethod ?? 'Cash on Pickup',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Total',
                          style: theme.textTheme.titleMedium,
                        ),
                        Text(
                          'EGP${currentOrder.totalPrice.toStringAsFixed(2)}',
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: theme.primaryColor,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),

                    // Pickup confirmation button (only show if order is ready for pickup)
                    if (currentOrder.status.toLowerCase() == 'ready' ||
                        currentOrder.status == OrderStatusUtils.statusReadyForPickup)
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: _confirmPickup,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppTheme.successColor,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          child: const Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(Icons.check_circle_outline),
                              SizedBox(width: 8),
                              Text(
                                'I\'ve picked up my order',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),

                    const SizedBox(height: 8),
                    SizedBox(
                      width: double.infinity,
                      child: OutlinedButton(
                        onPressed: () {
                          // Navigate to support chat with order context
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => SupportChatScreen(
                                orderId: currentOrder.id,
                                orderNumber: currentOrder.orderNumber,
                              ),
                            ),
                          );
                        },
                        child: const Text('Need Help with Order?'),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
      },
    );
  }

  Widget _buildStatusIcon(BuildContext context, IconData icon, String label,
      {required bool isActive, required bool isCompleted}) {
    final theme = Theme.of(context);
    final color = isActive ? theme.primaryColor : AppTheme.textSecondaryColor;

    return Column(
      children: [
        Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: isCompleted
                ? theme.primaryColor
                : theme.colorScheme.surfaceContainerHighest,
            shape: BoxShape.circle,
          ),
          child: Icon(
            icon,
            color: isCompleted ? Colors.white : AppTheme.textSecondaryColor,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          label,
          style: theme.textTheme.bodySmall?.copyWith(
            color: color,
            fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
          ),
        ),
      ],
    );
  }
}
