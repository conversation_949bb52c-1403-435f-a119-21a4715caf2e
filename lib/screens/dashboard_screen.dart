import 'package:flutter/material.dart';
import 'package:unieatsappv0/models/supabase_models.dart';
import 'package:unieatsappv0/widgets/cafeteria_card.dart';
import 'package:provider/provider.dart';
import 'package:unieatsappv0/providers/order_provider.dart';
import 'package:unieatsappv0/screens/order_tracking_screen.dart';

import 'package:unieatsappv0/providers/notification_provider.dart';
import 'package:unieatsappv0/providers/supabase_provider.dart';
import 'package:unieatsappv0/providers/simple_auth_provider.dart';

class DashboardScreen extends StatelessWidget {
  const DashboardScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    // Get user info from SimpleAuthProvider and data from SupabaseProvider
    final supabaseProvider = Provider.of<SupabaseProvider>(context);
    final authProvider = Provider.of<SimpleAuthProvider>(context);
    final userName = authProvider.currentUser?.fullName ??
        supabaseProvider.currentUser?.fullName ??
        'there';

    // Note: CafeteriaRatingsProvider is used in CafeteriaCard widget

    // Get menu items from Supabase
    final List<SupabaseMenuItem> menuItems = supabaseProvider.menuItems;

    // Update menu items with ratings from orders (if available)
    final List<SupabaseMenuItem> updatedMenuItems = menuItems.map((item) {
      // For now, just return the original items
      // In the future, we can implement a rating system with Supabase
      return item;
    }).toList();

    // Sort by rating (or another criteria) and get top items
    final List<SupabaseMenuItem> popularItems =
        List<SupabaseMenuItem>.from(updatedMenuItems);
    // Sort by price for now (we can change this to rating later)
    popularItems.sort((a, b) => b.price.compareTo(a.price));
    final List<SupabaseMenuItem> topPopularItems =
        popularItems.take(5).toList();

    return Scaffold(
      appBar: AppBar(
        title: const Text('UniEats'),
        actions: [
          Consumer<NotificationProvider>(
            builder: (context, notificationProvider, child) {
              final unreadCount = notificationProvider.unreadCount;
              return Stack(
                alignment: Alignment.center,
                children: [
                  IconButton(
                    icon: const Icon(Icons.notifications),
                    onPressed: () {
                      Navigator.of(context).pushNamed('/notifications');
                    },
                  ),
                  if (unreadCount > 0)
                    Positioned(
                      top: 8,
                      right: 8,
                      child: Container(
                        padding: const EdgeInsets.all(2),
                        decoration: BoxDecoration(
                          color: theme.colorScheme.error,
                          borderRadius: BorderRadius.circular(10),
                        ),
                        constraints: const BoxConstraints(
                          minWidth: 16,
                          minHeight: 16,
                        ),
                        child: Text(
                          unreadCount > 9 ? '9+' : unreadCount.toString(),
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                ],
              );
            },
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          // Get providers before async operations
          final supabaseProvider =
              Provider.of<SupabaseProvider>(context, listen: false);
          final notificationProvider =
              Provider.of<NotificationProvider>(context, listen: false);

          // Refresh data from Supabase
          await supabaseProvider.loadCafeterias();

          // Also refresh notifications
          await notificationProvider.refresh();

          debugPrint(
              'Refreshed cafeterias: ${supabaseProvider.cafeterias.length}');
        },
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            // Welcome message
            Text(
              'Good morning, $userName!',
              style: theme.textTheme.displayMedium,
            ),
            const SizedBox(height: 4),
            Text(
              'What would you like to eat today?',
              style: theme.textTheme.bodyLarge?.copyWith(
                color: theme.textTheme.bodySmall?.color,
              ),
            ),
            const SizedBox(height: 24),

            // Search bar
            GestureDetector(
              onTap: () {
                Navigator.pushNamed(context, '/search');
              },
              child: AbsorbPointer(
                child: TextField(
                  decoration: InputDecoration(
                    hintText: 'Search for food, cafeterias...',
                    prefixIcon: const Icon(Icons.search),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide.none,
                    ),
                    filled: true,
                    fillColor: theme.colorScheme.surface,
                  ),
                  readOnly: true,
                ),
              ),
            ),
            const SizedBox(height: 24),

            // Cafeterias section
            _buildSectionHeader(
              context,
              title: 'Cafeterias',
              onSeeAll: () {
                Navigator.of(context).pushNamed('/cafeterias');
              },
            ),
            const SizedBox(height: 12),
            SizedBox(
              height: 280, // Increased height to accommodate the card content
              child: Consumer<SupabaseProvider>(
                builder: (context, supabaseProvider, _) {
                  final cafeterias = supabaseProvider.cafeterias;

                  // Force reload cafeterias if empty
                  if (cafeterias.isEmpty) {
                    debugPrint(
                        'Dashboard: No cafeterias available, forcing reload');
                    // Use Future.microtask to avoid setState during build
                    Future.microtask(() async {
                      await supabaseProvider.loadCafeterias();
                    });
                    return const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          CircularProgressIndicator(),
                          SizedBox(height: 16),
                          Text('Loading cafeterias...'),
                        ],
                      ),
                    );
                  }

                  debugPrint(
                      'Dashboard: Displaying ${cafeterias.length} cafeterias');

                  // Debug each cafeteria
                  for (var cafeteria in cafeterias) {
                    debugPrint(
                        'Dashboard cafeteria: ${cafeteria.name}, ID: ${cafeteria.id}, isActive: ${cafeteria.isActive}');
                  }

                  return ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: cafeterias.length,
                    itemBuilder: (ctx, i) {
                      final cafeteria = cafeterias[i];
                      return GestureDetector(
                        onTap: () async {
                          // First select this cafeteria in the provider
                          // This ensures the data is loaded before navigation
                          await supabaseProvider.selectCafeteria(cafeteria.id);

                          // Then navigate to cafeteria detail
                          if (context.mounted) {
                            Navigator.of(context).pushNamed(
                              '/cafeteria',
                              arguments: {
                                'id': cafeteria.id,
                                'cafeteria': cafeteria,
                              },
                            );
                          }
                        },
                        child: SizedBox(
                          width:
                              250, // Fixed width that matches the card's internal width
                          child: CafeteriaCard(
                            cafeteria: cafeteria,
                          ),
                        ),
                      );
                    },
                  );
                },
              ),
            ),
            const SizedBox(height: 24),

            // Popular items section
            _buildSectionHeader(
              context,
              title: 'Popular Items',
              onSeeAll: () {
                Navigator.of(context).pushNamed('/popular_items');
              },
            ),
            const SizedBox(height: 12),
            Consumer<SupabaseProvider>(
              builder: (context, supabaseProvider, _) {
                if (topPopularItems.isEmpty) {
                  return const Center(
                    child: Padding(
                      padding: EdgeInsets.all(16.0),
                      child: Text('No menu items available'),
                    ),
                  );
                }

                return ListView.builder(
                  physics: const NeverScrollableScrollPhysics(),
                  shrinkWrap: true,
                  itemCount: topPopularItems.length,
                  itemBuilder: (ctx, i) {
                    return _buildPopularItemCard(context, topPopularItems[i]);
                  },
                );
              },
            ),

            // Current Orders Section
            _buildCurrentOrdersSection(context),
            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(
    BuildContext context, {
    required String title,
    required VoidCallback onSeeAll,
  }) {
    final theme = Theme.of(context);

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          title,
          style: theme.textTheme.displaySmall,
        ),
        TextButton(
          onPressed: onSeeAll,
          child: const Text('See All'),
        ),
      ],
    );
  }

  Widget _buildPopularItemCard(BuildContext context, SupabaseMenuItem item) {
    final theme = Theme.of(context);
    return InkWell(
      onTap: () {
        Navigator.of(context).pushNamed(
          '/item_details',
          arguments: item,
        );
      },
      child: Card(
        margin: const EdgeInsets.only(bottom: 12),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Item image
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: item.imageUrl != null &&
                        (item.imageUrl!.startsWith('http') ||
                            item.imageUrl!.startsWith('https'))
                    ? Image.network(
                        item.imageUrl!,
                        width: 80,
                        height: 80,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            width: 80,
                            height: 80,
                            color: theme.colorScheme.surface,
                            child: Icon(Icons.fastfood,
                                color: theme.textTheme.bodySmall?.color),
                          );
                        },
                      )
                    : Container(
                        width: 80,
                        height: 80,
                        color: theme.colorScheme.surface,
                        child: Icon(Icons.fastfood,
                            color: theme.textTheme.bodySmall?.color),
                      ),
              ),
              const SizedBox(width: 12),
              // Item details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      item.name,
                      style: theme.textTheme.titleMedium,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      item.description ?? 'No description available',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.textTheme.bodySmall?.color,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          '${item.price.toStringAsFixed(2)} EGP',
                          style: theme.textTheme.titleMedium?.copyWith(
                            color: theme.primaryColor,
                          ),
                        ),
                        // We don't have ratings in Supabase yet, so we'll just show availability
                        Row(
                          children: [
                            Icon(
                              item.isAvailable
                                  ? Icons.check_circle
                                  : Icons.cancel,
                              size: 16,
                              color:
                                  item.isAvailable ? Colors.green : Colors.red,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              item.isAvailable ? 'Available' : 'Unavailable',
                              style: theme.textTheme.bodyMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCurrentOrdersSection(BuildContext context) {
    final theme = Theme.of(context);
    final orders = Provider.of<OrderProvider>(context).orders;
    final currentOrders = orders
        .where(
            (order) => order.status != 'Completed' && order.status != 'Missed')
        .toList();

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      margin: const EdgeInsets.only(bottom: 24),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.local_shipping, color: theme.primaryColor),
                const SizedBox(width: 8),
                Text('Current Orders',
                    style: theme.textTheme.titleMedium
                        ?.copyWith(fontWeight: FontWeight.bold)),
              ],
            ),
            const SizedBox(height: 12),
            if (currentOrders.isEmpty)
              Text(
                  'No current orders. When you place an order, it will appear here!',
                  style: theme.textTheme.bodyMedium)
            else
              ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: currentOrders.length,
                separatorBuilder: (_, __) => const Divider(),
                itemBuilder: (context, index) {
                  final order = currentOrders[index];
                  return ListTile(
                    contentPadding: EdgeInsets.zero,
                    leading: Icon(Icons.receipt, color: theme.primaryColor),
                    title: Text('Order #${order.orderNumber}'),
                    subtitle:
                        Text('Pickup: ${order.pickupTime}  •  ${order.status}'),
                    trailing: const Icon(Icons.chevron_right),
                    onTap: () {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (_) => OrderTrackingScreen(order: order),
                        ),
                      );
                    },
                  );
                },
              ),
          ],
        ),
      ),
    );
  }
}
