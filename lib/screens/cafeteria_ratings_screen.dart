import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:unieatsappv0/models/cafeteria_rating.dart';
import 'package:unieatsappv0/providers/cafeteria_ratings_provider.dart';
import 'package:unieatsappv0/theme/app_theme.dart';
import 'package:intl/intl.dart';

class CafeteriaRatingsScreen extends StatelessWidget {
  final String cafeteriaId;
  final String cafeteriaName;

  const CafeteriaRatingsScreen({
    super.key,
    required this.cafeteriaId,
    required this.cafeteriaName,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final ratingsProvider = Provider.of<CafeteriaRatingsProvider>(context);
    final ratings = ratingsProvider.getRatingsForCafeteria(cafeteriaId);
    final averageRating =
        ratingsProvider.getAverageRatingForCafeteria(cafeteriaId);

    return Scaffold(
      appBar: AppBar(
        title: Text('$cafeteriaName Ratings'),
      ),
      body: Column(
        children: [
          // Rating summary
          Container(
            padding: const EdgeInsets.all(16),
            color: theme.colorScheme.surface,
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      averageRating.toStringAsFixed(1),
                      style: theme.textTheme.headlineLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: List.generate(5, (index) {
                            return Icon(
                              index < averageRating.floor()
                                  ? Icons.star
                                  : (index < averageRating
                                      ? Icons.star_half
                                      : Icons.star_border),
                              color: Colors.amber,
                              size: 24,
                            );
                          }),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '${ratings.length} ${ratings.length == 1 ? 'rating' : 'ratings'}',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: AppTheme.textSecondaryColor,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                const Divider(),
              ],
            ),
          ),

          // Ratings list
          Expanded(
            child: ratings.isEmpty
                ? Center(
                    child: Text(
                      'No ratings yet',
                      style: theme.textTheme.bodyLarge?.copyWith(
                        color: AppTheme.textSecondaryColor,
                      ),
                    ),
                  )
                : ListView.builder(
                    itemCount: ratings.length,
                    itemBuilder: (context, index) {
                      final rating = ratings[index];
                      return _buildRatingItem(context, rating);
                    },
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildRatingItem(BuildContext context, CafeteriaRating rating) {
    final theme = Theme.of(context);
    final dateFormat = DateFormat('MMM d, yyyy');
    final formattedDate = dateFormat.format(rating.date);

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: List.generate(5, (index) {
                    return Icon(
                      index < rating.rating ? Icons.star : Icons.star_border,
                      color: Colors.amber,
                      size: 20,
                    );
                  }),
                ),
                Text(
                  formattedDate,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
              ],
            ),
            if (rating.comment != null && rating.comment!.isNotEmpty) ...[
              const SizedBox(height: 12),
              Text(
                rating.comment!,
                style: theme.textTheme.bodyMedium,
              ),
            ],
            const SizedBox(height: 8),
            Row(
              children: [
                const Icon(
                  Icons.person,
                  size: 14,
                  color: AppTheme.textSecondaryColor,
                ),
                const SizedBox(width: 4),
                Text(
                  rating.username ?? 'Anonymous User',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: AppTheme.textSecondaryColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
