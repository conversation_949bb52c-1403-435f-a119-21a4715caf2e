// lib/screens/profile_screen.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:unieatsappv0/providers/auth_provider.dart';
import 'package:unieatsappv0/providers/simple_auth_provider.dart';
import 'package:unieatsappv0/widgets/profile_picture_picker.dart';

// Add a simple theme notifier for demonstration
class ThemeNotifier extends InheritedWidget {
  final ValueNotifier<ThemeMode> themeModeNotifier;
  const ThemeNotifier(
      {required this.themeModeNotifier, required super.child, super.key});
  static ThemeNotifier? of(BuildContext context) =>
      context.dependOnInheritedWidgetOfExactType<ThemeNotifier>();
  @override
  bool updateShouldNotify(ThemeNotifier oldWidget) =>
      themeModeNotifier != oldWidget.themeModeNotifier;
}

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  // No need for UserService anymore, we'll use SimpleAuthProvider
  Map<String, dynamic> _profile = {};

  @override
  void initState() {
    super.initState();
    _loadProfile();
  }

  Future<void> _loadProfile() async {
    // Get profile from SimpleAuthProvider
    final authProvider =
        Provider.of<SimpleAuthProvider>(context, listen: false);
    final user = authProvider.currentUser;

    if (user != null) {
      debugPrint(
          'Profile Screen: Loading profile for user: ${user.fullName}, email: ${user.email}');
      setState(() {
        _profile = {
          'name': user.fullName,
          'email': user.email,
          'phone': user.phone,
          'role': user.roleId,
          'notification_enabled': user.notificationEnabled,
        };
      });
    } else {
      debugPrint('Profile Screen: No user found in SimpleAuthProvider');
    }
  }

  @override
  Widget build(BuildContext context) {
    final oldAuthProvider = Provider.of<AuthProvider>(context);
    final simpleAuthProvider = Provider.of<SimpleAuthProvider>(context);

    return Scaffold(
      body: SafeArea(
        child: ListView(
          padding: const EdgeInsets.all(16.0),
          children: [
            // Profile header
            Center(
              child: Column(
                children: [
                  ProfilePicturePicker(
                    size: 100,
                    onImageSelected: (path) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                            content: Text('Profile picture updated')),
                      );
                    },
                  ),
                  const SizedBox(height: 16),
                  Text(
                    _profile['name']?.toString() ??
                        (simpleAuthProvider.currentUser?.fullName ??
                            oldAuthProvider.currentUser?.name ??
                            'Guest'),
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    _profile['email']?.toString() ??
                        (simpleAuthProvider.currentUser?.email ??
                            oldAuthProvider.currentUser?.email ??
                            ''),
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      fontSize: 16,
                      color: Colors.grey,
                    ),
                  ),
                  if (_profile['phone']?.toString().isNotEmpty == true ||
                      simpleAuthProvider.currentUser?.phone?.isNotEmpty == true)
                    Text(
                      _profile['phone']?.toString() ??
                          (simpleAuthProvider.currentUser?.phone ?? ''),
                      textAlign: TextAlign.center,
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.grey,
                      ),
                    ),
                ],
              ),
            ),
            const SizedBox(height: 32),

            // Profile menu items
            _buildMenuItem(
              context,
              icon: Icons.person,
              title: 'Account Settings',
              onTap: () async {
                await Navigator.pushNamed(context, '/profile/account-settings');
                _loadProfile(); // Refresh profile data after returning
              },
            ),
            _buildMenuItem(
              context,
              icon: Icons.history,
              title: 'Order History',
              onTap: () => Navigator.pushNamed(context, '/orders'),
            ),
            _buildMenuItem(
              context,
              icon: Icons.favorite,
              title: 'Favorites',
              onTap: () => Navigator.pushNamed(context, '/favorites'),
            ),
            _buildMenuItem(
              context,
              icon: Icons.help,
              title: 'Help',
              onTap: () => Navigator.pushNamed(context, '/profile/help'),
            ),
            _buildMenuItem(
              context,
              icon: Icons.message,
              title: 'Talk to Us',
              onTap: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const MiniChatScreen(),
                  ),
                );
              },
            ),
            _buildMenuItem(
              context,
              icon: Icons.info,
              title: 'About Us',
              onTap: () => Navigator.pushNamed(context, '/profile/about-us'),
            ),
            _buildMenuItem(
              context,
              icon: Icons.cloud,
              title: 'Supabase Test',
              onTap: () => Navigator.pushNamed(context, '/supabase_test'),
            ),
            _buildMenuItem(
              context,
              icon: Icons.settings,
              title: 'Supabase Configuration',
              onTap: () => Navigator.pushNamed(context, '/supabase_config'),
            ),
            _buildMenuItem(
              context,
              icon: Icons.sync,
              title: 'Data Migration',
              onTap: () => Navigator.pushNamed(context, '/migration'),
            ),

            const SizedBox(height: 16),

            // Logout button
            ElevatedButton(
              onPressed: () {
                // Logout synchronously first
                simpleAuthProvider.logout();
                oldAuthProvider.logout();

                // Then navigate
                Navigator.pushReplacementNamed(context, '/login');
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
              child: const Text('Logout'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMenuItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Icon(icon),
      title: Text(title),
      trailing: const Icon(Icons.chevron_right),
      onTap: onTap,
    );
  }
}

class MiniChatScreen extends StatefulWidget {
  const MiniChatScreen({super.key});
  @override
  State<MiniChatScreen> createState() => _MiniChatScreenState();
}

class _MiniChatScreenState extends State<MiniChatScreen> {
  final List<String> _messages = [
    'Hi! How can we help you today?',
  ];
  final TextEditingController _controller = TextEditingController();

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _sendMessage() {
    final text = _controller.text.trim();
    if (text.isNotEmpty) {
      setState(() {
        _messages.add(text);
        _controller.clear();
      });
      // Simulate a bot reply
      Future.delayed(const Duration(milliseconds: 800), () {
        if (mounted) {
          setState(() {
            _messages.add(
                'Thank you for reaching out! We will get back to you soon.');
          });
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      appBar: AppBar(title: const Text('Talk to Us')),
      body: Column(
        children: [
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: _messages.length,
              itemBuilder: (context, index) {
                final isUser = index % 2 == 1;
                return Align(
                  alignment:
                      isUser ? Alignment.centerRight : Alignment.centerLeft,
                  child: Container(
                    margin: const EdgeInsets.symmetric(vertical: 4),
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 10),
                    decoration: BoxDecoration(
                      color: isUser
                          ? theme.primaryColor.withAlpha(50)
                          : theme.colorScheme.surface,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(_messages[index]),
                  ),
                );
              },
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _controller,
                    decoration: const InputDecoration(
                      hintText: 'Type a message...',
                      border: OutlineInputBorder(),
                    ),
                    onSubmitted: (_) => _sendMessage(),
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.send),
                  onPressed: _sendMessage,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
