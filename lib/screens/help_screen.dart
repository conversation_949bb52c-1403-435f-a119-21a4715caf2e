import 'package:flutter/material.dart';
import 'package:unieatsappv0/screens/support_chat_screen.dart';

class HelpScreen extends StatelessWidget {
  const HelpScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Help & Support')),
      body: ListView(
        padding: const EdgeInsets.all(24),
        children: [
          const Text(
            'Get help and support for your UniEats experience',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          const Text(
            'Choose from the options below to get the assistance you need.',
            style: TextStyle(fontSize: 16, color: Colors.grey),
          ),
          const SizedBox(height: 32),

          // Live Chat Support
          Card(
            elevation: 2,
            child: ListTile(
              leading: const CircleAvatar(
                backgroundColor: Colors.blue,
                child: Icon(Icons.chat, color: Colors.white),
              ),
              title: const Text(
                'Live Chat Support',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              subtitle: const Text('Chat with our support team in real-time'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const SupportChatScreen(),
                  ),
                );
              },
            ),
          ),
          const SizedBox(height: 16),

          // FAQs
          Card(
            elevation: 2,
            child: ListTile(
              leading: const CircleAvatar(
                backgroundColor: Colors.green,
                child: Icon(Icons.help_outline, color: Colors.white),
              ),
              title: const Text(
                'FAQs',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              subtitle: const Text('Find answers to frequently asked questions'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () {
                _showFAQs(context);
              },
            ),
          ),
          const SizedBox(height: 16),

          // Email Support
          Card(
            elevation: 2,
            child: ListTile(
              leading: const CircleAvatar(
                backgroundColor: Colors.orange,
                child: Icon(Icons.email, color: Colors.white),
              ),
              title: const Text(
                'Email Support',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              subtitle: const Text('<EMAIL>'),
              trailing: const Icon(Icons.copy),
              onTap: () {
                _copyToClipboard(context, '<EMAIL>');
              },
            ),
          ),
          const SizedBox(height: 16),

          // Phone Support
          Card(
            elevation: 2,
            child: ListTile(
              leading: const CircleAvatar(
                backgroundColor: Colors.red,
                child: Icon(Icons.phone, color: Colors.white),
              ),
              title: const Text(
                'Call Us',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              subtitle: const Text('(+20)-************'),
              trailing: const Icon(Icons.copy),
              onTap: () {
                _copyToClipboard(context, '(+20)-************');
              },
            ),
          ),
          const SizedBox(height: 16),

          // Database Check (for testing)
          Card(
            elevation: 2,
            child: ListTile(
              leading: const CircleAvatar(
                backgroundColor: Colors.purple,
                child: Icon(Icons.storage, color: Colors.white),
              ),
              title: const Text(
                'Database Check',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              subtitle: const Text('Check database connection and tables'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () {
                Navigator.pushNamed(context, '/database-check');
              },
            ),
          ),
          const SizedBox(height: 16),

          // Search Test (for testing)
          Card(
            elevation: 2,
            child: ListTile(
              leading: const CircleAvatar(
                backgroundColor: Colors.teal,
                child: Icon(Icons.search, color: Colors.white),
              ),
              title: const Text(
                'Search Test',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              subtitle: const Text('Test search functionality and navigation'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () {
                Navigator.pushNamed(context, '/search-test');
              },
            ),
          ),
          const SizedBox(height: 16),

          // Database Compatibility Check
          Card(
            elevation: 2,
            child: ListTile(
              leading: const CircleAvatar(
                backgroundColor: Colors.orange,
                child: Icon(Icons.verified, color: Colors.white),
              ),
              title: const Text(
                'Database Compatibility',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              subtitle: const Text('Verify schema compatibility with new components'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () {
                Navigator.pushNamed(context, '/database-compatibility');
              },
            ),
          ),
          const SizedBox(height: 16),

          // Rating System Test
          Card(
            elevation: 2,
            child: ListTile(
              leading: const CircleAvatar(
                backgroundColor: Colors.amber,
                child: Icon(Icons.star, color: Colors.white),
              ),
              title: const Text(
                'Rating System Test',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              subtitle: const Text('Test menu item rating functionality'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () {
                Navigator.pushNamed(context, '/rating-test');
              },
            ),
          ),
          const SizedBox(height: 16),

          // Favorites Test
          Card(
            elevation: 2,
            child: ListTile(
              leading: const CircleAvatar(
                backgroundColor: Colors.pink,
                child: Icon(Icons.favorite, color: Colors.white),
              ),
              title: const Text(
                'Favorites Test',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              subtitle: const Text('Test favorites functionality with Supabase'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () {
                Navigator.pushNamed(context, '/favorites-test');
              },
            ),
          ),
          const SizedBox(height: 16),

          // Order Sync Test
          Card(
            elevation: 2,
            child: ListTile(
              leading: const CircleAvatar(
                backgroundColor: Colors.indigo,
                child: Icon(Icons.sync, color: Colors.white),
              ),
              title: const Text(
                'Order Sync Test',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              subtitle: const Text('Test real-time order updates and notifications'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () {
                Navigator.pushNamed(context, '/order-sync-test');
              },
            ),
          ),
          const SizedBox(height: 32),

          // Support Hours
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Support Hours',
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                ),
                SizedBox(height: 8),
                Text('Monday - Friday: 8:00 AM - 8:00 PM'),
                Text('Saturday: 9:00 AM - 6:00 PM'),
                Text('Sunday: 10:00 AM - 4:00 PM'),
                SizedBox(height: 8),
                Text(
                  'Live chat is available 24/7 for urgent issues',
                  style: TextStyle(fontStyle: FontStyle.italic, color: Colors.blue),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _copyToClipboard(BuildContext context, String text) {
    // In a real app, you'd use the clipboard package
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('$text copied to clipboard'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _showFAQs(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Frequently Asked Questions'),
        content: SizedBox(
          width: 400,
          height: 400,
          child: ListView(
            children: const [
              ExpansionTile(
                title: Text('How do I place an order?'),
                children: [
                  Padding(
                    padding: EdgeInsets.all(16),
                    child: Text('Browse cafeterias, select items, add to cart, and checkout.'),
                  ),
                ],
              ),
              ExpansionTile(
                title: Text('How do I track my order?'),
                children: [
                  Padding(
                    padding: EdgeInsets.all(16),
                    child: Text('Go to Order History to see real-time status updates.'),
                  ),
                ],
              ),
              ExpansionTile(
                title: Text('Can I cancel my order?'),
                children: [
                  Padding(
                    padding: EdgeInsets.all(16),
                    child: Text('Orders can be cancelled within 5 minutes of placement.'),
                  ),
                ],
              ),
              ExpansionTile(
                title: Text('What payment methods are accepted?'),
                children: [
                  Padding(
                    padding: EdgeInsets.all(16),
                    child: Text('We accept cash on pickup and mobile payments.'),
                  ),
                ],
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}