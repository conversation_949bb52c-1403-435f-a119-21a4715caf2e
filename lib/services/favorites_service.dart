import 'package:flutter/foundation.dart';
import 'package:unieatsappv0/services/supabase_service_new.dart';
import 'package:uuid/uuid.dart';

class FavoritesService {
  static final FavoritesService _instance = FavoritesService._internal();
  factory FavoritesService() => _instance;
  FavoritesService._internal();

  final SupabaseService _supabaseService = SupabaseService();

  // Table name
  static const String _favoritesTable = 'favorites';

  /// Add a menu item to favorites
  Future<bool> addToFavorites(String menuItemId) async {
    try {
      final user = _supabaseService.currentUser;
      if (user == null) {
        debugPrint('Cannot add to favorites: User not logged in');
        return false;
      }

      // Check if already a favorite to prevent duplicates
      final isAlreadyFavorite = await isFavorite(menuItemId);
      if (isAlreadyFavorite) {
        debugPrint('Menu item $menuItemId is already in favorites');
        return true; // Return true since it's already favorited
      }

      final favoriteId = const Uuid().v4();
      final now = DateTime.now();

      final favoriteData = {
        'id': favoriteId,
        'user_id': user.id,
        'menu_item_id': menuItemId, // Using menu_item_id to match the schema
        'created_at': now.toIso8601String(),
      };

      await _supabaseService.client
          .from(_favoritesTable)
          .insert(favoriteData);

      debugPrint('Added menu item $menuItemId to favorites');
      return true;
    } catch (e) {
      debugPrint('Error adding to favorites: $e');
      return false;
    }
  }

  /// Remove a menu item from favorites
  Future<bool> removeFromFavorites(String menuItemId) async {
    try {
      final user = _supabaseService.currentUser;
      if (user == null) {
        debugPrint('Cannot remove from favorites: User not logged in');
        return false;
      }

      await _supabaseService.client
          .from(_favoritesTable)
          .delete()
          .eq('user_id', user.id)
          .eq('menu_item_id', menuItemId);

      debugPrint('Removed menu item $menuItemId from favorites');
      return true;
    } catch (e) {
      debugPrint('Error removing from favorites: $e');
      return false;
    }
  }

  /// Check if a menu item is in favorites
  Future<bool> isFavorite(String menuItemId) async {
    try {
      final user = _supabaseService.currentUser;
      if (user == null) {
        return false;
      }

      final response = await _supabaseService.client
          .from(_favoritesTable)
          .select('id')
          .eq('user_id', user.id)
          .eq('menu_item_id', menuItemId)
          .maybeSingle();

      return response != null;
    } catch (e) {
      debugPrint('Error checking if favorite: $e');
      return false;
    }
  }

  /// Get all favorite menu item IDs for the current user
  Future<List<String>> getUserFavoriteIds() async {
    try {
      final user = _supabaseService.currentUser;
      if (user == null) {
        debugPrint('Cannot get favorites: User not logged in');
        return [];
      }

      final response = await _supabaseService.client
          .from(_favoritesTable)
          .select('menu_item_id')
          .eq('user_id', user.id)
          .order('created_at', ascending: false);

      return response.map((item) => item['menu_item_id'] as String).toList();
    } catch (e) {
      debugPrint('Error getting user favorites: $e');
      return [];
    }
  }

  /// Get all favorite menu items with details for the current user
  Future<List<Map<String, dynamic>>> getUserFavoritesWithDetails() async {
    try {
      final user = _supabaseService.currentUser;
      if (user == null) {
        debugPrint('Cannot get favorites: User not logged in');
        return [];
      }

      // Join favorites with menu_items to get full details
      final response = await _supabaseService.client
          .from(_favoritesTable)
          .select('''
            id,
            created_at,
            menu_items!inner(
              id,
              name,
              description,
              price,
              image_url,
              category,
              is_available,
              cafeteria_id,
              cafeterias!inner(
                id,
                name,
                location
              )
            )
          ''')
          .eq('user_id', user.id)
          .order('created_at', ascending: false);

      return response;
    } catch (e) {
      debugPrint('Error getting user favorites with details: $e');
      return [];
    }
  }

  /// Toggle favorite status for a menu item
  Future<bool> toggleFavorite(String menuItemId) async {
    try {
      final isFav = await isFavorite(menuItemId);

      if (isFav) {
        return await removeFromFavorites(menuItemId);
      } else {
        return await addToFavorites(menuItemId);
      }
    } catch (e) {
      debugPrint('Error toggling favorite: $e');
      return false;
    }
  }

  /// Get favorite count for a menu item (how many users favorited it)
  Future<int> getFavoriteCount(String menuItemId) async {
    try {
      final response = await _supabaseService.client
          .from(_favoritesTable)
          .select('id')
          .eq('menu_item_id', menuItemId);

      return response.length;
    } catch (e) {
      debugPrint('Error getting favorite count: $e');
      return 0;
    }
  }

  /// Get most favorited menu items
  Future<List<Map<String, dynamic>>> getMostFavoritedItems({int limit = 10}) async {
    try {
      // This is a complex query that would be better handled with a database function
      // For now, we'll get all favorites and count them in the app
      final response = await _supabaseService.client
          .from(_favoritesTable)
          .select('''
            menu_item_id,
            menu_items!inner(
              id,
              name,
              description,
              price,
              image_url,
              category,
              is_available,
              cafeteria_id,
              cafeterias!inner(
                id,
                name,
                location
              )
            )
          ''');

      // Group by item_id and count
      final Map<String, dynamic> itemCounts = {};
      final Map<String, dynamic> itemDetails = {};

      for (final item in response) {
        final itemId = item['menu_item_id'] as String;
        itemCounts[itemId] = (itemCounts[itemId] ?? 0) + 1;
        itemDetails[itemId] = item['menu_items'];
      }

      // Sort by count and take top items
      final sortedItems = itemCounts.entries.toList()
        ..sort((a, b) => b.value.compareTo(a.value));

      return sortedItems
          .take(limit)
          .map((entry) => {
                'item_id': entry.key,
                'favorite_count': entry.value,
                'menu_item': itemDetails[entry.key],
              })
          .toList();
    } catch (e) {
      debugPrint('Error getting most favorited items: $e');
      return [];
    }
  }

  /// Clear all favorites for the current user
  Future<bool> clearAllFavorites() async {
    try {
      final user = _supabaseService.currentUser;
      if (user == null) {
        debugPrint('Cannot clear favorites: User not logged in');
        return false;
      }

      await _supabaseService.client
          .from(_favoritesTable)
          .delete()
          .eq('user_id', user.id);

      debugPrint('Cleared all favorites for user');
      return true;
    } catch (e) {
      debugPrint('Error clearing favorites: $e');
      return false;
    }
  }

  /// Subscribe to real-time updates for user's favorites
  void subscribeToUserFavorites(String userId, Function(Map<String, dynamic>) onFavoriteChange) {
    // TODO: Implement real-time subscriptions when Supabase version is compatible
    debugPrint('Real-time subscription for user favorites $userId - feature coming soon');

    // For now, we'll use polling instead of real-time subscriptions
    // This can be implemented later when the Supabase version supports it properly
  }

  /// Unsubscribe from favorites updates
  void unsubscribeFromUserFavorites(String userId) {
    try {
      _supabaseService.client.removeChannel(
        _supabaseService.client.channel('user_favorites_$userId'),
      );
    } catch (e) {
      debugPrint('Error unsubscribing from favorites: $e');
    }
  }
}
