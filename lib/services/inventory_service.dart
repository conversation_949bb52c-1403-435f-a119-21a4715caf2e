import 'package:flutter/foundation.dart';
import 'supabase_service.dart';

class InventoryService {
  static final InventoryService _instance = InventoryService._internal();
  factory InventoryService() => _instance;
  InventoryService._internal();

  final SupabaseService _supabaseService = SupabaseService();
  static const String _inventoryTable = 'inventory_items';
  static const String _alertsTable = 'inventory_alerts';
  static const String _ingredientsTable = 'menu_item_ingredients';

  // Initialize the service
  Future<void> init() async {
    await _supabaseService.init();
  }

  // Check menu item availability based on inventory
  Future<Map<String, dynamic>> checkMenuItemAvailability(String menuItemId) async {
    try {
      await init();
      
      // Get menu item ingredients
      final response = await _supabaseService.client
          .from(_ingredientsTable)
          .select('''
            *,
            inventory_items(*)
          ''')
          .eq('menu_item_id', menuItemId);

      List<String> missingIngredients = [];
      List<String> lowStockIngredients = [];
      List<Map<String, dynamic>> details = [];
      bool available = true;

      for (var ingredient in response) {
        final inventoryItem = ingredient['inventory_items'];
        final needed = ingredient['quantity_needed'];
        final availableQty = inventoryItem['quantity'];
        final isOptional = ingredient['is_optional'] ?? false;

        details.add({
          'ingredient': inventoryItem['name'],
          'needed': needed,
          'available': availableQty,
          'unit': ingredient['unit'],
        });

        // Check if required ingredient is available
        if (!isOptional) {
          if (inventoryItem['status'] == 'out_of_stock' || availableQty < needed) {
            available = false;
            missingIngredients.add(inventoryItem['name']);
          } else if (inventoryItem['status'] == 'low_stock') {
            lowStockIngredients.add(inventoryItem['name']);
          }
        }
      }

      return {
        'available': available,
        'missingIngredients': missingIngredients,
        'lowStockIngredients': lowStockIngredients,
        'details': details,
      };
    } catch (e) {
      debugPrint('Error checking menu item availability: $e');
      return {
        'available': false,
        'missingIngredients': [],
        'lowStockIngredients': [],
        'details': [],
      };
    }
  }

  // Get inventory alerts for a cafeteria
  Future<List<Map<String, dynamic>>> getInventoryAlerts(String cafeteriaId) async {
    try {
      await init();
      
      final response = await _supabaseService.client
          .from(_alertsTable)
          .select('''
            *,
            inventory_items(*)
          ''')
          .eq('cafeteria_id', cafeteriaId)
          .eq('is_resolved', false)
          .order('created_at', ascending: false);

      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      debugPrint('Error getting inventory alerts: $e');
      return [];
    }
  }

  // Get inventory items for a cafeteria
  Future<List<Map<String, dynamic>>> getInventoryItems(String cafeteriaId) async {
    try {
      await init();
      
      final response = await _supabaseService.client
          .from(_inventoryTable)
          .select('*')
          .eq('cafeteria_id', cafeteriaId)
          .order('name');

      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      debugPrint('Error getting inventory items: $e');
      return [];
    }
  }

  // Get low stock items
  Future<List<Map<String, dynamic>>> getLowStockItems(String cafeteriaId) async {
    try {
      await init();
      
      final response = await _supabaseService.client
          .from(_inventoryTable)
          .select('*')
          .eq('cafeteria_id', cafeteriaId)
          .in_('status', ['low_stock', 'out_of_stock'])
          .order('status')
          .order('name');

      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      debugPrint('Error getting low stock items: $e');
      return [];
    }
  }

  // Update inventory quantity (for restocking)
  Future<bool> updateInventoryQuantity(String inventoryItemId, double newQuantity, {String? notes}) async {
    try {
      await init();
      
      await _supabaseService.client
          .from(_inventoryTable)
          .update({
            'quantity': newQuantity,
            'last_restocked': DateTime.now().toIso8601String(),
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', inventoryItemId);

      return true;
    } catch (e) {
      debugPrint('Error updating inventory quantity: $e');
      return false;
    }
  }

  // Deduct inventory for order (called when order is confirmed)
  Future<Map<String, dynamic>> deductInventoryForOrder(String orderId) async {
    try {
      await init();
      
      // Get order items with menu item ingredients
      final orderItemsResponse = await _supabaseService.client
          .from('order_items')
          .select('''
            *,
            menu_items(
              id,
              name,
              menu_item_ingredients(
                *,
                inventory_items(*)
              )
            )
          ''')
          .eq('order_id', orderId);

      List<String> affectedItems = [];
      bool success = true;
      String message = '';

      for (var orderItem in orderItemsResponse) {
        final menuItem = orderItem['menu_items'];
        final ingredients = menuItem['menu_item_ingredients'] ?? [];

        for (var ingredient in ingredients) {
          final inventoryItem = ingredient['inventory_items'];
          final totalNeeded = ingredient['quantity_needed'] * orderItem['quantity'];
          final currentQuantity = inventoryItem['quantity'];
          final newQuantity = (currentQuantity - totalNeeded).clamp(0.0, double.infinity);

          // Update inventory quantity
          final updateSuccess = await updateInventoryQuantity(
            inventoryItem['id'],
            newQuantity,
            notes: 'Deducted for order $orderId'
          );

          if (updateSuccess) {
            affectedItems.add('${inventoryItem['name']}: -$totalNeeded ${ingredient['unit']}');
          } else {
            success = false;
            message = 'Failed to update ${inventoryItem['name']}';
            break;
          }
        }

        if (!success) break;
      }

      return {
        'success': success,
        'message': success ? 'Successfully deducted inventory for order' : message,
        'affectedItems': affectedItems,
      };
    } catch (e) {
      debugPrint('Error deducting inventory for order: $e');
      return {
        'success': false,
        'message': 'Failed to deduct inventory for order',
        'affectedItems': [],
      };
    }
  }

  // Get inventory statistics
  Future<Map<String, dynamic>> getInventoryStats(String cafeteriaId) async {
    try {
      await init();
      
      final items = await getInventoryItems(cafeteriaId);
      final alerts = await getInventoryAlerts(cafeteriaId);

      final totalItems = items.length;
      final lowStockItems = items.where((item) => item['status'] == 'low_stock').length;
      final outOfStockItems = items.where((item) => item['status'] == 'out_of_stock').length;
      final inStockItems = items.where((item) => item['status'] == 'in_stock').length;

      double totalValue = 0;
      for (var item in items) {
        final quantity = item['quantity'] ?? 0;
        final costPerUnit = item['cost_per_unit'] ?? 0;
        totalValue += quantity * costPerUnit;
      }

      return {
        'totalItems': totalItems,
        'inStockItems': inStockItems,
        'lowStockItems': lowStockItems,
        'outOfStockItems': outOfStockItems,
        'totalValue': totalValue,
        'activeAlerts': alerts.length,
      };
    } catch (e) {
      debugPrint('Error getting inventory stats: $e');
      return {
        'totalItems': 0,
        'inStockItems': 0,
        'lowStockItems': 0,
        'outOfStockItems': 0,
        'totalValue': 0.0,
        'activeAlerts': 0,
      };
    }
  }

  // Resolve an inventory alert
  Future<bool> resolveAlert(String alertId) async {
    try {
      await init();
      
      await _supabaseService.client
          .from(_alertsTable)
          .update({
            'is_resolved': true,
            'resolved_at': DateTime.now().toIso8601String(),
          })
          .eq('id', alertId);

      return true;
    } catch (e) {
      debugPrint('Error resolving alert: $e');
      return false;
    }
  }

  // Subscribe to inventory changes (real-time)
  void subscribeToInventoryChanges(String cafeteriaId, Function(Map<String, dynamic>) onUpdate) {
    _supabaseService.client
        .channel('inventory_changes_$cafeteriaId')
        .on('postgres_changes', 
            filter: 'inventory_items:cafeteria_id=eq.$cafeteriaId',
            callback: (payload) {
              onUpdate(payload);
            })
        .subscribe();
  }

  // Subscribe to inventory alerts (real-time)
  void subscribeToInventoryAlerts(String cafeteriaId, Function(Map<String, dynamic>) onAlert) {
    _supabaseService.client
        .channel('inventory_alerts_$cafeteriaId')
        .on('postgres_changes',
            filter: 'inventory_alerts:cafeteria_id=eq.$cafeteriaId',
            callback: (payload) {
              onAlert(payload);
            })
        .subscribe();
  }

  // Check if menu items need availability updates
  Future<List<String>> getMenuItemsNeedingUpdate(String cafeteriaId) async {
    try {
      await init();
      
      // Get all menu items for the cafeteria
      final menuItemsResponse = await _supabaseService.client
          .from('menu_items')
          .select('id, name, is_available')
          .eq('cafeteria_id', cafeteriaId);

      List<String> needingUpdate = [];

      for (var menuItem in menuItemsResponse) {
        final availability = await checkMenuItemAvailability(menuItem['id']);
        
        // If current availability doesn't match what it should be
        if (menuItem['is_available'] != availability['available']) {
          needingUpdate.add(menuItem['name']);
        }
      }

      return needingUpdate;
    } catch (e) {
      debugPrint('Error checking menu items needing update: $e');
      return [];
    }
  }
}
