import 'package:flutter/material.dart';
import 'package:unieatsappv0/config/supabase_config.dart';
import 'package:unieatsappv0/models/cafeteria_rating.dart';

/// Service class for handling ratings in the app
class RatingService {
  /// Submit a cafeteria rating
  static Future<void> submitCafeteriaRating({
    required String cafeteriaId,
    required double rating,
    String? comment,
    String? orderId,
    required BuildContext context,
  }) async {
    try {
      // Get current user
      final currentUser = SupabaseConfig.client.auth.currentUser;
      if (currentUser == null) {
        throw Exception('User not logged in');
      }

      // Insert rating into cafeteria_user_ratings table
      await SupabaseConfig.client.from('cafeteria_user_ratings').insert({
        'cafeteria_id': cafeteriaId,
        'user_id': currentUser.id,
        'rating': rating.round(), // Ensure rating is an integer between 1-5
        'comment': comment,
        'order_id': orderId,
        'created_at': DateTime.now().toIso8601String(),
      });

      // The trigger function will automatically update the cafeteria_ratings table

      debugPrint('Cafeteria rating submitted successfully');
    } catch (e) {
      debugPrint('Error submitting cafeteria rating: $e');
      // Show error message if context is still mounted
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error submitting rating: $e')),
        );
      }
      rethrow;
    }
  }

  /// Submit a menu item rating
  static Future<void> submitMenuItemRating({
    required String menuItemId,
    required double rating,
    String? comment,
    String? orderId,
    required BuildContext context,
  }) async {
    try {
      // Get current user
      final currentUser = SupabaseConfig.client.auth.currentUser;
      if (currentUser == null) {
        throw Exception('User not logged in');
      }

      // Insert rating into menu_item_ratings table
      await SupabaseConfig.client.from('menu_item_ratings').insert({
        'menu_item_id': menuItemId,
        'user_id': currentUser.id,
        'rating': rating.round(), // Ensure rating is an integer between 1-5
        'review_comment': comment,
        'order_id': orderId,
        'created_at': DateTime.now().toIso8601String(),
      });

      // The trigger function will automatically update the menu_items table

      debugPrint('Menu item rating submitted successfully');
    } catch (e) {
      debugPrint('Error submitting menu item rating: $e');
      // Show error message if context is still mounted
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error submitting rating: $e')),
        );
      }
      rethrow;
    }
  }

  /// Submit an order rating
  static Future<void> submitOrderRating({
    required String orderId,
    required int rating,
    String? comment,
    required BuildContext context,
  }) async {
    try {
      // Get current user
      final currentUser = SupabaseConfig.client.auth.currentUser;
      if (currentUser == null) {
        throw Exception('User not logged in');
      }

      // Update the order with the rating
      await SupabaseConfig.client
          .from('orders')
          .update({
            'rating': rating,
            'review_comment': comment,
          })
          .eq('id', orderId);

      debugPrint('Order rating submitted successfully');
    } catch (e) {
      debugPrint('Error submitting order rating: $e');
      // Show error message if context is still mounted
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error submitting rating: $e')),
        );
      }
      rethrow;
    }
  }

  /// Get cafeteria ratings
  static Future<List<CafeteriaRating>> getCafeteriaRatings(String cafeteriaId) async {
    try {
      // Get ratings from cafeteria_user_ratings table
      final response = await SupabaseConfig.client
          .from('cafeteria_user_ratings')
          .select('*, profiles:user_id(full_name)')
          .eq('cafeteria_id', cafeteriaId)
          .order('created_at', ascending: false);

      // Convert to CafeteriaRating objects
      return (response as List).map((item) {
        return CafeteriaRating(
          id: item['id'],
          cafeteriaId: item['cafeteria_id'],
          rating: (item['rating'] ?? 0).toDouble(),
          comment: item['comment'],
          date: DateTime.parse(item['created_at']),
          orderId: item['order_id'],
          userId: item['user_id'],
          username: item['profiles']['full_name'] ?? 'Anonymous User',
        );
      }).toList();
    } catch (e) {
      debugPrint('Error getting cafeteria ratings: $e');
      return [];
    }
  }

  /// Get cafeteria average rating
  static Future<Map<String, dynamic>> getCafeteriaAverageRating(String cafeteriaId) async {
    try {
      // Get average rating from cafeteria_ratings table
      final response = await SupabaseConfig.client
          .from('cafeteria_ratings')
          .select('*')
          .eq('cafeteria_id', cafeteriaId)
          .single();

      return {
        'overall_rating': (response['overall_rating'] ?? 0).toDouble(),
        'total_ratings': response['total_ratings'] ?? 0,
        'food_quality': (response['food_quality'] ?? 0).toDouble(),
        'service': (response['service'] ?? 0).toDouble(),
        'cleanliness': (response['cleanliness'] ?? 0).toDouble(),
        'value_for_money': (response['value_for_money'] ?? 0).toDouble(),
      };
    } catch (e) {
      debugPrint('Error getting cafeteria average rating: $e');
      return {
        'overall_rating': 0.0,
        'total_ratings': 0,
        'food_quality': 0.0,
        'service': 0.0,
        'cleanliness': 0.0,
        'value_for_money': 0.0,
      };
    }
  }

  /// Get menu item ratings
  static Future<List<Map<String, dynamic>>> getMenuItemRatings(String menuItemId) async {
    try {
      // Get ratings from menu_item_ratings table
      final response = await SupabaseConfig.client
          .from('menu_item_ratings')
          .select('*, profiles:user_id(full_name)')
          .eq('menu_item_id', menuItemId)
          .order('created_at', ascending: false);

      // Return as list of maps
      return (response as List).map((item) => {
        'id': item['id'],
        'menu_item_id': item['menu_item_id'],
        'rating': (item['rating'] ?? 0).toDouble(),
        'comment': item['review_comment'],
        'date': DateTime.parse(item['created_at']),
        'user_id': item['user_id'],
        'username': item['profiles']['full_name'] ?? 'Anonymous User',
      }).toList();
    } catch (e) {
      debugPrint('Error getting menu item ratings: $e');
      return [];
    }
  }
}
