import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:unieatsappv0/models/supabase_models.dart';

/// A simplified authentication service that handles both auth and profile creation
class SimpleAuthService {
  static final SimpleAuthService _instance = SimpleAuthService._internal();
  factory SimpleAuthService() => _instance;
  SimpleAuthService._internal();

  // Get the Supabase client
  SupabaseClient get _client => Supabase.instance.client;

  /// Register a new user with email and password
  Future<UserProfile?> register({
    required String email,
    required String password,
    required String fullName,
    String? phone,
  }) async {
    try {
      debugPrint(
          'SimpleAuthService: Registering user with email: $email, name: $fullName');

      // Validate phone number if provided (Egyptian phone number)
      if (phone != null && phone.isNotEmpty) {
        // Egyptian phone numbers are typically 11 digits starting with 01
        final egyptianPhoneRegex = RegExp(r'^01[0-2,5]{1}[0-9]{8}$');
        if (!egyptianPhoneRegex.hasMatch(phone)) {
          throw Exception(
              'Invalid Egyptian phone number. Must be 11 digits starting with 01');
        }
        debugPrint('SimpleAuthService: Valid Egyptian phone number: $phone');
      }

      // Step 1: Create the user in auth.users with metadata
      final authResponse = await _client.auth.signUp(
        email: email,
        password: password,
        data: {
          'full_name': fullName, // Include full_name in metadata
          'display_name': fullName, // Add display_name as well for redundancy
          if (phone != null && phone.isNotEmpty)
            'phone': phone, // Include phone if provided
        },
      );

      if (authResponse.user == null) {
        debugPrint(
            'SimpleAuthService: Failed to register user: No user returned');
        return null;
      }

      final userId = authResponse.user!.id;
      debugPrint(
          'SimpleAuthService: User registered successfully with ID: $userId');

      // Step 2: Set the student role name
      const String roleName = 'student';
      debugPrint('SimpleAuthService: Using role name: $roleName');

      // Step 3: Create a profile in the profiles table
      try {
        // First check if a profile already exists
        final existingProfile = await _client
            .from('profiles')
            .select()
            .eq('id', userId)
            .maybeSingle();

        if (existingProfile != null) {
          debugPrint(
              'SimpleAuthService: Profile already exists, skipping creation');
        } else {
          // Create a new profile with correct column names
          final profileData = {
            'id': userId, // Use 'id' not 'user_id'
            'full_name': fullName, // Use 'full_name' not 'user_full_name'
            'role': roleName, // Use 'role' not 'user_role_id'
            'notification_enabled': true, // Default to enabled
            'created_at': DateTime.now().toIso8601String(),
            if (phone != null && phone.isNotEmpty)
              'phone': phone, // Use 'phone' not 'user_phone'
          };

          debugPrint(
              'SimpleAuthService: Creating profile with data: $profileData');

          // Insert directly into profiles table
          await _client.from('profiles').insert(profileData);

          debugPrint('SimpleAuthService: Profile created successfully');
        }
      } catch (e) {
        debugPrint('SimpleAuthService: Error creating profile: $e');
        // Continue anyway - we'll return a profile object even if DB insert fails
      }

      // Return a user profile object
      return UserProfile(
        id: userId,
        email: email,
        fullName: fullName,
        roleId: roleName,
        createdAt: DateTime.now(),
      );
    } catch (e) {
      debugPrint('SimpleAuthService: Error registering user: $e');
      rethrow; // Rethrow to see the full error in the UI
    }
  }

  /// Sign in a user with email and password
  Future<UserProfile?> login({
    required String email,
    required String password,
  }) async {
    try {
      debugPrint('SimpleAuthService: Logging in user with email: $email');

      // Check if we're already logged in
      final currentUser = _client.auth.currentUser;
      User? authUser;

      if (currentUser != null && currentUser.email == email) {
        // We're already logged in with this email
        debugPrint('SimpleAuthService: Already logged in with this email');
        authUser = currentUser;
      } else if (password.isNotEmpty) {
        // Sign in with email and password
        final authResponse = await _client.auth.signInWithPassword(
          email: email,
          password: password,
        );
        authUser = authResponse.user;
      } else {
        // Empty password means we're checking an existing session
        debugPrint('SimpleAuthService: Checking existing session');
        return null;
      }

      if (authUser == null) {
        debugPrint('SimpleAuthService: Failed to login: No user returned');
        return null;
      }

      final userId = authUser.id;
      debugPrint(
          'SimpleAuthService: User logged in successfully with ID: $userId');

      // Get the user profile
      try {
        final profileResponse = await _client
            .from('profiles')
            .select()
            .eq('id', userId)
            .maybeSingle();

        if (profileResponse != null) {
          debugPrint(
              'SimpleAuthService: Got profile: ${profileResponse.toString()}');

          return UserProfile(
            id: userId,
            email: email,
            fullName: profileResponse['full_name'],
            roleId: profileResponse['role'],
            theme: profileResponse['theme'],
            notificationEnabled: profileResponse['notification_enabled'],
            createdAt: profileResponse['created_at'] != null
                ? DateTime.parse(profileResponse['created_at'])
                : null,
            updatedAt: profileResponse['updated_at'] != null
                ? DateTime.parse(profileResponse['updated_at'])
                : null,
          );
        } else {
          debugPrint('SimpleAuthService: No profile found, creating one');

          // Set the student role name
          const String roleName = 'student';

          // Try to get the full name from user metadata
          String fullName = email.split('@')[0]; // Default to email username

          try {
            final userData = authUser.userMetadata;
            if (userData != null && userData['full_name'] != null) {
              fullName = userData['full_name'] as String;
              debugPrint(
                  'SimpleAuthService: Got full name from metadata: $fullName');
            } else if (userData != null && userData['display_name'] != null) {
              fullName = userData['display_name'] as String;
              debugPrint(
                  'SimpleAuthService: Got display name from metadata: $fullName');
            } else {
              debugPrint(
                  'SimpleAuthService: No name in metadata, using email username');
            }
          } catch (e) {
            debugPrint('SimpleAuthService: Error getting user metadata: $e');
          }

          // Create a profile with correct column names
          final profileData = {
            'id': userId, // Use 'id' not 'user_id'
            'full_name': fullName, // Use 'full_name' not 'user_full_name'
            'role': roleName, // Use 'role' not 'user_role_id'
            'notification_enabled': true, // Default to enabled
            'created_at': DateTime.now().toIso8601String(),
          };

          try {
            await _client.from('profiles').insert(profileData);
            debugPrint('SimpleAuthService: Profile created successfully during login');
          } catch (e) {
            debugPrint(
                'SimpleAuthService: Error creating profile during login: $e');
          }

          // Return a basic profile
          return UserProfile(
            id: userId,
            email: email,
            fullName: profileData['full_name'] as String?,
            roleId: roleName,
            createdAt: DateTime.now(),
          );
        }
      } catch (e) {
        debugPrint('SimpleAuthService: Error getting profile: $e');

        // Return a basic profile if we can't get it from the database
        return UserProfile(
          id: userId,
          email: email,
          createdAt: DateTime.now(),
        );
      }
    } catch (e) {
      debugPrint('SimpleAuthService: Error logging in: $e');
      return null;
    }
  }

  /// Sign out the current user
  Future<void> logout() async {
    try {
      await _client.auth.signOut();
      debugPrint('SimpleAuthService: User signed out');
    } catch (e) {
      debugPrint('SimpleAuthService: Error signing out: $e');
    }
  }

  /// Update a user profile
  Future<UserProfile?> updateProfile({
    required String userId,
    String? fullName,
    String? email,
    String? phone,
  }) async {
    try {
      debugPrint('SimpleAuthService: Updating profile for user: $userId');

      // Get the current profile
      final profileResponse = await _client
          .from('profiles')
          .select()
          .eq('id', userId)
          .maybeSingle();

      if (profileResponse == null) {
        debugPrint('SimpleAuthService: No profile found for user: $userId');
        return null;
      }

      // Update the profile
      final updates = <String, dynamic>{};

      if (fullName != null && fullName.isNotEmpty) {
        updates['full_name'] = fullName;
      }

      if (phone != null && phone.isNotEmpty) {
        updates['phone'] = phone;
      }

      if (updates.isNotEmpty) {
        await _client.from('profiles').update(updates).eq('id', userId);

        debugPrint('SimpleAuthService: Profile updated successfully');

        // Get the updated profile
        final updatedProfile =
            await _client.from('profiles').select().eq('id', userId).single();

        return UserProfile(
          id: userId,
          email: email ?? profileResponse['email'],
          fullName: updatedProfile['full_name'],
          phone: updatedProfile['phone'], // Include phone number
          roleId: updatedProfile['role'],
          theme: updatedProfile['theme'],
          notificationEnabled: updatedProfile['notification_enabled'],
          createdAt: updatedProfile['created_at'] != null
              ? DateTime.parse(updatedProfile['created_at'])
              : null,
          updatedAt: DateTime.now(),
        );
      }

      return null;
    } catch (e) {
      debugPrint('SimpleAuthService: Error updating profile: $e');
      return null;
    }
  }

  /// Get the current user
  User? get currentUser => _client.auth.currentUser;

  /// Check if a user is signed in
  bool get isAuthenticated => _client.auth.currentUser != null;
}
