/// Base model class for Supabase entities
abstract class SupabaseModel {
  Map<String, dynamic> toJson();

  // Factory method to be implemented by subclasses
  static SupabaseModel from<PERSON>son(Map<String, dynamic> json) {
    throw UnimplementedError('Subclasses must implement fromJson');
  }
}

/// User profile model
class UserProfile extends SupabaseModel {
  final String id;
  final String? email;
  final String? fullName;
  final String? avatarUrl;
  final String? roleId; // This is the UUID of the role
  final String? phone; // Added phone field
  final String? theme;
  final bool? notificationEnabled;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  UserProfile({
    required this.id,
    this.email,
    this.fullName,
    this.avatarUrl,
    this.roleId,
    this.phone,
    this.theme,
    this.notificationEnabled,
    this.createdAt,
    this.updatedAt,
  });

  factory UserProfile.fromJson(Map<String, dynamic> json) {
    return UserProfile(
      id: json['id'],
      email: json['email'],
      fullName: json['full_name'],
      avatarUrl: json['avatar_url'],
      roleId: json['role'],
      phone: json['phone'],
      theme: json['theme'],
      notificationEnabled: json['notification_enabled'],
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'])
          : null,
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'])
          : null,
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'full_name': fullName,
      'avatar_url': avatarUrl,
      'role': roleId,
      'phone': phone,
      'theme': theme,
      'notification_enabled': notificationEnabled,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }
}

// Add more model classes based on your Supabase tables
// For example:

/// Cafeteria model for Supabase
class SupabaseCafeteria extends SupabaseModel {
  final String id;
  final String name;
  final String? description;
  final String? imageUrl;
  final String? location;
  final double? rating;
  final DateTime? createdAt;
  final bool isActive;

  SupabaseCafeteria({
    required this.id,
    required this.name,
    this.description,
    this.imageUrl,
    this.location,
    this.rating,
    this.createdAt,
    this.isActive = true,
  });

  // Computed property for isOpen (can be based on business hours logic)
  bool get isOpen => isActive; // For now, assume active cafeterias are open

  factory SupabaseCafeteria.fromJson(Map<String, dynamic> json) {
    return SupabaseCafeteria(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      imageUrl: json['image_url'],
      location: json['location'],
      rating: json['rating']?.toDouble(),
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'])
          : null,
      isActive: json['is_active'] ?? true,
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'image_url': imageUrl,
      'location': location,
      'rating': rating,
      'created_at': createdAt?.toIso8601String(),
      'is_active': isActive,
    };
  }
}

/// Menu item model for Supabase
class SupabaseMenuItem extends SupabaseModel {
  final String id;
  final String cafeteriaId;
  final String name;
  final String? description;
  final double price;
  final String? imageUrl;
  final bool isAvailable;
  final String? category;
  final Map<String, dynamic>? nutritionInfo;
  final List<String>? ingredients;
  final Map<String, dynamic>? customizationOptions;
  final DateTime? createdAt;

  SupabaseMenuItem({
    required this.id,
    required this.cafeteriaId,
    required this.name,
    this.description,
    required this.price,
    this.imageUrl,
    this.isAvailable = true,
    this.category,
    this.nutritionInfo,
    this.ingredients,
    this.customizationOptions,
    this.createdAt,
  });

  factory SupabaseMenuItem.fromJson(Map<String, dynamic> json) {
    return SupabaseMenuItem(
      id: json['id'],
      cafeteriaId: json['cafeteria_id'],
      name: json['name'],
      description: json['description'],
      price: json['price'].toDouble(),
      imageUrl: json['image_url'],
      isAvailable: json['is_available'] ?? true,
      category: json['category'],
      nutritionInfo: json['nutrition_info'],
      ingredients: json['ingredients'] != null
          ? List<String>.from(json['ingredients'])
          : null,
      customizationOptions: json['customization_options'],
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'])
          : null,
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'cafeteria_id': cafeteriaId,
      'name': name,
      'description': description,
      'price': price,
      'image_url': imageUrl,
      'is_available': isAvailable,
      'category': category,
      'nutrition_info': nutritionInfo,
      'ingredients': ingredients,
      'customization_options': customizationOptions,
      'created_at': createdAt?.toIso8601String(),
    };
  }
}

/// Order model for Supabase
class SupabaseOrder extends SupabaseModel {
  final String id;
  final String userId;
  final String cafeteriaId;
  final double totalAmount;
  final String status;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final List<SupabaseOrderItem>? items;
  final String? orderNumber;
  final String? pickupTime;
  final String? paymentMethod;

  SupabaseOrder({
    required this.id,
    required this.userId,
    required this.cafeteriaId,
    required this.totalAmount,
    required this.status,
    required this.createdAt,
    this.updatedAt,
    this.items,
    this.orderNumber,
    this.pickupTime,
    this.paymentMethod,
  });

  factory SupabaseOrder.fromJson(Map<String, dynamic> json) {
    // Calculate total amount from items if not provided
    double totalAmount = 0.0;
    if (json['total_amount'] != null) {
      totalAmount = json['total_amount'].toDouble();
    }

    return SupabaseOrder(
      id: json['id'],
      userId: json['student_id'] ?? json['user_id'], // Support both field names
      cafeteriaId: json['cafeteria_id'],
      totalAmount: totalAmount,
      status: json['status'],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'])
          : null,
      items: json['items'] != null
          ? (json['items'] as List)
              .map((item) => SupabaseOrderItem.fromJson(item))
              .toList()
          : null,
      orderNumber: json['order_number'],
      pickupTime: json['pickup_time'],
      paymentMethod: json['payment_method'],
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'cafeteria_id': cafeteriaId,
      'total_amount': totalAmount,
      'status': status,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'order_number': orderNumber,
      'pickup_time': pickupTime,
      'payment_method': paymentMethod,
      // Don't include items in the main order JSON
    };
  }
}

/// Order item model for Supabase
class SupabaseOrderItem extends SupabaseModel {
  final String id;
  final String orderId;
  final String menuItemId;
  final int quantity;
  final double price;
  final String? notes;

  SupabaseOrderItem({
    required this.id,
    required this.orderId,
    required this.menuItemId,
    required this.quantity,
    required this.price,
    this.notes,
  });

  factory SupabaseOrderItem.fromJson(Map<String, dynamic> json) {
    // Handle price which might not be in the database
    double price = 0.0;
    if (json['price'] != null) {
      price = json['price'].toDouble();
    }

    return SupabaseOrderItem(
      id: json['id'],
      orderId: json['order_id'],
      menuItemId:
          json['item_id'] ?? json['menu_item_id'], // Support both field names
      quantity: json['quantity'],
      price: price,
      notes:
          json['selected_variant'] ?? json['notes'], // Support both field names
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'order_id': orderId,
      'menu_item_id': menuItemId,
      'quantity': quantity,
      'price': price,
      'notes': notes,
    };
  }
}

/// Menu item rating model for Supabase
class SupabaseMenuItemRating extends SupabaseModel {
  final String id;
  final String userId;
  final String menuItemId;
  final String? orderId;
  final int rating;
  final String? reviewComment;
  final DateTime createdAt;

  SupabaseMenuItemRating({
    required this.id,
    required this.userId,
    required this.menuItemId,
    this.orderId,
    required this.rating,
    this.reviewComment,
    required this.createdAt,
  });

  factory SupabaseMenuItemRating.fromJson(Map<String, dynamic> json) {
    return SupabaseMenuItemRating(
      id: json['id'],
      userId: json['user_id'],
      menuItemId: json['menu_item_id'],
      orderId: json['order_id'],
      rating: json['rating'],
      reviewComment: json['review_comment'],
      createdAt: DateTime.parse(json['created_at']),
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'menu_item_id': menuItemId,
      'order_id': orderId,
      'rating': rating,
      'review_comment': reviewComment,
      'created_at': createdAt.toIso8601String(),
    };
  }
}