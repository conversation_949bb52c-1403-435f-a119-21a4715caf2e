class MenuItem {
  final String id;
  final String name;
  final double price;
  final String description;
  final String image;
  final String cafeteriaId;
  final String category;
  final double rating;
  final bool isAvailable;
  final double discount;
  final Map<String, dynamic>? customizationOptions;
  final Map<String, dynamic>? nutritionInfo;
  final List<String>? ingredients;
  final List<String>? allergens;
  final int preparationTime;

  const MenuItem({
    required this.id,
    required this.name,
    required this.price,
    required this.description,
    required this.image,
    required this.cafeteriaId,
    required this.category,
    required this.rating,
    this.isAvailable = true,
    this.discount = 0.0,
    this.customizationOptions,
    this.nutritionInfo,
    this.ingredients,
    this.allergens,
    this.preparationTime = 15,
  });

  // Formatted currency strings
  String get formattedPrice => '${price.toStringAsFixed(2)} EGP';
  String get formattedDiscountedPrice => '${(price - discount).toStringAsFixed(2)} EGP';

  // Create a MenuItem from a JSON object
  factory MenuItem.fromJson(Map<String, dynamic> json) {
    return MenuItem(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      price: (json['price'] ?? 0.0).toDouble(),
      description: json['description'] ?? '',
      image: json['image'] ?? 'assets/images/placeholder.png',
      cafeteriaId: json['cafeteriaId'] ?? '',
      category: json['category'] ?? 'Other',
      rating: (json['rating'] ?? 0.0).toDouble(),
      isAvailable: json['isAvailable'] ?? true,
      discount: (json['discount'] ?? 0.0).toDouble(),
      customizationOptions: json['customizationOptions'],
      nutritionInfo: json['nutritionInfo'],
      ingredients: json['ingredients'] != null
          ? List<String>.from(json['ingredients'])
          : null,
      allergens: json['allergens'] != null
          ? List<String>.from(json['allergens'])
          : null,
      preparationTime: json['preparationTime'] ?? 15,
    );
  }

  // Create a MenuItem from a Supabase row
  factory MenuItem.fromSupabase(Map<String, dynamic> data) {
    return MenuItem(
      id: data['id'] ?? '',
      name: data['name'] ?? '',
      price: (data['price'] ?? 0.0).toDouble(),
      description: data['description'] ?? '',
      image: data['image_url'] ?? 'assets/images/placeholder.png',
      cafeteriaId: data['cafeteria_id'] ?? '',
      category: data['category'] ?? 'Other',
      rating: (data['rating'] ?? 0.0).toDouble(),
      isAvailable: data['is_available'] ?? true,
      discount: 0.0,
      customizationOptions: data['customization_options'],
      nutritionInfo: data['nutrition_info'],
      ingredients: data['ingredients'] != null
          ? List<String>.from(data['ingredients'])
          : null,
      allergens: data['allergens'] != null
          ? List<String>.from(data['allergens'])
          : null,
      preparationTime: data['preparation_time'] ?? 15,
    );
  }

  // Convert a MenuItem to a Map for Supabase
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'price': price,
      'description': description,
      'image_url': image,
      'cafeteria_id': cafeteriaId,
      'category': category,
      'rating': rating,
      'is_available': isAvailable,
      'customization_options': customizationOptions,
      'nutrition_info': nutritionInfo,
      'ingredients': ingredients,
      'allergens': allergens,
      'preparation_time': preparationTime,
    };
  }

  // Create a copy of this MenuItem with the given fields replaced
  MenuItem copyWith({
    String? id,
    String? name,
    double? price,
    String? description,
    String? image,
    String? cafeteriaId,
    String? category,
    double? rating,
    bool? isAvailable,
    double? discount,
    Map<String, dynamic>? customizationOptions,
    Map<String, dynamic>? nutritionInfo,
    List<String>? ingredients,
    List<String>? allergens,
    int? preparationTime,
  }) {
    return MenuItem(
      id: id ?? this.id,
      name: name ?? this.name,
      price: price ?? this.price,
      description: description ?? this.description,
      image: image ?? this.image,
      cafeteriaId: cafeteriaId ?? this.cafeteriaId,
      category: category ?? this.category,
      rating: rating ?? this.rating,
      isAvailable: isAvailable ?? this.isAvailable,
      discount: discount ?? this.discount,
      customizationOptions: customizationOptions ?? this.customizationOptions,
      nutritionInfo: nutritionInfo ?? this.nutritionInfo,
      ingredients: ingredients ?? this.ingredients,
      allergens: allergens ?? this.allergens,
      preparationTime: preparationTime ?? this.preparationTime,
    );
  }
}
