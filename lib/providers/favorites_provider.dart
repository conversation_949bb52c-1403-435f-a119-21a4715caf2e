import 'package:flutter/foundation.dart';
import 'package:unieatsappv0/models/menu_item.dart';
import 'package:unieatsappv0/models/cafeteria.dart';
import 'package:unieatsappv0/models/supabase_models.dart';
import 'package:unieatsappv0/services/favorites_service.dart';
import 'package:unieatsappv0/services/supabase_service_new.dart';

class FavoritesProvider with ChangeNotifier {
  final Set<String> _favoriteMenuItemIds = {};
  final Set<String> _favoriteCafeteriaIds = {};
  final FavoritesService _favoritesService = FavoritesService();
  final SupabaseService _supabaseService = SupabaseService();

  bool _isLoading = false;
  String? _error;

  List<String> get favoriteMenuItemIds => _favoriteMenuItemIds.toList();
  List<String> get favoriteCafeteriaIds => _favoriteCafeteriaIds.toList();
  bool get isLoading => _isLoading;
  String? get error => _error;

  bool isMenuItemFavorite(String id) => _favoriteMenuItemIds.contains(id);
  bool isCafeteriaFavorite(String id) => _favoriteCafeteriaIds.contains(id);

  /// Initialize favorites from Supabase
  Future<void> initialize() async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      await loadUserFavorites();

      // Subscribe to real-time updates
      final user = _supabaseService.currentUser;
      if (user != null) {
        _favoritesService.subscribeToUserFavorites(user.id, _onFavoriteChange);
      }

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = 'Failed to initialize favorites: $e';
      _isLoading = false;
      notifyListeners();
      debugPrint('Error initializing favorites: $e');
    }
  }

  /// Load user favorites from Supabase
  Future<void> loadUserFavorites() async {
    try {
      final favoriteIds = await _favoritesService.getUserFavoriteIds();
      _favoriteMenuItemIds.clear();
      _favoriteMenuItemIds.addAll(favoriteIds);
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading user favorites: $e');
    }
  }

  /// Toggle menu item favorite status
  Future<void> toggleMenuItemFavorite(String menuItemId) async {
    try {
      _isLoading = true;
      notifyListeners();

      final success = await _favoritesService.toggleFavorite(menuItemId);

      if (success) {
        // Update local state
        if (_favoriteMenuItemIds.contains(menuItemId)) {
          _favoriteMenuItemIds.remove(menuItemId);
        } else {
          _favoriteMenuItemIds.add(menuItemId);
        }
      }

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = 'Failed to update favorite: $e';
      _isLoading = false;
      notifyListeners();
      debugPrint('Error toggling favorite: $e');
    }
  }

  /// Legacy method for MenuItem objects
  void toggleMenuItemFavoriteLegacy(MenuItem item) {
    toggleMenuItemFavorite(item.id);
  }

  /// Legacy method for SupabaseMenuItem objects
  void toggleSupabaseMenuItemFavorite(SupabaseMenuItem item) {
    toggleMenuItemFavorite(item.id);
  }

  /// Toggle cafeteria favorite status (for Supabase cafeterias)
  Future<void> toggleSupabaseCafeteriaFavorite(SupabaseCafeteria cafeteria) async {
    try {
      _isLoading = true;
      notifyListeners();

      // For now, just toggle locally since we don't have cafeteria favorites in Supabase yet
      if (_favoriteCafeteriaIds.contains(cafeteria.id)) {
        _favoriteCafeteriaIds.remove(cafeteria.id);
      } else {
        _favoriteCafeteriaIds.add(cafeteria.id);
      }

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = 'Failed to update cafeteria favorite: $e';
      _isLoading = false;
      notifyListeners();
      debugPrint('Error toggling cafeteria favorite: $e');
    }
  }

  /// Legacy method for Cafeteria objects (kept for compatibility)
  void toggleCafeteriaFavorite(Cafeteria cafeteria) {
    if (_favoriteCafeteriaIds.contains(cafeteria.id)) {
      _favoriteCafeteriaIds.remove(cafeteria.id);
    } else {
      _favoriteCafeteriaIds.add(cafeteria.id);
    }
    notifyListeners();
  }

  /// Handle real-time favorite changes
  void _onFavoriteChange(Map<String, dynamic> payload) {
    // Reload favorites when changes occur
    loadUserFavorites();
  }

  /// Clear all favorites
  Future<void> clearAllFavorites() async {
    try {
      _isLoading = true;
      notifyListeners();

      final success = await _favoritesService.clearAllFavorites();

      if (success) {
        _favoriteMenuItemIds.clear();
      }

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = 'Failed to clear favorites: $e';
      _isLoading = false;
      notifyListeners();
      debugPrint('Error clearing favorites: $e');
    }
  }

  /// Dispose method to clean up subscriptions
  @override
  void dispose() {
    final user = _supabaseService.currentUser;
    if (user != null) {
      _favoritesService.unsubscribeFromUserFavorites(user.id);
    }
    super.dispose();
  }
}