import 'package:flutter/foundation.dart';
import 'package:unieatsappv0/models/supabase_models.dart';
import 'package:unieatsappv0/services/simple_auth_service.dart';

/// A simplified authentication provider that uses SimpleAuthService
class SimpleAuthProvider with ChangeNotifier {
  final SimpleAuthService _authService = SimpleAuthService();

  bool _isLoading = false;
  String? _error;
  UserProfile? _currentUser;

  // Getters
  bool get isLoading => _isLoading;
  String? get error => _error;
  UserProfile? get currentUser => _currentUser;
  bool get isAuthenticated => _currentUser != null;

  // Constructor to check for existing session
  SimpleAuthProvider() {
    checkCurrentUser();
  }

  // Check if a user is already logged in
  Future<void> checkCurrentUser() async {
    _isLoading = true;
    notifyListeners();

    try {
      // Check if there's a current user in Supabase
      final currentUser = _authService.currentUser;

      if (currentUser != null) {
        debugPrint(
            'SimpleAuthProvider: Found existing user session: ${currentUser.email}');

        // Try to get the user profile
        final email = currentUser.email ?? '';

        // Get user profile from login method
        final userProfile = await _authService.login(
          email: email,
          password: '', // We don't need the password for session login
        );

        if (userProfile != null) {
          _currentUser = userProfile;
          debugPrint(
              'SimpleAuthProvider: Loaded user profile for: ${userProfile.fullName}');
        } else {
          debugPrint('SimpleAuthProvider: Could not load user profile');
        }
      } else {
        debugPrint('SimpleAuthProvider: No existing user session found');
      }
    } catch (e) {
      debugPrint('SimpleAuthProvider: Error checking current user: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Register a new user
  Future<bool> register({
    required String email,
    required String password,
    required String fullName,
    String? phone,
  }) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      debugPrint('SimpleAuthProvider: Registering user with email: $email');

      final user = await _authService.register(
        email: email,
        password: password,
        fullName: fullName,
        phone: phone,
      );

      if (user != null) {
        _currentUser = user;
        notifyListeners();
        return true;
      } else {
        _error = 'Failed to register user. Please try again.';
        notifyListeners();
        return false;
      }
    } catch (e) {
      // Handle specific error cases
      String errorMessage = 'Registration failed';

      if (e.toString().contains('already registered')) {
        errorMessage =
            'This email is already registered. Please try logging in instead.';
      } else if (e.toString().contains('invalid email')) {
        errorMessage = 'Please enter a valid email address.';
      } else if (e.toString().contains('password')) {
        errorMessage = 'Password must be at least 6 characters long.';
      } else {
        errorMessage = 'Registration error: ${e.toString().split(':').last}';
      }

      debugPrint('SimpleAuthProvider: Registration error: $e');
      _error = errorMessage;
      notifyListeners();
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Login a user
  Future<bool> login({
    required String email,
    required String password,
  }) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      debugPrint('SimpleAuthProvider: Logging in user with email: $email');

      final user = await _authService.login(
        email: email,
        password: password,
      );

      if (user != null) {
        _currentUser = user;
        notifyListeners();
        return true;
      } else {
        _error = 'Invalid email or password';
        notifyListeners();
        return false;
      }
    } catch (e) {
      // Handle specific error cases
      String errorMessage = 'Login failed';

      if (e.toString().contains('Invalid login credentials')) {
        errorMessage = 'Invalid email or password. Please try again.';
      } else if (e.toString().contains('Email not confirmed')) {
        errorMessage = 'Please confirm your email before logging in.';
      } else {
        errorMessage = 'Login error: ${e.toString().split(':').last}';
      }

      debugPrint('SimpleAuthProvider: Login error: $e');
      _error = errorMessage;
      notifyListeners();
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Logout the current user
  Future<void> logout() async {
    _isLoading = true;
    notifyListeners();

    try {
      await _authService.logout();
      _currentUser = null;
    } catch (e) {
      _error = 'Logout error: $e';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Update the user profile
  Future<bool> updateProfile({
    String? fullName,
    String? email,
    String? phone,
  }) async {
    if (_currentUser == null) {
      _error = 'No user logged in';
      notifyListeners();
      return false;
    }

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final updatedProfile = await _authService.updateProfile(
        userId: _currentUser!.id,
        fullName: fullName,
        email: email,
        phone: phone,
      );

      if (updatedProfile != null) {
        _currentUser = updatedProfile;
        notifyListeners();
        return true;
      } else {
        _error = 'Failed to update profile';
        notifyListeners();
        return false;
      }
    } catch (e) {
      _error = 'Update profile error: $e';
      notifyListeners();
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }
}
