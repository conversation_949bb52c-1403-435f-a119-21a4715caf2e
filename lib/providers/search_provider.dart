import 'package:flutter/foundation.dart';
import '../models/menu_item.dart';
import '../models/cafeteria.dart';
import '../models/supabase_models.dart';
import '../services/supabase_service_new.dart';

class SearchProvider with ChangeNotifier {
  final SupabaseService _supabaseService = SupabaseService();

  List<MenuItem> _menuItems = [];
  List<Cafeteria> _cafeterias = [];
  bool _isLoading = false;
  String _currentQuery = '';

  // Filters
  String _selectedCategory = '';
  double _minPrice = 0;
  double _maxPrice = 1000;
  double _minRating = 0;
  bool _isVegetarian = false;
  bool _isHalal = false;

  // Getters
  List<MenuItem> get menuItems => _menuItems;
  List<Cafeteria> get cafeterias => _cafeterias;
  bool get isLoading => _isLoading;
  String get currentQuery => _currentQuery;

  // Filter getters
  String get selectedCategory => _selectedCategory;
  double get minPrice => _minPrice;
  double get maxPrice => _maxPrice;
  double get minRating => _minRating;
  bool get isVegetarian => _isVegetarian;
  bool get isHalal => _isHalal;

  /// Search for menu items and cafeterias
  Future<void> searchAll(String query) async {
    if (query.trim().isEmpty) {
      clearSearch();
      return;
    }

    _currentQuery = query;
    _isLoading = true;
    notifyListeners();

    try {
      // Search menu items and cafeterias concurrently
      final results = await Future.wait([
        _searchMenuItems(query),
        _searchCafeterias(query),
      ]);

      _menuItems = results[0] as List<MenuItem>;
      _cafeterias = results[1] as List<Cafeteria>;
    } catch (e) {
      debugPrint('Search error: $e');
      _menuItems = [];
      _cafeterias = [];
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Search only menu items using Supabase
  Future<List<MenuItem>> _searchMenuItems(String query) async {
    try {
      // Get all menu items from Supabase with ratings
      final response = await _supabaseService.client
          .from('menu_items')
          .select('''
            *,
            cafeterias!inner(*),
            menu_item_rating(rating)
          ''')
          .ilike('name', '%$query%')
          .eq('is_available', true);

      // Convert SupabaseMenuItem to MenuItem for compatibility
      final List<MenuItem> menuItems = [];
      for (final item in response) {
        final supabaseItem = SupabaseMenuItem.fromJson(item);

        // Calculate average rating from menu_item_rating
        double averageRating = 0.0;
        final ratings = item['menu_item_rating'] as List?;
        if (ratings != null && ratings.isNotEmpty) {
          final sum = ratings.fold(0.0, (double sum, rating) => sum + ((rating['rating'] ?? 0) as num).toDouble());
          averageRating = sum / ratings.length;
        }

        final menuItem = MenuItem(
          id: supabaseItem.id,
          name: supabaseItem.name,
          price: supabaseItem.price,
          description: supabaseItem.description ?? '',
          image: supabaseItem.imageUrl ?? 'assets/images/placeholder.png',
          cafeteriaId: supabaseItem.cafeteriaId,
          category: supabaseItem.category ?? 'Other',
          rating: averageRating > 0 ? averageRating : 4.0, // Use actual rating or default
          isAvailable: supabaseItem.isAvailable,
        );

        // Apply filters
        bool passesFilters = true;

        if (_selectedCategory.isNotEmpty && menuItem.category != _selectedCategory) {
          passesFilters = false;
        }
        if (menuItem.price < _minPrice || menuItem.price > _maxPrice) {
          passesFilters = false;
        }
        if (menuItem.rating < _minRating) {
          passesFilters = false;
        }

        if (passesFilters) {
          menuItems.add(menuItem);
        }
      }

      return menuItems;
    } catch (e) {
      debugPrint('Search menu items error: $e');
      return [];
    }
  }

  /// Search only cafeterias using Supabase
  Future<List<Cafeteria>> _searchCafeterias(String query) async {
    try {
      // Get all cafeterias from Supabase
      final response = await _supabaseService.client
          .from('cafeterias')
          .select('*')
          .ilike('name', '%$query%')
          .eq('is_active', true);

      // Convert SupabaseCafeteria to Cafeteria for compatibility
      final List<Cafeteria> cafeterias = [];
      for (final item in response) {
        final supabaseCafeteria = SupabaseCafeteria.fromJson(item);
        final cafeteria = Cafeteria(
          id: supabaseCafeteria.id,
          name: supabaseCafeteria.name,
          location: supabaseCafeteria.location ?? '',
          phoneNumber: '', // Not available in Supabase model
          email: '', // Not available in Supabase model
          cuisineType: '', // Not available in Supabase model
          description: supabaseCafeteria.description ?? '',
          image: supabaseCafeteria.imageUrl ?? 'assets/images/placeholder.png',
          isOpen: supabaseCafeteria.isOpen,
          openingHours: '9:00 AM - 5:00 PM', // Default hours
          rating: supabaseCafeteria.rating ?? 4.0,
          estimatedTime: 15, // Default time
        );

        // Apply rating filter
        if (cafeteria.rating >= _minRating) {
          cafeterias.add(cafeteria);
        }
      }

      return cafeterias;
    } catch (e) {
      debugPrint('Search cafeterias error: $e');
      return [];
    }
  }

  /// Clear search results
  void clearSearch() {
    _menuItems = [];
    _cafeterias = [];
    _currentQuery = '';
    _isLoading = false;
    notifyListeners();
  }

  /// Set category filter
  void setCategory(String category) {
    _selectedCategory = category;
    if (_currentQuery.isNotEmpty) {
      searchAll(_currentQuery);
    } else {
      notifyListeners();
    }
  }

  /// Set price range filter
  void setPriceRange(double min, double max) {
    _minPrice = min;
    _maxPrice = max;
    if (_currentQuery.isNotEmpty) {
      searchAll(_currentQuery);
    } else {
      notifyListeners();
    }
  }

  /// Set minimum rating filter
  void setMinRating(double rating) {
    _minRating = rating;
    if (_currentQuery.isNotEmpty) {
      searchAll(_currentQuery);
    } else {
      notifyListeners();
    }
  }

  /// Set vegetarian filter
  void setVegetarian(bool isVegetarian) {
    _isVegetarian = isVegetarian;
    if (_currentQuery.isNotEmpty) {
      searchAll(_currentQuery);
    } else {
      notifyListeners();
    }
  }

  /// Set halal filter
  void setHalal(bool isHalal) {
    _isHalal = isHalal;
    if (_currentQuery.isNotEmpty) {
      searchAll(_currentQuery);
    } else {
      notifyListeners();
    }
  }

  /// Clear all filters
  void clearFilters() {
    _selectedCategory = '';
    _minPrice = 0;
    _maxPrice = 1000;
    _minRating = 0;
    _isVegetarian = false;
    _isHalal = false;

    if (_currentQuery.isNotEmpty) {
      searchAll(_currentQuery);
    } else {
      notifyListeners();
    }
  }

  /// Get recent searches (mock implementation)
  List<String> getRecentSearches() {
    // In a real app, you would store this in local storage
    return [
      'Pizza',
      'Burger',
      'Shawarma',
      'Salad',
      'Coffee',
    ];
  }

  /// Get popular searches (mock implementation)
  List<String> getPopularSearches() {
    return [
      'Fried Chicken',
      'Pasta',
      'Sandwich',
      'Juice',
      'Dessert',
    ];
  }

  /// Get search suggestions based on query
  List<String> getSearchSuggestions(String query) {
    if (query.isEmpty) return [];

    final allSuggestions = [
      'Pizza Margherita',
      'Chicken Burger',
      'Beef Shawarma',
      'Caesar Salad',
      'Cappuccino',
      'Fried Rice',
      'Grilled Chicken',
      'Vegetable Soup',
      'Chocolate Cake',
      'Fresh Juice',
    ];

    return allSuggestions
        .where((suggestion) =>
            suggestion.toLowerCase().contains(query.toLowerCase()))
        .take(5)
        .toList();
  }
}
