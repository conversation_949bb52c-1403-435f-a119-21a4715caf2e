import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:unieatsappv0/models/order.dart';

class OrderHistoryProvider with ChangeNotifier {
  List<Order> _orders = [];
  bool _isLoading = false;
  static const String _storageKey = 'order_history';

  List<Order> get orders => [..._orders];
  bool get isLoading => _isLoading;

  // Initialize the provider by loading orders from local storage
  Future<void> loadOrders() async {
    _isLoading = true;
    notifyListeners();

    try {
      final prefs = await SharedPreferences.getInstance();
      final String? ordersJson = prefs.getString(_storageKey);

      if (ordersJson != null) {
        final List<dynamic> decodedData = json.decode(ordersJson);
        _orders =
            decodedData.map((orderData) => Order.fromJson(orderData)).toList();

        // Sort orders by date (newest first)
        _orders.sort((a, b) => b.dateTime.compareTo(a.dateTime));
      }
    } catch (e) {
      debugPrint('Error loading orders: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Add a new order to history
  Future<void> addOrder(Order order) async {
    _orders.insert(0, order); // Add to the beginning of the list
    notifyListeners();
    await _saveOrdersToStorage();
  }

  // Save orders to local storage
  Future<void> _saveOrdersToStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final String ordersJson =
          json.encode(_orders.map((order) => order.toJson()).toList());
      await prefs.setString(_storageKey, ordersJson);
    } catch (e) {
      debugPrint('Error saving orders: $e');
    }
  }

  // Get orders for a specific date
  List<Order> getOrdersForDate(DateTime date) {
    return _orders.where((order) {
      return order.dateTime.year == date.year &&
          order.dateTime.month == date.month &&
          order.dateTime.day == date.day;
    }).toList();
  }

  // Get orders by status
  List<Order> getOrdersByStatus(String status) {
    return _orders.where((order) => order.status == status).toList();
  }

  // Get recent orders (last 30 days)
  List<Order> getRecentOrders() {
    final DateTime thirtyDaysAgo =
        DateTime.now().subtract(const Duration(days: 30));
    return _orders
        .where((order) => order.dateTime.isAfter(thirtyDaysAgo))
        .toList();
  }

  // Update an existing order
  Future<void> updateOrder(Order updatedOrder) async {
    final index = _orders
        .indexWhere((order) => order.orderNumber == updatedOrder.orderNumber);
    if (index != -1) {
      _orders[index] = updatedOrder;
      notifyListeners();
      await _saveOrdersToStorage();
    }
  }

  // Clear all order history
  Future<void> clearOrderHistory() async {
    _orders = [];
    notifyListeners();

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_storageKey);
    } catch (e) {
      debugPrint('Error clearing order history: $e');
    }
  }
}
