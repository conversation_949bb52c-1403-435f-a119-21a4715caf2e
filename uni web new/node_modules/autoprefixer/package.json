{"name": "autoprefixer", "version": "10.4.21", "description": "Parse CSS and add vendor prefixes to CSS rules using values from the Can I Use website", "engines": {"node": "^10 || ^12 || >=14"}, "keywords": ["autoprefixer", "css", "prefix", "postcss", "postcss-plugin"], "main": "lib/autoprefixer.js", "bin": "bin/autoprefixer", "types": "lib/autoprefixer.d.ts", "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/autoprefixer"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "repository": "postcss/autoprefixer", "bugs": {"url": "https://github.com/postcss/autoprefixer/issues"}, "peerDependencies": {"postcss": "^8.1.0"}, "dependencies": {"browserslist": "^4.24.4", "caniuse-lite": "^1.0.30001702", "fraction.js": "^4.3.7", "normalize-range": "^0.1.2", "picocolors": "^1.1.1", "postcss-value-parser": "^4.2.0"}}