(()=>{var $;function C(G){return C=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(H){return typeof H}:function(H){return H&&typeof Symbol=="function"&&H.constructor===Symbol&&H!==Symbol.prototype?"symbol":typeof H},C(G)}function E(G,H){var J=Object.keys(G);if(Object.getOwnPropertySymbols){var X=Object.getOwnPropertySymbols(G);H&&(X=X.filter(function(Y){return Object.getOwnPropertyDescriptor(G,Y).enumerable})),J.push.apply(J,X)}return J}function q(G){for(var H=1;H<arguments.length;H++){var J=arguments[H]!=null?arguments[H]:{};H%2?E(Object(J),!0).forEach(function(X){N(G,X,J[X])}):Object.getOwnPropertyDescriptors?Object.defineProperties(G,Object.getOwnPropertyDescriptors(J)):E(Object(J)).forEach(function(X){Object.defineProperty(G,X,Object.getOwnPropertyDescriptor(J,X))})}return G}function N(G,H,J){if(H=z(H),H in G)Object.defineProperty(G,H,{value:J,enumerable:!0,configurable:!0,writable:!0});else G[H]=J;return G}function z(G){var H=A(G,"string");return C(H)=="symbol"?H:String(H)}function A(G,H){if(C(G)!="object"||!G)return G;var J=G[Symbol.toPrimitive];if(J!==void 0){var X=J.call(G,H||"default");if(C(X)!="object")return X;throw new TypeError("@@toPrimitive must return a primitive value.")}return(H==="string"?String:Number)(G)}var W=Object.defineProperty,YG=function G(H,J){for(var X in J)W(H,X,{get:J[X],enumerable:!0,configurable:!0,set:function Y(Z){return J[X]=function(){return Z}}})},D={lessThanXSeconds:{one:"d\u01B0\u1EDBi 1 gi\xE2y",other:"d\u01B0\u1EDBi {{count}} gi\xE2y"},xSeconds:{one:"1 gi\xE2y",other:"{{count}} gi\xE2y"},halfAMinute:"n\u1EEDa ph\xFAt",lessThanXMinutes:{one:"d\u01B0\u1EDBi 1 ph\xFAt",other:"d\u01B0\u1EDBi {{count}} ph\xFAt"},xMinutes:{one:"1 ph\xFAt",other:"{{count}} ph\xFAt"},aboutXHours:{one:"kho\u1EA3ng 1 gi\u1EDD",other:"kho\u1EA3ng {{count}} gi\u1EDD"},xHours:{one:"1 gi\u1EDD",other:"{{count}} gi\u1EDD"},xDays:{one:"1 ng\xE0y",other:"{{count}} ng\xE0y"},aboutXWeeks:{one:"kho\u1EA3ng 1 tu\u1EA7n",other:"kho\u1EA3ng {{count}} tu\u1EA7n"},xWeeks:{one:"1 tu\u1EA7n",other:"{{count}} tu\u1EA7n"},aboutXMonths:{one:"kho\u1EA3ng 1 th\xE1ng",other:"kho\u1EA3ng {{count}} th\xE1ng"},xMonths:{one:"1 th\xE1ng",other:"{{count}} th\xE1ng"},aboutXYears:{one:"kho\u1EA3ng 1 n\u0103m",other:"kho\u1EA3ng {{count}} n\u0103m"},xYears:{one:"1 n\u0103m",other:"{{count}} n\u0103m"},overXYears:{one:"h\u01A1n 1 n\u0103m",other:"h\u01A1n {{count}} n\u0103m"},almostXYears:{one:"g\u1EA7n 1 n\u0103m",other:"g\u1EA7n {{count}} n\u0103m"}},S=function G(H,J,X){var Y,Z=D[H];if(typeof Z==="string")Y=Z;else if(J===1)Y=Z.one;else Y=Z.other.replace("{{count}}",String(J));if(X!==null&&X!==void 0&&X.addSuffix)if(X.comparison&&X.comparison>0)return Y+" n\u1EEFa";else return Y+" tr\u01B0\u1EDBc";return Y};function K(G){return function(){var H=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},J=H.width?String(H.width):G.defaultWidth,X=G.formats[J]||G.formats[G.defaultWidth];return X}}var M={full:"EEEE, 'ng\xE0y' d MMMM 'n\u0103m' y",long:"'ng\xE0y' d MMMM 'n\u0103m' y",medium:"d MMM 'n\u0103m' y",short:"dd/MM/y"},R={full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},L={full:"{{date}} {{time}}",long:"{{date}} {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},V={date:K({formats:M,defaultWidth:"full"}),time:K({formats:R,defaultWidth:"full"}),dateTime:K({formats:L,defaultWidth:"full"})},j={lastWeek:"eeee 'tu\u1EA7n tr\u01B0\u1EDBc v\xE0o l\xFAc' p",yesterday:"'h\xF4m qua v\xE0o l\xFAc' p",today:"'h\xF4m nay v\xE0o l\xFAc' p",tomorrow:"'ng\xE0y mai v\xE0o l\xFAc' p",nextWeek:"eeee 't\u1EDBi v\xE0o l\xFAc' p",other:"P"},w=function G(H,J,X,Y){return j[H]};function I(G){return function(H,J){var X=J!==null&&J!==void 0&&J.context?String(J.context):"standalone",Y;if(X==="formatting"&&G.formattingValues){var Z=G.defaultFormattingWidth||G.defaultWidth,B=J!==null&&J!==void 0&&J.width?String(J.width):Z;Y=G.formattingValues[B]||G.formattingValues[Z]}else{var T=G.defaultWidth,Q=J!==null&&J!==void 0&&J.width?String(J.width):G.defaultWidth;Y=G.values[Q]||G.values[T]}var U=G.argumentCallback?G.argumentCallback(H):H;return Y[U]}}var _={narrow:["TCN","SCN"],abbreviated:["tr\u01B0\u1EDBc CN","sau CN"],wide:["tr\u01B0\u1EDBc C\xF4ng Nguy\xEAn","sau C\xF4ng Nguy\xEAn"]},F={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["Qu\xFD 1","Qu\xFD 2","Qu\xFD 3","Qu\xFD 4"]},f={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["qu\xFD I","qu\xFD II","qu\xFD III","qu\xFD IV"]},P={narrow:["1","2","3","4","5","6","7","8","9","10","11","12"],abbreviated:["Thg 1","Thg 2","Thg 3","Thg 4","Thg 5","Thg 6","Thg 7","Thg 8","Thg 9","Thg 10","Thg 11","Thg 12"],wide:["Th\xE1ng M\u1ED9t","Th\xE1ng Hai","Th\xE1ng Ba","Th\xE1ng T\u01B0","Th\xE1ng N\u0103m","Th\xE1ng S\xE1u","Th\xE1ng B\u1EA3y","Th\xE1ng T\xE1m","Th\xE1ng Ch\xEDn","Th\xE1ng M\u01B0\u1EDDi","Th\xE1ng M\u01B0\u1EDDi M\u1ED9t","Th\xE1ng M\u01B0\u1EDDi Hai"]},v={narrow:["01","02","03","04","05","06","07","08","09","10","11","12"],abbreviated:["thg 1","thg 2","thg 3","thg 4","thg 5","thg 6","thg 7","thg 8","thg 9","thg 10","thg 11","thg 12"],wide:["th\xE1ng 01","th\xE1ng 02","th\xE1ng 03","th\xE1ng 04","th\xE1ng 05","th\xE1ng 06","th\xE1ng 07","th\xE1ng 08","th\xE1ng 09","th\xE1ng 10","th\xE1ng 11","th\xE1ng 12"]},k={narrow:["CN","T2","T3","T4","T5","T6","T7"],short:["CN","Th 2","Th 3","Th 4","Th 5","Th 6","Th 7"],abbreviated:["CN","Th\u1EE9 2","Th\u1EE9 3","Th\u1EE9 4","Th\u1EE9 5","Th\u1EE9 6","Th\u1EE9 7"],wide:["Ch\u1EE7 Nh\u1EADt","Th\u1EE9 Hai","Th\u1EE9 Ba","Th\u1EE9 T\u01B0","Th\u1EE9 N\u0103m","Th\u1EE9 S\xE1u","Th\u1EE9 B\u1EA3y"]},h={narrow:{am:"am",pm:"pm",midnight:"n\u1EEDa \u0111\xEAm",noon:"tr",morning:"sg",afternoon:"ch",evening:"t\u1ED1i",night:"\u0111\xEAm"},abbreviated:{am:"AM",pm:"PM",midnight:"n\u1EEDa \u0111\xEAm",noon:"tr\u01B0a",morning:"s\xE1ng",afternoon:"chi\u1EC1u",evening:"t\u1ED1i",night:"\u0111\xEAm"},wide:{am:"SA",pm:"CH",midnight:"n\u1EEDa \u0111\xEAm",noon:"tr\u01B0a",morning:"s\xE1ng",afternoon:"chi\u1EC1u",evening:"t\u1ED1i",night:"\u0111\xEAm"}},b={narrow:{am:"am",pm:"pm",midnight:"n\u1EEDa \u0111\xEAm",noon:"tr",morning:"sg",afternoon:"ch",evening:"t\u1ED1i",night:"\u0111\xEAm"},abbreviated:{am:"AM",pm:"PM",midnight:"n\u1EEDa \u0111\xEAm",noon:"tr\u01B0a",morning:"s\xE1ng",afternoon:"chi\u1EC1u",evening:"t\u1ED1i",night:"\u0111\xEAm"},wide:{am:"SA",pm:"CH",midnight:"n\u1EEDa \u0111\xEAm",noon:"gi\u1EEFa tr\u01B0a",morning:"v\xE0o bu\u1ED5i s\xE1ng",afternoon:"v\xE0o bu\u1ED5i chi\u1EC1u",evening:"v\xE0o bu\u1ED5i t\u1ED1i",night:"v\xE0o ban \u0111\xEAm"}},c=function G(H,J){var X=Number(H),Y=J===null||J===void 0?void 0:J.unit;if(Y==="quarter")switch(X){case 1:return"I";case 2:return"II";case 3:return"III";case 4:return"IV"}else if(Y==="day")switch(X){case 1:return"th\u1EE9 2";case 2:return"th\u1EE9 3";case 3:return"th\u1EE9 4";case 4:return"th\u1EE9 5";case 5:return"th\u1EE9 6";case 6:return"th\u1EE9 7";case 7:return"ch\u1EE7 nh\u1EADt"}else if(Y==="week")if(X===1)return"th\u1EE9 nh\u1EA5t";else return"th\u1EE9 "+X;else if(Y==="dayOfYear")if(X===1)return"\u0111\u1EA7u ti\xEAn";else return"th\u1EE9 "+X;return String(X)},y={ordinalNumber:c,era:I({values:_,defaultWidth:"wide"}),quarter:I({values:F,defaultWidth:"wide",formattingValues:f,defaultFormattingWidth:"wide",argumentCallback:function G(H){return H-1}}),month:I({values:P,defaultWidth:"wide",formattingValues:v,defaultFormattingWidth:"wide"}),day:I({values:k,defaultWidth:"wide"}),dayPeriod:I({values:h,defaultWidth:"wide",formattingValues:b,defaultFormattingWidth:"wide"})};function O(G){return function(H){var J=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},X=J.width,Y=X&&G.matchPatterns[X]||G.matchPatterns[G.defaultMatchWidth],Z=H.match(Y);if(!Z)return null;var B=Z[0],T=X&&G.parsePatterns[X]||G.parsePatterns[G.defaultParseWidth],Q=Array.isArray(T)?d(T,function(x){return x.test(B)}):m(T,function(x){return x.test(B)}),U;U=G.valueCallback?G.valueCallback(Q):Q,U=J.valueCallback?J.valueCallback(U):U;var XG=H.slice(B.length);return{value:U,rest:XG}}}function m(G,H){for(var J in G)if(Object.prototype.hasOwnProperty.call(G,J)&&H(G[J]))return J;return}function d(G,H){for(var J=0;J<G.length;J++)if(H(G[J]))return J;return}function g(G){return function(H){var J=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},X=H.match(G.matchPattern);if(!X)return null;var Y=X[0],Z=H.match(G.parsePattern);if(!Z)return null;var B=G.valueCallback?G.valueCallback(Z[0]):Z[0];B=J.valueCallback?J.valueCallback(B):B;var T=H.slice(Y.length);return{value:B,rest:T}}}var p=/^(\d+)/i,l=/\d+/i,u={narrow:/^(tcn|scn)/i,abbreviated:/^(trước CN|sau CN)/i,wide:/^(trước Công Nguyên|sau Công Nguyên)/i},i={any:[/^t/i,/^s/i]},n={narrow:/^([1234]|i{1,3}v?)/i,abbreviated:/^q([1234]|i{1,3}v?)/i,wide:/^quý ([1234]|i{1,3}v?)/i},s={any:[/(1|i)$/i,/(2|ii)$/i,/(3|iii)$/i,/(4|iv)$/i]},o={narrow:/^(0?[2-9]|10|11|12|0?1)/i,abbreviated:/^thg[ _]?(0?[1-9](?!\d)|10|11|12)/i,wide:/^tháng ?(Một|Hai|Ba|Tư|Năm|Sáu|Bảy|Tám|Chín|Mười|Mười ?Một|Mười ?Hai|0?[1-9](?!\d)|10|11|12)/i},r={narrow:[/0?1$/i,/0?2/i,/3/,/4/,/5/,/6/,/7/,/8/,/9/,/10/,/11/,/12/],abbreviated:[/^thg[ _]?0?1(?!\d)/i,/^thg[ _]?0?2/i,/^thg[ _]?0?3/i,/^thg[ _]?0?4/i,/^thg[ _]?0?5/i,/^thg[ _]?0?6/i,/^thg[ _]?0?7/i,/^thg[ _]?0?8/i,/^thg[ _]?0?9/i,/^thg[ _]?10/i,/^thg[ _]?11/i,/^thg[ _]?12/i],wide:[/^tháng ?(Một|0?1(?!\d))/i,/^tháng ?(Hai|0?2)/i,/^tháng ?(Ba|0?3)/i,/^tháng ?(Tư|0?4)/i,/^tháng ?(Năm|0?5)/i,/^tháng ?(Sáu|0?6)/i,/^tháng ?(Bảy|0?7)/i,/^tháng ?(Tám|0?8)/i,/^tháng ?(Chín|0?9)/i,/^tháng ?(Mười|10)/i,/^tháng ?(Mười ?Một|11)/i,/^tháng ?(Mười ?Hai|12)/i]},a={narrow:/^(CN|T2|T3|T4|T5|T6|T7)/i,short:/^(CN|Th ?2|Th ?3|Th ?4|Th ?5|Th ?6|Th ?7)/i,abbreviated:/^(CN|Th ?2|Th ?3|Th ?4|Th ?5|Th ?6|Th ?7)/i,wide:/^(Chủ ?Nhật|Chúa ?Nhật|thứ ?Hai|thứ ?Ba|thứ ?Tư|thứ ?Năm|thứ ?Sáu|thứ ?Bảy)/i},e={narrow:[/CN/i,/2/i,/3/i,/4/i,/5/i,/6/i,/7/i],short:[/CN/i,/2/i,/3/i,/4/i,/5/i,/6/i,/7/i],abbreviated:[/CN/i,/2/i,/3/i,/4/i,/5/i,/6/i,/7/i],wide:[/(Chủ|Chúa) ?Nhật/i,/Hai/i,/Ba/i,/Tư/i,/Năm/i,/Sáu/i,/Bảy/i]},t={narrow:/^(a|p|nửa đêm|trưa|(giờ) (sáng|chiều|tối|đêm))/i,abbreviated:/^(am|pm|nửa đêm|trưa|(giờ) (sáng|chiều|tối|đêm))/i,wide:/^(ch[^i]*|sa|nửa đêm|trưa|(giờ) (sáng|chiều|tối|đêm))/i},GG={any:{am:/^(a|sa)/i,pm:/^(p|ch[^i]*)/i,midnight:/nửa đêm/i,noon:/trưa/i,morning:/sáng/i,afternoon:/chiều/i,evening:/tối/i,night:/^đêm/i}},HG={ordinalNumber:g({matchPattern:p,parsePattern:l,valueCallback:function G(H){return parseInt(H,10)}}),era:O({matchPatterns:u,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any"}),quarter:O({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"any",valueCallback:function G(H){return H+1}}),month:O({matchPatterns:o,defaultMatchWidth:"wide",parsePatterns:r,defaultParseWidth:"wide"}),day:O({matchPatterns:a,defaultMatchWidth:"wide",parsePatterns:e,defaultParseWidth:"wide"}),dayPeriod:O({matchPatterns:t,defaultMatchWidth:"wide",parsePatterns:GG,defaultParseWidth:"any"})},JG={code:"vi",formatDistance:S,formatLong:V,formatRelative:w,localize:y,match:HG,options:{weekStartsOn:1,firstWeekContainsDate:1}};window.dateFns=q(q({},window.dateFns),{},{locale:q(q({},($=window.dateFns)===null||$===void 0?void 0:$.locale),{},{vi:JG})})})();

//# debugId=6C2318370AAF54DE64756E2164756E21
