{"version": 3, "sources": ["../../../../../src/shared/lib/router/utils/get-route-from-asset-path.ts"], "sourcesContent": ["// Translate a pages asset path (relative from a common prefix) back into its logical route\n\nimport { isDynamicRoute } from './is-dynamic'\n\n// \"asset path\" being its javascript file, data file, prerendered html,...\nexport default function getRouteFromAssetPath(\n  assetPath: string,\n  ext: string = ''\n): string {\n  assetPath = assetPath.replace(/\\\\/g, '/')\n  assetPath =\n    ext && assetPath.endsWith(ext) ? assetPath.slice(0, -ext.length) : assetPath\n  if (assetPath.startsWith('/index/') && !isDynamicRoute(assetPath)) {\n    assetPath = assetPath.slice(6)\n  } else if (assetPath === '/index') {\n    assetPath = '/'\n  }\n  return assetPath\n}\n"], "names": ["getRouteFromAssetPath", "assetPath", "ext", "replace", "endsWith", "slice", "length", "startsWith", "isDynamicRoute"], "mappings": "AAAA,2FAA2F;;;;;+BAI3F,0EAA0E;AAC1E;;;eAAwBA;;;2BAHO;AAGhB,SAASA,sBACtBC,SAAiB,EACjBC,GAAgB;IAAhBA,IAAAA,gBAAAA,MAAc;IAEdD,YAAYA,UAAUE,OAAO,CAAC,OAAO;IACrCF,YACEC,OAAOD,UAAUG,QAAQ,CAACF,OAAOD,UAAUI,KAAK,CAAC,GAAG,CAACH,IAAII,MAAM,IAAIL;IACrE,IAAIA,UAAUM,UAAU,CAAC,cAAc,CAACC,IAAAA,yBAAc,EAACP,YAAY;QACjEA,YAAYA,UAAUI,KAAK,CAAC;IAC9B,OAAO,IAAIJ,cAAc,UAAU;QACjCA,YAAY;IACd;IACA,OAAOA;AACT"}