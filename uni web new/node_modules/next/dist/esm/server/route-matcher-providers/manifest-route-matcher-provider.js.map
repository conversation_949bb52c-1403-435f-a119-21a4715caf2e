{"version": 3, "sources": ["../../../src/server/route-matcher-providers/manifest-route-matcher-provider.ts"], "sourcesContent": ["import type { RouteMatcher } from '../route-matchers/route-matcher'\nimport type {\n  <PERSON><PERSON><PERSON>,\n  ManifestLoader,\n} from './helpers/manifest-loaders/manifest-loader'\nimport { CachedRouteMatcherProvider } from './helpers/cached-route-matcher-provider'\n\nexport abstract class ManifestRouteMatcherProvider<\n  M extends RouteMatcher = RouteMatcher,\n> extends CachedRouteMatcherProvider<M, Manifest | null> {\n  constructor(manifestName: string, manifestLoader: ManifestLoader) {\n    super({\n      load: async () => manifestLoader.load(manifestName),\n      compare: (left, right) => left === right,\n    })\n  }\n}\n"], "names": ["CachedRouteMatcherProvider", "ManifestRouteMatcherProvider", "constructor", "manifestName", "manifest<PERSON><PERSON>der", "load", "compare", "left", "right"], "mappings": "AAKA,SAASA,0BAA0B,QAAQ,0CAAyC;AAEpF,OAAO,MAAeC,qCAEZD;IACRE,YAAYC,YAAoB,EAAEC,cAA8B,CAAE;QAChE,KAAK,CAAC;YACJC,MAAM,UAAYD,eAAeC,IAAI,CAACF;YACtCG,SAAS,CAACC,MAAMC,QAAUD,SAASC;QACrC;IACF;AACF"}