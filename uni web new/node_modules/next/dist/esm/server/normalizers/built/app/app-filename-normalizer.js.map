{"version": 3, "sources": ["../../../../../src/server/normalizers/built/app/app-filename-normalizer.ts"], "sourcesContent": ["import { SERVER_DIRECTORY } from '../../../../shared/lib/constants'\nimport { PrefixingNormalizer } from '../../prefixing-normalizer'\n\nexport class AppFilenameNormalizer extends PrefixingNormalizer {\n  constructor(distDir: string) {\n    super(distDir, SERVER_DIRECTORY)\n  }\n\n  public normalize(manifestFilename: string): string {\n    return super.normalize(manifestFilename)\n  }\n}\n"], "names": ["SERVER_DIRECTORY", "PrefixingNormalizer", "AppFilenameNormalizer", "constructor", "distDir", "normalize", "manifestFilename"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,mCAAkC;AACnE,SAASC,mBAAmB,QAAQ,6BAA4B;AAEhE,OAAO,MAAMC,8BAA8BD;IACzCE,YAAYC,OAAe,CAAE;QAC3B,KAAK,CAACA,SAASJ;IACjB;IAEOK,UAAUC,gBAAwB,EAAU;QACjD,OAAO,KAAK,CAACD,UAAUC;IACzB;AACF"}