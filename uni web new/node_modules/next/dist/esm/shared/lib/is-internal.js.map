{"version": 3, "sources": ["../../../src/shared/lib/is-internal.ts"], "sourcesContent": ["/** React that's compiled with `next`. Used by App Router. */\nexport const reactVendoredRe =\n  /[\\\\/]next[\\\\/]dist[\\\\/]compiled[\\\\/](react|react-dom|react-server-dom-(webpack|turbopack)|scheduler)[\\\\/]/\n\n/** React the user installed. Used by Pages Router, or user imports in App Router. */\nexport const reactNodeModulesRe =\n  /node_modules[\\\\/](react|react-dom|scheduler)[\\\\/]/\n\nexport const nextInternalsRe =\n  /(node_modules[\\\\/]next[\\\\/]|[\\\\/].next[\\\\/]static[\\\\/]chunks[\\\\/]webpack\\.js$|(edge-runtime-webpack|webpack-runtime)\\.js$)/\n\nexport default function isInternal(file: string | null) {\n  if (!file) return false\n\n  return (\n    nextInternalsRe.test(file) ||\n    reactVendoredRe.test(file) ||\n    reactNodeModulesRe.test(file)\n  )\n}\n"], "names": ["reactVendoredRe", "reactNodeModulesRe", "nextInternalsRe", "isInternal", "file", "test"], "mappings": "AAAA,2DAA2D,GAC3D,OAAO,MAAMA,kBACX,4GAA2G;AAE7G,mFAAmF,GACnF,OAAO,MAAMC,qBACX,oDAAmD;AAErD,OAAO,MAAMC,kBACX,6HAA4H;AAE9H,eAAe,SAASC,WAAWC,IAAmB;IACpD,IAAI,CAACA,MAAM,OAAO;IAElB,OACEF,gBAAgBG,IAAI,CAACD,SACrBJ,gBAAgBK,IAAI,CAACD,SACrBH,mBAAmBI,IAAI,CAACD;AAE5B"}