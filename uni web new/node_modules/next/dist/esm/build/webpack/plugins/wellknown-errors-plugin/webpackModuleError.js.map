{"version": 3, "sources": ["../../../../../src/build/webpack/plugins/wellknown-errors-plugin/webpackModuleError.ts"], "sourcesContent": ["import { readFileSync } from 'fs'\nimport * as path from 'path'\nimport type { webpack } from 'next/dist/compiled/webpack/webpack'\n\nimport { getBabelError } from './parseBabel'\nimport { getCssError } from './parseCss'\nimport { getScssError } from './parseScss'\nimport { getNotFoundError, getImageError } from './parseNotFoundError'\nimport type { SimpleWebpackError } from './simpleWebpackError'\nimport isError from '../../../../lib/is-error'\nimport { getNextFontError } from './parseNextFontError'\nimport { getNextAppLoaderError } from './parseNextAppLoaderError'\nimport { getNextInvalidImportError } from './parseNextInvalidImportError'\n\nfunction getFileData(\n  compilation: webpack.Compilation,\n  m: any\n): [string, string | null] {\n  let resolved: string\n  let ctx: string | null = compilation.compiler?.context ?? null\n  if (ctx !== null && typeof m.resource === 'string') {\n    const res = path.relative(ctx, m.resource).replace(/\\\\/g, path.posix.sep)\n    resolved = res.startsWith('.') ? res : `.${path.posix.sep}${res}`\n  } else {\n    const requestShortener = compilation.requestShortener\n    if (typeof m?.readableIdentifier === 'function') {\n      resolved = m.readableIdentifier(requestShortener)\n    } else {\n      resolved = m.request ?? m.userRequest\n    }\n  }\n\n  if (resolved) {\n    let content: string | null = null\n    try {\n      content = readFileSync(\n        ctx ? path.resolve(ctx, resolved) : resolved,\n        'utf8'\n      )\n    } catch {}\n    return [resolved, content]\n  }\n\n  return ['<unknown>', null]\n}\n\nexport async function getModuleBuildError(\n  compiler: webpack.Compiler,\n  compilation: webpack.Compilation,\n  input: any\n): Promise<SimpleWebpackError | false> {\n  if (\n    !(\n      typeof input === 'object' &&\n      (input?.name === 'ModuleBuildError' ||\n        input?.name === 'ModuleNotFoundError') &&\n      Boolean(input.module) &&\n      isError(input.error)\n    )\n  ) {\n    return false\n  }\n\n  const err: Error = input.error\n  const [sourceFilename, sourceContent] = getFileData(compilation, input.module)\n\n  const notFoundError = await getNotFoundError(\n    compilation,\n    input,\n    sourceFilename,\n    input.module\n  )\n  if (notFoundError !== false) {\n    return notFoundError\n  }\n\n  const imageError = await getImageError(compilation, input, err)\n  if (imageError !== false) {\n    return imageError\n  }\n\n  const babel = getBabelError(sourceFilename, err)\n  if (babel !== false) {\n    return babel\n  }\n\n  const css = getCssError(sourceFilename, err)\n  if (css !== false) {\n    return css\n  }\n\n  const scss = getScssError(sourceFilename, sourceContent, err)\n  if (scss !== false) {\n    return scss\n  }\n\n  const nextFont = getNextFontError(err, input.module)\n  if (nextFont !== false) {\n    return nextFont\n  }\n\n  const nextAppLoader = getNextAppLoaderError(err, input.module, compiler)\n  if (nextAppLoader !== false) {\n    return nextAppLoader\n  }\n\n  const invalidImportError = getNextInvalidImportError(\n    err,\n    input.module,\n    compilation,\n    compiler\n  )\n  if (invalidImportError !== false) {\n    return invalidImportError\n  }\n\n  return false\n}\n"], "names": ["readFileSync", "path", "getBabelError", "getCssError", "getScssError", "getNotFoundError", "getImageError", "isError", "getNextFontError", "getNextAppLoaderError", "getNextInvalidImportError", "getFileData", "compilation", "m", "resolved", "ctx", "compiler", "context", "resource", "res", "relative", "replace", "posix", "sep", "startsWith", "requestShortener", "readableIdentifier", "request", "userRequest", "content", "resolve", "getModuleBuildError", "input", "name", "Boolean", "module", "error", "err", "sourceFilename", "sourceContent", "notFoundError", "imageError", "babel", "css", "scss", "nextFont", "nextApp<PERSON><PERSON>der", "invalidImportError"], "mappings": "AAAA,SAASA,YAAY,QAAQ,KAAI;AACjC,YAAYC,UAAU,OAAM;AAG5B,SAASC,aAAa,QAAQ,eAAc;AAC5C,SAASC,WAAW,QAAQ,aAAY;AACxC,SAASC,YAAY,QAAQ,cAAa;AAC1C,SAASC,gBAAgB,EAAEC,aAAa,QAAQ,uBAAsB;AAEtE,OAAOC,aAAa,2BAA0B;AAC9C,SAASC,gBAAgB,QAAQ,uBAAsB;AACvD,SAASC,qBAAqB,QAAQ,4BAA2B;AACjE,SAASC,yBAAyB,QAAQ,gCAA+B;AAEzE,SAASC,YACPC,WAAgC,EAChCC,CAAM;QAGmBD;IADzB,IAAIE;IACJ,IAAIC,MAAqBH,EAAAA,wBAAAA,YAAYI,QAAQ,qBAApBJ,sBAAsBK,OAAO,KAAI;IAC1D,IAAIF,QAAQ,QAAQ,OAAOF,EAAEK,QAAQ,KAAK,UAAU;QAClD,MAAMC,MAAMlB,KAAKmB,QAAQ,CAACL,KAAKF,EAAEK,QAAQ,EAAEG,OAAO,CAAC,OAAOpB,KAAKqB,KAAK,CAACC,GAAG;QACxET,WAAWK,IAAIK,UAAU,CAAC,OAAOL,MAAM,CAAC,CAAC,EAAElB,KAAKqB,KAAK,CAACC,GAAG,GAAGJ,KAAK;IACnE,OAAO;QACL,MAAMM,mBAAmBb,YAAYa,gBAAgB;QACrD,IAAI,QAAOZ,qBAAAA,EAAGa,kBAAkB,MAAK,YAAY;YAC/CZ,WAAWD,EAAEa,kBAAkB,CAACD;QAClC,OAAO;YACLX,WAAWD,EAAEc,OAAO,IAAId,EAAEe,WAAW;QACvC;IACF;IAEA,IAAId,UAAU;QACZ,IAAIe,UAAyB;QAC7B,IAAI;YACFA,UAAU7B,aACRe,MAAMd,KAAK6B,OAAO,CAACf,KAAKD,YAAYA,UACpC;QAEJ,EAAE,OAAM,CAAC;QACT,OAAO;YAACA;YAAUe;SAAQ;IAC5B;IAEA,OAAO;QAAC;QAAa;KAAK;AAC5B;AAEA,OAAO,eAAeE,oBACpBf,QAA0B,EAC1BJ,WAAgC,EAChCoB,KAAU;IAEV,IACE,CACE,CAAA,OAAOA,UAAU,YAChBA,CAAAA,CAAAA,yBAAAA,MAAOC,IAAI,MAAK,sBACfD,CAAAA,yBAAAA,MAAOC,IAAI,MAAK,qBAAoB,KACtCC,QAAQF,MAAMG,MAAM,KACpB5B,QAAQyB,MAAMI,KAAK,CAAA,GAErB;QACA,OAAO;IACT;IAEA,MAAMC,MAAaL,MAAMI,KAAK;IAC9B,MAAM,CAACE,gBAAgBC,cAAc,GAAG5B,YAAYC,aAAaoB,MAAMG,MAAM;IAE7E,MAAMK,gBAAgB,MAAMnC,iBAC1BO,aACAoB,OACAM,gBACAN,MAAMG,MAAM;IAEd,IAAIK,kBAAkB,OAAO;QAC3B,OAAOA;IACT;IAEA,MAAMC,aAAa,MAAMnC,cAAcM,aAAaoB,OAAOK;IAC3D,IAAII,eAAe,OAAO;QACxB,OAAOA;IACT;IAEA,MAAMC,QAAQxC,cAAcoC,gBAAgBD;IAC5C,IAAIK,UAAU,OAAO;QACnB,OAAOA;IACT;IAEA,MAAMC,MAAMxC,YAAYmC,gBAAgBD;IACxC,IAAIM,QAAQ,OAAO;QACjB,OAAOA;IACT;IAEA,MAAMC,OAAOxC,aAAakC,gBAAgBC,eAAeF;IACzD,IAAIO,SAAS,OAAO;QAClB,OAAOA;IACT;IAEA,MAAMC,WAAWrC,iBAAiB6B,KAAKL,MAAMG,MAAM;IACnD,IAAIU,aAAa,OAAO;QACtB,OAAOA;IACT;IAEA,MAAMC,gBAAgBrC,sBAAsB4B,KAAKL,MAAMG,MAAM,EAAEnB;IAC/D,IAAI8B,kBAAkB,OAAO;QAC3B,OAAOA;IACT;IAEA,MAAMC,qBAAqBrC,0BACzB2B,KACAL,MAAMG,MAAM,EACZvB,aACAI;IAEF,IAAI+B,uBAAuB,OAAO;QAChC,OAAOA;IACT;IAEA,OAAO;AACT"}