{"version": 3, "sources": ["../../../../src/build/webpack/plugins/next-font-manifest-plugin.ts"], "sourcesContent": ["import { webpack, sources } from 'next/dist/compiled/webpack/webpack'\nimport getRouteFromEntrypoint from '../../../server/get-route-from-entrypoint'\nimport { NEXT_FONT_MANIFEST } from '../../../shared/lib/constants'\nimport { traverseModules } from '../utils'\nimport path from 'path'\n\nexport type NextFontManifest = {\n  pages: {\n    [path: string]: string[]\n  }\n  app: {\n    [entry: string]: string[]\n  }\n  appUsingSizeAdjust: boolean\n  pagesUsingSizeAdjust: boolean\n}\nconst PLUGIN_NAME = 'NextFontManifestPlugin'\n\n/**\n * When calling font functions with next/font, you can specify if you'd like the font to be preloaded (true by default).\n * e.g.: const inter = Inter({ subsets: ['latin'], preload: true })\n *\n * In that case, next-font-loader will emit the font file as [name].p.[ext] instead of [name].[ext]\n * This function returns those files from an array that can include both preloaded and non-preloaded files.\n */\nfunction getPreloadedFontFiles(fontFiles: string[]) {\n  return fontFiles.filter((file: string) =>\n    /\\.p\\.(woff|woff2|eot|ttf|otf)$/.test(file)\n  )\n}\n\n/**\n * Similarly to getPreloadedFontFiles, but returns true if some of the files includes -s in the name.\n * This means that a font is using size adjust in its fallback font.\n * This was added to enable adding data-size-adjust=\"true\" to the dom, used by the Google Aurora team to collect statistics.\n */\nfunction getPageIsUsingSizeAdjust(fontFiles: string[]) {\n  return fontFiles.some((file) => file.includes('-s'))\n}\n\n/**\n * The NextFontManifestPlugin collects all font files emitted by next-font-loader and creates a manifest file.\n * The manifest file is used in the Next.js render functions (_document.tsx for pages/ and app-render for app/) to add preload tags for the font files.\n * We only want to att preload fonts that are used by the current route.\n *\n * For pages/ the plugin finds the fonts imported in the entrypoint chunks and creates a map:\n * { [route]: fontFile[] }\n * When rendering the app in _document.tsx, it gets the font files to preload: manifest.pages[currentRouteBeingRendered].\n *\n * For app/, the manifest is a bit different.\n * Instead of creating a map of route to font files, it creates a map of the webpack module request to font files.\n * { [webpackModuleRequest]: fontFile[]]}\n * When creating the component tree in app-render it looks for font files to preload: manifest.app[moduleBeingRendered]\n */\nexport class NextFontManifestPlugin {\n  private appDir: undefined | string\n\n  constructor(options: { appDir: undefined | string }) {\n    this.appDir = options.appDir\n  }\n\n  apply(compiler: webpack.Compiler) {\n    compiler.hooks.make.tap(PLUGIN_NAME, (compilation) => {\n      // In this stage the font files are emitted and we can collect all files emitted by each chunkGroup (entry).\n      compilation.hooks.processAssets.tap(\n        {\n          name: PLUGIN_NAME,\n          stage: webpack.Compilation.PROCESS_ASSETS_STAGE_ADDITIONS,\n        },\n        () => {\n          const nextFontManifest: NextFontManifest = {\n            pages: {},\n            app: {},\n            appUsingSizeAdjust: false,\n            pagesUsingSizeAdjust: false,\n          }\n\n          if (this.appDir) {\n            const appDirBase = path.dirname(this.appDir) + path.sep\n\n            // After all modules are created, we collect the modules that was created by next-font-loader.\n            traverseModules(\n              compilation,\n              (mod, _chunk, chunkGroup) => {\n                if (mod?.request?.includes('/next-font-loader/index.js?')) {\n                  if (!mod.buildInfo?.assets) return\n\n                  const chunkEntryName = (appDirBase + chunkGroup.name).replace(\n                    /[\\\\/]/g,\n                    path.sep\n                  )\n\n                  const modAssets = Object.keys(mod.buildInfo.assets)\n                  const fontFiles: string[] = modAssets.filter((file: string) =>\n                    /\\.(woff|woff2|eot|ttf|otf)$/.test(file)\n                  )\n\n                  // Look if size-adjust fallback font is being used\n                  if (!nextFontManifest.appUsingSizeAdjust) {\n                    nextFontManifest.appUsingSizeAdjust =\n                      getPageIsUsingSizeAdjust(fontFiles)\n                  }\n\n                  const preloadedFontFiles = getPreloadedFontFiles(fontFiles)\n\n                  // Add an entry of the module's font files in the manifest.\n                  // We'll add an entry even if no files should preload.\n                  // When an entry is present but empty, instead of preloading the font files, a preconnect tag is added.\n                  if (fontFiles.length > 0) {\n                    if (!nextFontManifest.app[chunkEntryName]) {\n                      nextFontManifest.app[chunkEntryName] = []\n                    }\n                    nextFontManifest.app[chunkEntryName].push(\n                      ...preloadedFontFiles\n                    )\n                  }\n                }\n              },\n              (chunkGroup) => {\n                // Only loop through entrypoints that are under app/.\n                return !!chunkGroup.name?.startsWith('app/')\n              }\n            )\n          }\n\n          // Look at all the entrypoints created for pages/.\n          for (const entrypoint of compilation.entrypoints.values()) {\n            const pagePath = getRouteFromEntrypoint(entrypoint.name!)\n\n            if (!pagePath) {\n              continue\n            }\n\n            // Get font files from the chunks included in the entrypoint.\n            const fontFiles: string[] = entrypoint.chunks\n              .flatMap((chunk: any) => [...chunk.auxiliaryFiles])\n              .filter((file: string) =>\n                /\\.(woff|woff2|eot|ttf|otf)$/.test(file)\n              )\n\n            // Look if size-adjust fallback font is being used\n            if (!nextFontManifest.pagesUsingSizeAdjust) {\n              nextFontManifest.pagesUsingSizeAdjust =\n                getPageIsUsingSizeAdjust(fontFiles)\n            }\n\n            const preloadedFontFiles = getPreloadedFontFiles(fontFiles)\n\n            // Add an entry of the route's font files in the manifest.\n            // We'll add an entry even if no files should preload.\n            // When an entry is present but empty, instead of preloading the font files, a preconnect tag is added.\n            if (fontFiles.length > 0) {\n              nextFontManifest.pages[pagePath] = preloadedFontFiles\n            }\n          }\n\n          const manifest = JSON.stringify(nextFontManifest, null)\n          // Create manifest for edge\n          compilation.emitAsset(\n            `server/${NEXT_FONT_MANIFEST}.js`,\n            new sources.RawSource(\n              `self.__NEXT_FONT_MANIFEST=${JSON.stringify(manifest)}`\n            ) as unknown as webpack.sources.RawSource\n          )\n\n          // Create manifest for server\n          compilation.emitAsset(\n            `server/${NEXT_FONT_MANIFEST}.json`,\n            new sources.RawSource(\n              manifest\n            ) as unknown as webpack.sources.RawSource\n          )\n        }\n      )\n    })\n    return\n  }\n}\n"], "names": ["webpack", "sources", "getRouteFromEntrypoint", "NEXT_FONT_MANIFEST", "traverseModules", "path", "PLUGIN_NAME", "getPreloadedFontFiles", "fontFiles", "filter", "file", "test", "getPageIsUsingSizeAdjust", "some", "includes", "NextFontManifestPlugin", "constructor", "options", "appDir", "apply", "compiler", "hooks", "make", "tap", "compilation", "processAssets", "name", "stage", "Compilation", "PROCESS_ASSETS_STAGE_ADDITIONS", "nextFontManifest", "pages", "app", "appUsingSizeAdjust", "pagesUsingSizeAdjust", "appDirBase", "dirname", "sep", "mod", "_chunk", "chunkGroup", "request", "buildInfo", "assets", "chunkEntryName", "replace", "modAssets", "Object", "keys", "preloadedFontFiles", "length", "push", "startsWith", "entrypoint", "entrypoints", "values", "pagePath", "chunks", "flatMap", "chunk", "auxiliaryFiles", "manifest", "JSON", "stringify", "emitAsset", "RawSource"], "mappings": "AAAA,SAASA,OAAO,EAAEC,OAAO,QAAQ,qCAAoC;AACrE,OAAOC,4BAA4B,4CAA2C;AAC9E,SAASC,kBAAkB,QAAQ,gCAA+B;AAClE,SAASC,eAAe,QAAQ,WAAU;AAC1C,OAAOC,UAAU,OAAM;AAYvB,MAAMC,cAAc;AAEpB;;;;;;CAMC,GACD,SAASC,sBAAsBC,SAAmB;IAChD,OAAOA,UAAUC,MAAM,CAAC,CAACC,OACvB,iCAAiCC,IAAI,CAACD;AAE1C;AAEA;;;;CAIC,GACD,SAASE,yBAAyBJ,SAAmB;IACnD,OAAOA,UAAUK,IAAI,CAAC,CAACH,OAASA,KAAKI,QAAQ,CAAC;AAChD;AAEA;;;;;;;;;;;;;CAaC,GACD,OAAO,MAAMC;IAGXC,YAAYC,OAAuC,CAAE;QACnD,IAAI,CAACC,MAAM,GAAGD,QAAQC,MAAM;IAC9B;IAEAC,MAAMC,QAA0B,EAAE;QAChCA,SAASC,KAAK,CAACC,IAAI,CAACC,GAAG,CAACjB,aAAa,CAACkB;YACpC,4GAA4G;YAC5GA,YAAYH,KAAK,CAACI,aAAa,CAACF,GAAG,CACjC;gBACEG,MAAMpB;gBACNqB,OAAO3B,QAAQ4B,WAAW,CAACC,8BAA8B;YAC3D,GACA;gBACE,MAAMC,mBAAqC;oBACzCC,OAAO,CAAC;oBACRC,KAAK,CAAC;oBACNC,oBAAoB;oBACpBC,sBAAsB;gBACxB;gBAEA,IAAI,IAAI,CAAChB,MAAM,EAAE;oBACf,MAAMiB,aAAa9B,KAAK+B,OAAO,CAAC,IAAI,CAAClB,MAAM,IAAIb,KAAKgC,GAAG;oBAEvD,8FAA8F;oBAC9FjC,gBACEoB,aACA,CAACc,KAAKC,QAAQC;4BACRF;wBAAJ,IAAIA,wBAAAA,eAAAA,IAAKG,OAAO,qBAAZH,aAAcxB,QAAQ,CAAC,gCAAgC;gCACpDwB;4BAAL,IAAI,GAACA,iBAAAA,IAAII,SAAS,qBAAbJ,eAAeK,MAAM,GAAE;4BAE5B,MAAMC,iBAAiB,AAACT,CAAAA,aAAaK,WAAWd,IAAI,AAAD,EAAGmB,OAAO,CAC3D,UACAxC,KAAKgC,GAAG;4BAGV,MAAMS,YAAYC,OAAOC,IAAI,CAACV,IAAII,SAAS,CAACC,MAAM;4BAClD,MAAMnC,YAAsBsC,UAAUrC,MAAM,CAAC,CAACC,OAC5C,8BAA8BC,IAAI,CAACD;4BAGrC,kDAAkD;4BAClD,IAAI,CAACoB,iBAAiBG,kBAAkB,EAAE;gCACxCH,iBAAiBG,kBAAkB,GACjCrB,yBAAyBJ;4BAC7B;4BAEA,MAAMyC,qBAAqB1C,sBAAsBC;4BAEjD,2DAA2D;4BAC3D,sDAAsD;4BACtD,uGAAuG;4BACvG,IAAIA,UAAU0C,MAAM,GAAG,GAAG;gCACxB,IAAI,CAACpB,iBAAiBE,GAAG,CAACY,eAAe,EAAE;oCACzCd,iBAAiBE,GAAG,CAACY,eAAe,GAAG,EAAE;gCAC3C;gCACAd,iBAAiBE,GAAG,CAACY,eAAe,CAACO,IAAI,IACpCF;4BAEP;wBACF;oBACF,GACA,CAACT;4BAEUA;wBADT,qDAAqD;wBACrD,OAAO,CAAC,GAACA,mBAAAA,WAAWd,IAAI,qBAAfc,iBAAiBY,UAAU,CAAC;oBACvC;gBAEJ;gBAEA,kDAAkD;gBAClD,KAAK,MAAMC,cAAc7B,YAAY8B,WAAW,CAACC,MAAM,GAAI;oBACzD,MAAMC,WAAWtD,uBAAuBmD,WAAW3B,IAAI;oBAEvD,IAAI,CAAC8B,UAAU;wBACb;oBACF;oBAEA,6DAA6D;oBAC7D,MAAMhD,YAAsB6C,WAAWI,MAAM,CAC1CC,OAAO,CAAC,CAACC,QAAe;+BAAIA,MAAMC,cAAc;yBAAC,EACjDnD,MAAM,CAAC,CAACC,OACP,8BAA8BC,IAAI,CAACD;oBAGvC,kDAAkD;oBAClD,IAAI,CAACoB,iBAAiBI,oBAAoB,EAAE;wBAC1CJ,iBAAiBI,oBAAoB,GACnCtB,yBAAyBJ;oBAC7B;oBAEA,MAAMyC,qBAAqB1C,sBAAsBC;oBAEjD,0DAA0D;oBAC1D,sDAAsD;oBACtD,uGAAuG;oBACvG,IAAIA,UAAU0C,MAAM,GAAG,GAAG;wBACxBpB,iBAAiBC,KAAK,CAACyB,SAAS,GAAGP;oBACrC;gBACF;gBAEA,MAAMY,WAAWC,KAAKC,SAAS,CAACjC,kBAAkB;gBAClD,2BAA2B;gBAC3BN,YAAYwC,SAAS,CACnB,CAAC,OAAO,EAAE7D,mBAAmB,GAAG,CAAC,EACjC,IAAIF,QAAQgE,SAAS,CACnB,CAAC,0BAA0B,EAAEH,KAAKC,SAAS,CAACF,WAAW;gBAI3D,6BAA6B;gBAC7BrC,YAAYwC,SAAS,CACnB,CAAC,OAAO,EAAE7D,mBAAmB,KAAK,CAAC,EACnC,IAAIF,QAAQgE,SAAS,CACnBJ;YAGN;QAEJ;QACA;IACF;AACF"}