{"version": 3, "sources": ["../../../../src/build/webpack/plugins/eval-source-map-dev-tool-plugin.ts"], "sourcesContent": ["/*\n\tMIT License http://www.opensource.org/licenses/mit-license.php\n\tAuthor <PERSON> @sokra\n\n  Forked to add support for `ignoreList`.\n  Keep in sync with packages/next/webpack-plugins/eval-source-map-dev-tool-plugin.js\n*/\nimport {\n  type webpack,\n  type SourceMapDevToolPluginOptions,\n  ConcatenatedModule,\n  makePathsAbsolute,\n  ModuleFilenameHelpers,\n  NormalModule,\n  RuntimeGlobals,\n  SourceMapDevToolModuleOptionsPlugin,\n} from 'next/dist/compiled/webpack/webpack'\nimport type { RawSourceMap } from 'next/dist/compiled/source-map'\n\nconst cache = new WeakMap<webpack.sources.Source, webpack.sources.Source>()\n\nconst devtoolWarningMessage = `/*\n * ATTENTION: An \"eval-source-map\" devtool has been used.\n * This devtool is neither made for production nor for readable output files.\n * It uses \"eval()\" calls to create a separate source file with attached SourceMaps in the browser devtools.\n * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)\n * or disable the default devtool with \"devtool: false\".\n * If you are looking for production-ready output files, see mode: \"production\" (https://webpack.js.org/configuration/mode/).\n */\n`\n\n// @ts-expect-error -- can't compare `string` with `number` in `version`Ï\ninterface SourceMap extends RawSourceMap {\n  ignoreList?: number[]\n  version: number\n}\n\nexport interface EvalSourceMapDevToolPluginOptions\n  extends SourceMapDevToolPluginOptions {\n  // Fork\n  shouldIgnorePath?: (modulePath: string) => boolean\n}\n\n// Fork of webpack's EvalSourceMapDevToolPlugin with support for adding `ignoreList`.\n// https://github.com/webpack/webpack/blob/e237b580e2bda705c5ab39973f786f7c5a7026bc/lib/EvalSourceMapDevToolPlugin.js#L37\nexport default class EvalSourceMapDevToolPlugin {\n  sourceMapComment: string\n  moduleFilenameTemplate: NonNullable<\n    EvalSourceMapDevToolPluginOptions['moduleFilenameTemplate']\n  >\n  namespace: NonNullable<EvalSourceMapDevToolPluginOptions['namespace']>\n  options: EvalSourceMapDevToolPluginOptions\n  shouldIgnorePath: (modulePath: string) => boolean\n\n  /**\n   * @param {SourceMapDevToolPluginOptions|string} inputOptions Options object\n   */\n  constructor(inputOptions: EvalSourceMapDevToolPluginOptions) {\n    let options: EvalSourceMapDevToolPluginOptions\n    if (typeof inputOptions === 'string') {\n      options = {\n        append: inputOptions,\n      }\n    } else {\n      options = inputOptions\n    }\n    this.sourceMapComment =\n      options.append && typeof options.append !== 'function'\n        ? options.append\n        : '//# sourceURL=[module]\\n//# sourceMappingURL=[url]'\n    this.moduleFilenameTemplate =\n      options.moduleFilenameTemplate ||\n      'webpack://[namespace]/[resource-path]?[hash]'\n    this.namespace = options.namespace || ''\n    this.options = options\n\n    // fork\n    this.shouldIgnorePath = options.shouldIgnorePath ?? (() => false)\n  }\n\n  /**\n   * Apply the plugin\n   * @param compiler the compiler instance\n   */\n  apply(compiler: webpack.Compiler): void {\n    const options = this.options\n    compiler.hooks.compilation.tap(\n      'NextJSEvalSourceMapDevToolPlugin',\n      (compilation) => {\n        const { JavascriptModulesPlugin } = compiler.webpack.javascript\n        const { RawSource, ConcatSource } = compiler.webpack.sources\n\n        const devtoolWarning = new RawSource(devtoolWarningMessage)\n\n        const hooks = JavascriptModulesPlugin.getCompilationHooks(compilation)\n\n        new SourceMapDevToolModuleOptionsPlugin(options).apply(compilation)\n        const matchModule = ModuleFilenameHelpers.matchObject.bind(\n          ModuleFilenameHelpers,\n          options\n        )\n\n        hooks.renderModuleContent.tap(\n          'NextJSEvalSourceMapDevToolPlugin',\n          (source, m, { chunk, runtimeTemplate, chunkGraph }) => {\n            const cachedSource = cache.get(source)\n            if (cachedSource !== undefined) {\n              return cachedSource\n            }\n\n            const result = (\n              r: webpack.sources.Source\n            ): webpack.sources.Source => {\n              cache.set(source, r)\n              return r\n            }\n\n            if (m instanceof NormalModule) {\n              const module = m\n              if (!matchModule(module.resource)) {\n                return result(source)\n              }\n            } else if (m instanceof ConcatenatedModule) {\n              const concatModule = m\n              if (concatModule.rootModule instanceof NormalModule) {\n                const module = concatModule.rootModule\n                if (!matchModule(module.resource)) {\n                  return result(source)\n                }\n              } else {\n                return result(source)\n              }\n            } else {\n              return result(source)\n            }\n\n            const namespace = compilation.getPath(this.namespace, {\n              chunk,\n            })\n            let sourceMap: SourceMap\n            let content\n            if (source.sourceAndMap) {\n              const sourceAndMap = source.sourceAndMap(options)\n              sourceMap = sourceAndMap.map as SourceMap\n              content = sourceAndMap.source\n            } else {\n              sourceMap = source.map(options) as SourceMap\n              content = source.source()\n            }\n            if (!sourceMap) {\n              return result(source)\n            }\n\n            // Clone (flat) the sourcemap to ensure that the mutations below do not persist.\n            sourceMap = { ...sourceMap }\n            const context = compiler.options.context!\n            const root = compiler.root\n            const modules = sourceMap.sources.map((sourceMapSource) => {\n              if (!sourceMapSource.startsWith('webpack://'))\n                return sourceMapSource\n              sourceMapSource = makePathsAbsolute(\n                context,\n                sourceMapSource.slice(10),\n                root\n              )\n              const module = compilation.findModule(sourceMapSource)\n              return module || sourceMapSource\n            })\n            let moduleFilenames = modules.map((module) =>\n              ModuleFilenameHelpers.createFilename(\n                module,\n                {\n                  moduleFilenameTemplate: this.moduleFilenameTemplate,\n                  namespace,\n                },\n                {\n                  requestShortener: runtimeTemplate.requestShortener,\n                  chunkGraph,\n                  hashFunction: compilation.outputOptions.hashFunction!,\n                }\n              )\n            )\n            moduleFilenames = ModuleFilenameHelpers.replaceDuplicates(\n              moduleFilenames,\n              (filename, _i, n) => {\n                for (let j = 0; j < n; j++) filename += '*'\n                return filename\n              }\n            )\n            sourceMap.sources = moduleFilenames\n            sourceMap.ignoreList = []\n            for (let index = 0; index < moduleFilenames.length; index++) {\n              if (this.shouldIgnorePath(moduleFilenames[index])) {\n                sourceMap.ignoreList.push(index)\n              }\n            }\n            if (options.noSources) {\n              sourceMap.sourcesContent = undefined\n            }\n            sourceMap.sourceRoot = options.sourceRoot || ''\n            const moduleId =\n              /** @type {ModuleId} */\n              chunkGraph.getModuleId(m)\n            if (moduleId) {\n              sourceMap.file =\n                typeof moduleId === 'number' ? `${moduleId}.js` : moduleId\n            }\n\n            const footer = `${this.sourceMapComment.replace(\n              /\\[url\\]/g,\n              `data:application/json;charset=utf-8;base64,${Buffer.from(\n                JSON.stringify(sourceMap),\n                'utf8'\n              ).toString('base64')}`\n            )}\\n//# sourceURL=webpack-internal:///${moduleId}\\n` // workaround for chrome bug\n\n            return result(\n              new RawSource(\n                `eval(${\n                  compilation.outputOptions.trustedTypes\n                    ? `${RuntimeGlobals.createScript}(${JSON.stringify(\n                        content + footer\n                      )})`\n                    : JSON.stringify(content + footer)\n                });`\n              )\n            )\n          }\n        )\n        hooks.inlineInRuntimeBailout.tap(\n          'EvalDevToolModulePlugin',\n          () => 'the eval-source-map devtool is used.'\n        )\n        hooks.render.tap(\n          'EvalSourceMapDevToolPlugin',\n          (source) => new ConcatSource(devtoolWarning, source)\n        )\n        hooks.chunkHash.tap('EvalSourceMapDevToolPlugin', (_chunk, hash) => {\n          hash.update('EvalSourceMapDevToolPlugin')\n          hash.update('2')\n        })\n        if (compilation.outputOptions.trustedTypes) {\n          compilation.hooks.additionalModuleRuntimeRequirements.tap(\n            'EvalSourceMapDevToolPlugin',\n            (_module, set, _context) => {\n              set.add(RuntimeGlobals.createScript)\n            }\n          )\n        }\n      }\n    )\n  }\n}\n"], "names": ["ConcatenatedModule", "makePathsAbsolute", "ModuleFilenameHelpers", "NormalModule", "RuntimeGlobals", "SourceMapDevToolModuleOptionsPlugin", "cache", "WeakMap", "devtoolWarningMessage", "EvalSourceMapDevToolPlugin", "constructor", "inputOptions", "options", "append", "sourceMapComment", "moduleFilenameTemplate", "namespace", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "apply", "compiler", "hooks", "compilation", "tap", "JavascriptModulesPlugin", "webpack", "javascript", "RawSource", "ConcatSource", "sources", "devtoolWarning", "getCompilationHooks", "matchModule", "matchObject", "bind", "renderModuleContent", "source", "m", "chunk", "runtimeTemplate", "chunkGraph", "cachedSource", "get", "undefined", "result", "r", "set", "module", "resource", "concatModule", "rootModule", "<PERSON><PERSON><PERSON>", "sourceMap", "content", "sourceAndMap", "map", "context", "root", "modules", "sourceMapSource", "startsWith", "slice", "findModule", "moduleFilenames", "createFilename", "requestShortener", "hashFunction", "outputOptions", "replaceDuplicates", "filename", "_i", "n", "j", "ignoreList", "index", "length", "push", "noSources", "sourcesContent", "sourceRoot", "moduleId", "getModuleId", "file", "footer", "replace", "<PERSON><PERSON><PERSON>", "from", "JSON", "stringify", "toString", "trustedTypes", "createScript", "inlineInRuntimeBailout", "render", "chunkHash", "_chunk", "hash", "update", "additionalModuleRuntimeRequirements", "_module", "_context", "add"], "mappings": "AAAA;;;;;;AAMA,GACA,SAGEA,kBAAkB,EAClBC,iBAAiB,EACjBC,qBAAqB,EACrBC,YAAY,EACZC,cAAc,EACdC,mCAAmC,QAC9B,qCAAoC;AAG3C,MAAMC,QAAQ,IAAIC;AAElB,MAAMC,wBAAwB,CAAC;;;;;;;;AAQ/B,CAAC;AAcD,qFAAqF;AACrF,yHAAyH;AACzH,eAAe,MAAMC;IASnB;;GAEC,GACDC,YAAYC,YAA+C,CAAE;QAC3D,IAAIC;QACJ,IAAI,OAAOD,iBAAiB,UAAU;YACpCC,UAAU;gBACRC,QAAQF;YACV;QACF,OAAO;YACLC,UAAUD;QACZ;QACA,IAAI,CAACG,gBAAgB,GACnBF,QAAQC,MAAM,IAAI,OAAOD,QAAQC,MAAM,KAAK,aACxCD,QAAQC,MAAM,GACd;QACN,IAAI,CAACE,sBAAsB,GACzBH,QAAQG,sBAAsB,IAC9B;QACF,IAAI,CAACC,SAAS,GAAGJ,QAAQI,SAAS,IAAI;QACtC,IAAI,CAACJ,OAAO,GAAGA;QAEf,OAAO;QACP,IAAI,CAACK,gBAAgB,GAAGL,QAAQK,gBAAgB,IAAK,CAAA,IAAM,KAAI;IACjE;IAEA;;;GAGC,GACDC,MAAMC,QAA0B,EAAQ;QACtC,MAAMP,UAAU,IAAI,CAACA,OAAO;QAC5BO,SAASC,KAAK,CAACC,WAAW,CAACC,GAAG,CAC5B,oCACA,CAACD;YACC,MAAM,EAAEE,uBAAuB,EAAE,GAAGJ,SAASK,OAAO,CAACC,UAAU;YAC/D,MAAM,EAAEC,SAAS,EAAEC,YAAY,EAAE,GAAGR,SAASK,OAAO,CAACI,OAAO;YAE5D,MAAMC,iBAAiB,IAAIH,UAAUlB;YAErC,MAAMY,QAAQG,wBAAwBO,mBAAmB,CAACT;YAE1D,IAAIhB,oCAAoCO,SAASM,KAAK,CAACG;YACvD,MAAMU,cAAc7B,sBAAsB8B,WAAW,CAACC,IAAI,CACxD/B,uBACAU;YAGFQ,MAAMc,mBAAmB,CAACZ,GAAG,CAC3B,oCACA,CAACa,QAAQC,GAAG,EAAEC,KAAK,EAAEC,eAAe,EAAEC,UAAU,EAAE;gBAChD,MAAMC,eAAelC,MAAMmC,GAAG,CAACN;gBAC/B,IAAIK,iBAAiBE,WAAW;oBAC9B,OAAOF;gBACT;gBAEA,MAAMG,SAAS,CACbC;oBAEAtC,MAAMuC,GAAG,CAACV,QAAQS;oBAClB,OAAOA;gBACT;gBAEA,IAAIR,aAAajC,cAAc;oBAC7B,MAAM2C,SAASV;oBACf,IAAI,CAACL,YAAYe,OAAOC,QAAQ,GAAG;wBACjC,OAAOJ,OAAOR;oBAChB;gBACF,OAAO,IAAIC,aAAapC,oBAAoB;oBAC1C,MAAMgD,eAAeZ;oBACrB,IAAIY,aAAaC,UAAU,YAAY9C,cAAc;wBACnD,MAAM2C,SAASE,aAAaC,UAAU;wBACtC,IAAI,CAAClB,YAAYe,OAAOC,QAAQ,GAAG;4BACjC,OAAOJ,OAAOR;wBAChB;oBACF,OAAO;wBACL,OAAOQ,OAAOR;oBAChB;gBACF,OAAO;oBACL,OAAOQ,OAAOR;gBAChB;gBAEA,MAAMnB,YAAYK,YAAY6B,OAAO,CAAC,IAAI,CAAClC,SAAS,EAAE;oBACpDqB;gBACF;gBACA,IAAIc;gBACJ,IAAIC;gBACJ,IAAIjB,OAAOkB,YAAY,EAAE;oBACvB,MAAMA,eAAelB,OAAOkB,YAAY,CAACzC;oBACzCuC,YAAYE,aAAaC,GAAG;oBAC5BF,UAAUC,aAAalB,MAAM;gBAC/B,OAAO;oBACLgB,YAAYhB,OAAOmB,GAAG,CAAC1C;oBACvBwC,UAAUjB,OAAOA,MAAM;gBACzB;gBACA,IAAI,CAACgB,WAAW;oBACd,OAAOR,OAAOR;gBAChB;gBAEA,gFAAgF;gBAChFgB,YAAY;oBAAE,GAAGA,SAAS;gBAAC;gBAC3B,MAAMI,UAAUpC,SAASP,OAAO,CAAC2C,OAAO;gBACxC,MAAMC,OAAOrC,SAASqC,IAAI;gBAC1B,MAAMC,UAAUN,UAAUvB,OAAO,CAAC0B,GAAG,CAAC,CAACI;oBACrC,IAAI,CAACA,gBAAgBC,UAAU,CAAC,eAC9B,OAAOD;oBACTA,kBAAkBzD,kBAChBsD,SACAG,gBAAgBE,KAAK,CAAC,KACtBJ;oBAEF,MAAMV,SAASzB,YAAYwC,UAAU,CAACH;oBACtC,OAAOZ,UAAUY;gBACnB;gBACA,IAAII,kBAAkBL,QAAQH,GAAG,CAAC,CAACR,SACjC5C,sBAAsB6D,cAAc,CAClCjB,QACA;wBACE/B,wBAAwB,IAAI,CAACA,sBAAsB;wBACnDC;oBACF,GACA;wBACEgD,kBAAkB1B,gBAAgB0B,gBAAgB;wBAClDzB;wBACA0B,cAAc5C,YAAY6C,aAAa,CAACD,YAAY;oBACtD;gBAGJH,kBAAkB5D,sBAAsBiE,iBAAiB,CACvDL,iBACA,CAACM,UAAUC,IAAIC;oBACb,IAAK,IAAIC,IAAI,GAAGA,IAAID,GAAGC,IAAKH,YAAY;oBACxC,OAAOA;gBACT;gBAEFjB,UAAUvB,OAAO,GAAGkC;gBACpBX,UAAUqB,UAAU,GAAG,EAAE;gBACzB,IAAK,IAAIC,QAAQ,GAAGA,QAAQX,gBAAgBY,MAAM,EAAED,QAAS;oBAC3D,IAAI,IAAI,CAACxD,gBAAgB,CAAC6C,eAAe,CAACW,MAAM,GAAG;wBACjDtB,UAAUqB,UAAU,CAACG,IAAI,CAACF;oBAC5B;gBACF;gBACA,IAAI7D,QAAQgE,SAAS,EAAE;oBACrBzB,UAAU0B,cAAc,GAAGnC;gBAC7B;gBACAS,UAAU2B,UAAU,GAAGlE,QAAQkE,UAAU,IAAI;gBAC7C,MAAMC,WACJ,qBAAqB,GACrBxC,WAAWyC,WAAW,CAAC5C;gBACzB,IAAI2C,UAAU;oBACZ5B,UAAU8B,IAAI,GACZ,OAAOF,aAAa,WAAW,GAAGA,SAAS,GAAG,CAAC,GAAGA;gBACtD;gBAEA,MAAMG,SAAS,GAAG,IAAI,CAACpE,gBAAgB,CAACqE,OAAO,CAC7C,YACA,CAAC,2CAA2C,EAAEC,OAAOC,IAAI,CACvDC,KAAKC,SAAS,CAACpC,YACf,QACAqC,QAAQ,CAAC,WAAW,EACtB,oCAAoC,EAAET,SAAS,EAAE,CAAC,CAAC,4BAA4B;;gBAEjF,OAAOpC,OACL,IAAIjB,UACF,CAAC,KAAK,EACJL,YAAY6C,aAAa,CAACuB,YAAY,GAClC,GAAGrF,eAAesF,YAAY,CAAC,CAAC,EAAEJ,KAAKC,SAAS,CAC9CnC,UAAU8B,QACV,CAAC,CAAC,GACJI,KAAKC,SAAS,CAACnC,UAAU8B,QAC9B,EAAE,CAAC;YAGV;YAEF9D,MAAMuE,sBAAsB,CAACrE,GAAG,CAC9B,2BACA,IAAM;YAERF,MAAMwE,MAAM,CAACtE,GAAG,CACd,8BACA,CAACa,SAAW,IAAIR,aAAaE,gBAAgBM;YAE/Cf,MAAMyE,SAAS,CAACvE,GAAG,CAAC,8BAA8B,CAACwE,QAAQC;gBACzDA,KAAKC,MAAM,CAAC;gBACZD,KAAKC,MAAM,CAAC;YACd;YACA,IAAI3E,YAAY6C,aAAa,CAACuB,YAAY,EAAE;gBAC1CpE,YAAYD,KAAK,CAAC6E,mCAAmC,CAAC3E,GAAG,CACvD,8BACA,CAAC4E,SAASrD,KAAKsD;oBACbtD,IAAIuD,GAAG,CAAChG,eAAesF,YAAY;gBACrC;YAEJ;QACF;IAEJ;AACF"}