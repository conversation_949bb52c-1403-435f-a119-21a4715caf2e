{"version": 3, "sources": ["../../../../../src/build/webpack/plugins/wellknown-errors-plugin/parseNotFoundError.ts"], "sourcesContent": ["import { bold, cyan, green, red, yellow } from '../../../../lib/picocolors'\nimport { SimpleWebpackError } from './simpleWebpackError'\nimport {\n  createOriginalStackFrame,\n  getIgnoredSources,\n} from '../../../../client/components/react-dev-overlay/server/middleware-webpack'\nimport type { webpack } from 'next/dist/compiled/webpack/webpack'\n\n// Based on https://github.com/webpack/webpack/blob/fcdd04a833943394bbb0a9eeb54a962a24cc7e41/lib/stats/DefaultStatsFactoryPlugin.js#L422-L431\n/*\nCopyright JS Foundation and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n'Software'), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n*/\nfunction getModuleTrace(input: any, compilation: any) {\n  const visitedModules = new Set()\n  const moduleTrace = []\n  let current = input.module\n  while (current) {\n    if (visitedModules.has(current)) break // circular (technically impossible, but who knows)\n    visitedModules.add(current)\n    const origin = compilation.moduleGraph.getIssuer(current)\n    if (!origin) break\n    moduleTrace.push({ origin, module: current })\n    current = origin\n  }\n\n  return moduleTrace\n}\n\nasync function getSourceFrame(\n  input: any,\n  fileName: any,\n  compilation: any\n): Promise<{ frame: string; lineNumber: string; column: string }> {\n  try {\n    const loc =\n      input.loc || input.dependencies.map((d: any) => d.loc).filter(Boolean)[0]\n    const module = input.module as webpack.Module\n    const originalSource = module.originalSource()\n    const sourceMap = originalSource?.map() ?? undefined\n\n    if (sourceMap) {\n      const moduleId = compilation.chunkGraph.getModuleId(module)\n\n      const result = await createOriginalStackFrame({\n        source: {\n          type: 'bundle',\n          sourceMap,\n          ignoredSources: getIgnoredSources(sourceMap),\n          compilation,\n          moduleId,\n          moduleURL: fileName,\n        },\n        rootDirectory: compilation.options.context!,\n        frame: {\n          arguments: [],\n          file: fileName,\n          methodName: '',\n          lineNumber: loc.start.line,\n          // loc is 0-based but columns in stack frames are 1-based.\n          column: (loc.start.column ?? 0) + 1,\n        },\n      })\n\n      return {\n        frame: result?.originalCodeFrame ?? '',\n        lineNumber: result?.originalStackFrame?.lineNumber?.toString() ?? '',\n        column: result?.originalStackFrame?.column?.toString() ?? '',\n      }\n    }\n  } catch {}\n\n  return { frame: '', lineNumber: '', column: '' }\n}\n\nfunction getFormattedFileName(\n  fileName: string,\n  module: any,\n  lineNumber?: string,\n  column?: string\n): string {\n  if (\n    module.loaders?.find((loader: any) =>\n      /next-font-loader[/\\\\]index.js/.test(loader.loader)\n    )\n  ) {\n    // Parse the query and get the path of the file where the font function was called.\n    // provided by next-swc next-transform-font\n    return JSON.parse(module.resourceResolveData.query.slice(1)).path\n  } else {\n    let formattedFileName: string = cyan(fileName)\n    if (lineNumber && column) {\n      formattedFileName += `:${yellow(lineNumber)}:${yellow(column)}`\n    }\n\n    return formattedFileName\n  }\n}\n\nexport async function getNotFoundError(\n  compilation: webpack.Compilation,\n  input: any,\n  fileName: string,\n  module: any\n) {\n  if (\n    input.name !== 'ModuleNotFoundError' &&\n    !(\n      input.name === 'ModuleBuildError' &&\n      /Error: Can't resolve '.+' in /.test(input.message)\n    )\n  ) {\n    return false\n  }\n\n  try {\n    const { frame, lineNumber, column } = await getSourceFrame(\n      input,\n      fileName,\n      compilation\n    )\n\n    const errorMessage = input.error.message\n      .replace(/ in '.*?'/, '')\n      .replace(/Can't resolve '(.*)'/, `Can't resolve '${green('$1')}'`)\n\n    const importTrace = () => {\n      const moduleTrace = getModuleTrace(input, compilation)\n        .map(({ origin }) =>\n          origin.readableIdentifier(compilation.requestShortener)\n        )\n        .filter(\n          (name) =>\n            name &&\n            !/next-(app|middleware|client-pages|route|flight-(client|server|client-entry))-loader/.test(\n              name\n            ) &&\n            !/css-loader.+\\.js/.test(name)\n        )\n      if (moduleTrace.length === 0) return ''\n\n      return `\\nImport trace for requested module:\\n${moduleTrace.join('\\n')}`\n    }\n\n    let message =\n      red(bold('Module not found')) +\n      `: ${errorMessage}` +\n      '\\n' +\n      frame +\n      (frame !== '' ? '\\n' : '') +\n      '\\nhttps://nextjs.org/docs/messages/module-not-found\\n' +\n      importTrace()\n\n    const formattedFileName = getFormattedFileName(\n      fileName,\n      module,\n      lineNumber,\n      column\n    )\n\n    return new SimpleWebpackError(formattedFileName, message)\n  } catch (err) {\n    // Don't fail on failure to resolve sourcemaps\n    return input\n  }\n}\n\nexport async function getImageError(\n  compilation: any,\n  input: any,\n  err: Error\n): Promise<SimpleWebpackError | false> {\n  if (err.name !== 'InvalidImageFormatError') {\n    return false\n  }\n\n  const moduleTrace = getModuleTrace(input, compilation)\n  const { origin, module } = moduleTrace[0] || {}\n  if (!origin || !module) {\n    return false\n  }\n  const page = origin.rawRequest.replace(/^private-next-pages/, './pages')\n  const importedFile = module.rawRequest\n  const source = origin.originalSource().buffer().toString('utf8') as string\n  let lineNumber = -1\n  source.split('\\n').some((line) => {\n    lineNumber++\n    return line.includes(importedFile)\n  })\n  return new SimpleWebpackError(\n    `${cyan(page)}:${yellow(lineNumber.toString())}`,\n    red(bold('Error')).concat(\n      `: Image import \"${importedFile}\" is not a valid image file. The image may be corrupted or an unsupported format.`\n    )\n  )\n}\n"], "names": ["bold", "cyan", "green", "red", "yellow", "SimpleWebpackError", "createOriginalStackFrame", "getIgnoredSources", "getModuleTrace", "input", "compilation", "visitedModules", "Set", "moduleTrace", "current", "module", "has", "add", "origin", "moduleGraph", "get<PERSON><PERSON><PERSON>", "push", "getSourceFrame", "fileName", "loc", "dependencies", "map", "d", "filter", "Boolean", "originalSource", "sourceMap", "undefined", "result", "moduleId", "chunkGraph", "getModuleId", "source", "type", "ignoredSources", "moduleURL", "rootDirectory", "options", "context", "frame", "arguments", "file", "methodName", "lineNumber", "start", "line", "column", "originalCodeFrame", "originalStackFrame", "toString", "getFormattedFileName", "loaders", "find", "loader", "test", "JSON", "parse", "resourceResolveData", "query", "slice", "path", "formattedFileName", "getNotFoundError", "name", "message", "errorMessage", "error", "replace", "importTrace", "readableIdentifier", "requestShortener", "length", "join", "err", "getImageError", "page", "rawRequest", "importedFile", "buffer", "split", "some", "includes", "concat"], "mappings": "AAAA,SAASA,IAAI,EAAEC,IAAI,EAAEC,KAAK,EAAEC,GAAG,EAAEC,MAAM,QAAQ,6BAA4B;AAC3E,SAASC,kBAAkB,QAAQ,uBAAsB;AACzD,SACEC,wBAAwB,EACxBC,iBAAiB,QACZ,4EAA2E;AAGlF,6IAA6I;AAC7I;;;;;;;;;;;;;;;;;;;;;;AAsBA,GACA,SAASC,eAAeC,KAAU,EAAEC,WAAgB;IAClD,MAAMC,iBAAiB,IAAIC;IAC3B,MAAMC,cAAc,EAAE;IACtB,IAAIC,UAAUL,MAAMM,MAAM;IAC1B,MAAOD,QAAS;QACd,IAAIH,eAAeK,GAAG,CAACF,UAAU,OAAM,mDAAmD;QAC1FH,eAAeM,GAAG,CAACH;QACnB,MAAMI,SAASR,YAAYS,WAAW,CAACC,SAAS,CAACN;QACjD,IAAI,CAACI,QAAQ;QACbL,YAAYQ,IAAI,CAAC;YAAEH;YAAQH,QAAQD;QAAQ;QAC3CA,UAAUI;IACZ;IAEA,OAAOL;AACT;AAEA,eAAeS,eACbb,KAAU,EACVc,QAAa,EACbb,WAAgB;IAEhB,IAAI;QACF,MAAMc,MACJf,MAAMe,GAAG,IAAIf,MAAMgB,YAAY,CAACC,GAAG,CAAC,CAACC,IAAWA,EAAEH,GAAG,EAAEI,MAAM,CAACC,QAAQ,CAAC,EAAE;QAC3E,MAAMd,SAASN,MAAMM,MAAM;QAC3B,MAAMe,iBAAiBf,OAAOe,cAAc;QAC5C,MAAMC,YAAYD,CAAAA,kCAAAA,eAAgBJ,GAAG,OAAMM;QAE3C,IAAID,WAAW;gBAyBCE,uCAAAA,4BACJA,mCAAAA;YAzBV,MAAMC,WAAWxB,YAAYyB,UAAU,CAACC,WAAW,CAACrB;YAEpD,MAAMkB,SAAS,MAAM3B,yBAAyB;gBAC5C+B,QAAQ;oBACNC,MAAM;oBACNP;oBACAQ,gBAAgBhC,kBAAkBwB;oBAClCrB;oBACAwB;oBACAM,WAAWjB;gBACb;gBACAkB,eAAe/B,YAAYgC,OAAO,CAACC,OAAO;gBAC1CC,OAAO;oBACLC,WAAW,EAAE;oBACbC,MAAMvB;oBACNwB,YAAY;oBACZC,YAAYxB,IAAIyB,KAAK,CAACC,IAAI;oBAC1B,0DAA0D;oBAC1DC,QAAQ,AAAC3B,CAAAA,IAAIyB,KAAK,CAACE,MAAM,IAAI,CAAA,IAAK;gBACpC;YACF;YAEA,OAAO;gBACLP,OAAOX,CAAAA,0BAAAA,OAAQmB,iBAAiB,KAAI;gBACpCJ,YAAYf,CAAAA,2BAAAA,6BAAAA,OAAQoB,kBAAkB,sBAA1BpB,wCAAAA,2BAA4Be,UAAU,qBAAtCf,sCAAwCqB,QAAQ,OAAM;gBAClEH,QAAQlB,CAAAA,2BAAAA,8BAAAA,OAAQoB,kBAAkB,sBAA1BpB,oCAAAA,4BAA4BkB,MAAM,qBAAlClB,kCAAoCqB,QAAQ,OAAM;YAC5D;QACF;IACF,EAAE,OAAM,CAAC;IAET,OAAO;QAAEV,OAAO;QAAII,YAAY;QAAIG,QAAQ;IAAG;AACjD;AAEA,SAASI,qBACPhC,QAAgB,EAChBR,MAAW,EACXiC,UAAmB,EACnBG,MAAe;QAGbpC;IADF,KACEA,kBAAAA,OAAOyC,OAAO,qBAAdzC,gBAAgB0C,IAAI,CAAC,CAACC,SACpB,gCAAgCC,IAAI,CAACD,OAAOA,MAAM,IAEpD;QACA,mFAAmF;QACnF,2CAA2C;QAC3C,OAAOE,KAAKC,KAAK,CAAC9C,OAAO+C,mBAAmB,CAACC,KAAK,CAACC,KAAK,CAAC,IAAIC,IAAI;IACnE,OAAO;QACL,IAAIC,oBAA4BjE,KAAKsB;QACrC,IAAIyB,cAAcG,QAAQ;YACxBe,qBAAqB,CAAC,CAAC,EAAE9D,OAAO4C,YAAY,CAAC,EAAE5C,OAAO+C,SAAS;QACjE;QAEA,OAAOe;IACT;AACF;AAEA,OAAO,eAAeC,iBACpBzD,WAAgC,EAChCD,KAAU,EACVc,QAAgB,EAChBR,MAAW;IAEX,IACEN,MAAM2D,IAAI,KAAK,yBACf,CACE3D,CAAAA,MAAM2D,IAAI,KAAK,sBACf,gCAAgCT,IAAI,CAAClD,MAAM4D,OAAO,CAAA,GAEpD;QACA,OAAO;IACT;IAEA,IAAI;QACF,MAAM,EAAEzB,KAAK,EAAEI,UAAU,EAAEG,MAAM,EAAE,GAAG,MAAM7B,eAC1Cb,OACAc,UACAb;QAGF,MAAM4D,eAAe7D,MAAM8D,KAAK,CAACF,OAAO,CACrCG,OAAO,CAAC,aAAa,IACrBA,OAAO,CAAC,wBAAwB,CAAC,eAAe,EAAEtE,MAAM,MAAM,CAAC,CAAC;QAEnE,MAAMuE,cAAc;YAClB,MAAM5D,cAAcL,eAAeC,OAAOC,aACvCgB,GAAG,CAAC,CAAC,EAAER,MAAM,EAAE,GACdA,OAAOwD,kBAAkB,CAAChE,YAAYiE,gBAAgB,GAEvD/C,MAAM,CACL,CAACwC,OACCA,QACA,CAAC,sFAAsFT,IAAI,CACzFS,SAEF,CAAC,mBAAmBT,IAAI,CAACS;YAE/B,IAAIvD,YAAY+D,MAAM,KAAK,GAAG,OAAO;YAErC,OAAO,CAAC,sCAAsC,EAAE/D,YAAYgE,IAAI,CAAC,OAAO;QAC1E;QAEA,IAAIR,UACFlE,IAAIH,KAAK,uBACT,CAAC,EAAE,EAAEsE,cAAc,GACnB,OACA1B,QACCA,CAAAA,UAAU,KAAK,OAAO,EAAC,IACxB,0DACA6B;QAEF,MAAMP,oBAAoBX,qBACxBhC,UACAR,QACAiC,YACAG;QAGF,OAAO,IAAI9C,mBAAmB6D,mBAAmBG;IACnD,EAAE,OAAOS,KAAK;QACZ,8CAA8C;QAC9C,OAAOrE;IACT;AACF;AAEA,OAAO,eAAesE,cACpBrE,WAAgB,EAChBD,KAAU,EACVqE,GAAU;IAEV,IAAIA,IAAIV,IAAI,KAAK,2BAA2B;QAC1C,OAAO;IACT;IAEA,MAAMvD,cAAcL,eAAeC,OAAOC;IAC1C,MAAM,EAAEQ,MAAM,EAAEH,MAAM,EAAE,GAAGF,WAAW,CAAC,EAAE,IAAI,CAAC;IAC9C,IAAI,CAACK,UAAU,CAACH,QAAQ;QACtB,OAAO;IACT;IACA,MAAMiE,OAAO9D,OAAO+D,UAAU,CAACT,OAAO,CAAC,uBAAuB;IAC9D,MAAMU,eAAenE,OAAOkE,UAAU;IACtC,MAAM5C,SAASnB,OAAOY,cAAc,GAAGqD,MAAM,GAAG7B,QAAQ,CAAC;IACzD,IAAIN,aAAa,CAAC;IAClBX,OAAO+C,KAAK,CAAC,MAAMC,IAAI,CAAC,CAACnC;QACvBF;QACA,OAAOE,KAAKoC,QAAQ,CAACJ;IACvB;IACA,OAAO,IAAI7E,mBACT,GAAGJ,KAAK+E,MAAM,CAAC,EAAE5E,OAAO4C,WAAWM,QAAQ,KAAK,EAChDnD,IAAIH,KAAK,UAAUuF,MAAM,CACvB,CAAC,gBAAgB,EAAEL,aAAa,iFAAiF,CAAC;AAGxH"}