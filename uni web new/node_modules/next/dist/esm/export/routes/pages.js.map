{"version": 3, "sources": ["../../../src/export/routes/pages.ts"], "sourcesContent": ["import type { ExportRouteResult } from '../types'\nimport type {\n  PagesRenderContext,\n  PagesSharedContext,\n  RenderOpts,\n} from '../../server/render'\nimport type { LoadComponentsReturnType } from '../../server/load-components'\nimport type { AmpValidation } from '../types'\nimport type { NextParsedUrlQuery } from '../../server/request-meta'\nimport type { Params } from '../../server/request/params'\n\nimport RenderResult from '../../server/render-result'\nimport { join } from 'path'\nimport type {\n  MockedRequest,\n  MockedResponse,\n} from '../../server/lib/mock-request'\nimport { isInAmpMode } from '../../shared/lib/amp-mode'\nimport {\n  NEXT_DATA_SUFFIX,\n  SERVER_PROPS_EXPORT_ERROR,\n} from '../../lib/constants'\nimport { isBailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport AmpHtmlValidator from 'next/dist/compiled/amphtml-validator'\nimport { FileType, fileExists } from '../../lib/file-exists'\nimport { lazyRenderPagesPage } from '../../server/route-modules/pages/module.render'\nimport type { MultiFileWriter } from '../../lib/multi-file-writer'\n\n/**\n * Renders & exports a page associated with the /pages directory\n */\nexport async function exportPagesPage(\n  req: MockedRequest,\n  res: MockedResponse,\n  path: string,\n  page: string,\n  query: NextParsedUrlQuery,\n  params: Params | undefined,\n  htmlFilepath: string,\n  htmlFilename: string,\n  ampPath: string,\n  subFolders: boolean,\n  outDir: string,\n  ampValidatorPath: string | undefined,\n  pagesDataDir: string,\n  buildExport: boolean,\n  isDynamic: boolean,\n  sharedContext: PagesSharedContext,\n  renderContext: PagesRenderContext,\n  hasOrigQueryValues: boolean,\n  renderOpts: RenderOpts,\n  components: LoadComponentsReturnType,\n  fileWriter: MultiFileWriter\n): Promise<ExportRouteResult | undefined> {\n  const ampState = {\n    ampFirst: components.pageConfig?.amp === true,\n    hasQuery: Boolean(query.amp),\n    hybrid: components.pageConfig?.amp === 'hybrid',\n  }\n\n  if (!ampValidatorPath) {\n    ampValidatorPath = require.resolve(\n      'next/dist/compiled/amphtml-validator/validator_wasm.js'\n    )\n  }\n\n  const inAmpMode = isInAmpMode(ampState)\n  const hybridAmp = ampState.hybrid\n\n  if (components.getServerSideProps) {\n    throw new Error(`Error for page ${page}: ${SERVER_PROPS_EXPORT_ERROR}`)\n  }\n\n  // for non-dynamic SSG pages we should have already\n  // prerendered the file\n  if (!buildExport && components.getStaticProps && !isDynamic) {\n    return\n  }\n\n  // Pages router merges page params (e.g. [lang]) with query params\n  // primarily to support them both being accessible on `useRouter().query`.\n  // If we extracted dynamic params from the path, we need to merge them\n  // back into the query object.\n  const searchAndDynamicParams = {\n    ...query,\n    ...params,\n  }\n\n  if (components.getStaticProps && !htmlFilepath.endsWith('.html')) {\n    // make sure it ends with .html if the name contains a dot\n    htmlFilepath += '.html'\n    htmlFilename += '.html'\n  }\n\n  let renderResult: RenderResult | undefined\n\n  if (typeof components.Component === 'string') {\n    renderResult = RenderResult.fromStatic(components.Component)\n\n    if (hasOrigQueryValues) {\n      throw new Error(\n        `\\nError: you provided query values for ${path} which is an auto-exported page. These can not be applied since the page can no longer be re-rendered on the server. To disable auto-export for this page add \\`getInitialProps\\`\\n`\n      )\n    }\n  } else {\n    /**\n     * This sets environment variable to be used at the time of SSR by head.tsx.\n     * Using this from process.env allows targeting SSR by calling\n     * `process.env.__NEXT_OPTIMIZE_CSS`.\n     */\n    if (renderOpts.optimizeCss) {\n      process.env.__NEXT_OPTIMIZE_CSS = JSON.stringify(true)\n    }\n    try {\n      renderResult = await lazyRenderPagesPage(\n        req,\n        res,\n        page,\n        searchAndDynamicParams,\n        renderOpts,\n        sharedContext,\n        renderContext\n      )\n    } catch (err) {\n      if (!isBailoutToCSRError(err)) throw err\n    }\n  }\n\n  const ssgNotFound = renderResult?.metadata.isNotFound\n\n  const ampValidations: AmpValidation[] = []\n\n  const validateAmp = async (\n    rawAmpHtml: string,\n    ampPageName: string,\n    validatorPath: string | undefined\n  ) => {\n    const validator = await AmpHtmlValidator.getInstance(validatorPath)\n    const result = validator.validateString(rawAmpHtml)\n    const errors = result.errors.filter((e) => e.severity === 'ERROR')\n    const warnings = result.errors.filter((e) => e.severity !== 'ERROR')\n\n    if (warnings.length || errors.length) {\n      ampValidations.push({\n        page: ampPageName,\n        result: {\n          errors,\n          warnings,\n        },\n      })\n    }\n  }\n\n  const html =\n    renderResult && !renderResult.isNull ? renderResult.toUnchunkedString() : ''\n\n  let ampRenderResult: RenderResult | undefined\n\n  if (inAmpMode && !renderOpts.ampSkipValidation) {\n    if (!ssgNotFound) {\n      await validateAmp(html, path, ampValidatorPath)\n    }\n  } else if (hybridAmp) {\n    const ampHtmlFilename = subFolders\n      ? join(ampPath, 'index.html')\n      : `${ampPath}.html`\n\n    const ampHtmlFilepath = join(outDir, ampHtmlFilename)\n\n    const exists = await fileExists(ampHtmlFilepath, FileType.File)\n    if (!exists) {\n      try {\n        ampRenderResult = await lazyRenderPagesPage(\n          req,\n          res,\n          page,\n          { ...searchAndDynamicParams, amp: '1' },\n          renderOpts,\n          sharedContext,\n          renderContext\n        )\n      } catch (err) {\n        if (!isBailoutToCSRError(err)) throw err\n      }\n\n      const ampHtml =\n        ampRenderResult && !ampRenderResult.isNull\n          ? ampRenderResult.toUnchunkedString()\n          : ''\n      if (!renderOpts.ampSkipValidation) {\n        await validateAmp(ampHtml, page + '?amp=1', ampValidatorPath)\n      }\n\n      fileWriter.append(ampHtmlFilepath, ampHtml)\n    }\n  }\n\n  const metadata = renderResult?.metadata || ampRenderResult?.metadata || {}\n  if (metadata.pageData) {\n    const dataFile = join(\n      pagesDataDir,\n      htmlFilename.replace(/\\.html$/, NEXT_DATA_SUFFIX)\n    )\n\n    fileWriter.append(dataFile, JSON.stringify(metadata.pageData))\n\n    if (hybridAmp) {\n      fileWriter.append(\n        dataFile.replace(/\\.json$/, '.amp.json'),\n        JSON.stringify(metadata.pageData)\n      )\n    }\n  }\n\n  if (!ssgNotFound) {\n    // don't attempt writing to disk if getStaticProps returned not found\n    fileWriter.append(htmlFilepath, html)\n  }\n\n  return {\n    ampValidations,\n    cacheControl: metadata.cacheControl ?? {\n      revalidate: false,\n      expire: undefined,\n    },\n    ssgNotFound,\n  }\n}\n"], "names": ["RenderResult", "join", "isInAmpMode", "NEXT_DATA_SUFFIX", "SERVER_PROPS_EXPORT_ERROR", "isBailoutToCSRError", "AmpHtmlValidator", "FileType", "fileExists", "lazyRenderPagesPage", "exportPagesPage", "req", "res", "path", "page", "query", "params", "htmlFilepath", "htmlFilename", "ampPath", "subFolders", "outDir", "ampValidator<PERSON>ath", "pagesDataDir", "buildExport", "isDynamic", "sharedContext", "renderContext", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "renderOpts", "components", "fileWriter", "ampState", "ampFirs<PERSON>", "pageConfig", "amp", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "hybrid", "require", "resolve", "inAmpMode", "hybridAmp", "getServerSideProps", "Error", "getStaticProps", "searchAndDynamicParams", "endsWith", "renderResult", "Component", "fromStatic", "optimizeCss", "process", "env", "__NEXT_OPTIMIZE_CSS", "JSON", "stringify", "err", "ssgNotFound", "metadata", "isNotFound", "ampValidations", "validateAmp", "rawAmpHtml", "ampPageName", "validatorPath", "validator", "getInstance", "result", "validateString", "errors", "filter", "e", "severity", "warnings", "length", "push", "html", "isNull", "toUnchunkedString", "ampRenderResult", "ampSkipValidation", "ampHtmlFilename", "ampHtmlFilepath", "exists", "File", "ampHtml", "append", "pageData", "dataFile", "replace", "cacheControl", "revalidate", "expire", "undefined"], "mappings": "AAWA,OAAOA,kBAAkB,6BAA4B;AACrD,SAASC,IAAI,QAAQ,OAAM;AAK3B,SAASC,WAAW,QAAQ,4BAA2B;AACvD,SACEC,gBAAgB,EAChBC,yBAAyB,QACpB,sBAAqB;AAC5B,SAASC,mBAAmB,QAAQ,+CAA8C;AAClF,OAAOC,sBAAsB,uCAAsC;AACnE,SAASC,QAAQ,EAAEC,UAAU,QAAQ,wBAAuB;AAC5D,SAASC,mBAAmB,QAAQ,iDAAgD;AAGpF;;CAEC,GACD,OAAO,eAAeC,gBACpBC,GAAkB,EAClBC,GAAmB,EACnBC,IAAY,EACZC,IAAY,EACZC,KAAyB,EACzBC,MAA0B,EAC1BC,YAAoB,EACpBC,YAAoB,EACpBC,OAAe,EACfC,UAAmB,EACnBC,MAAc,EACdC,gBAAoC,EACpCC,YAAoB,EACpBC,WAAoB,EACpBC,SAAkB,EAClBC,aAAiC,EACjCC,aAAiC,EACjCC,kBAA2B,EAC3BC,UAAsB,EACtBC,UAAoC,EACpCC,UAA2B;QAGfD,wBAEFA;IAHV,MAAME,WAAW;QACfC,UAAUH,EAAAA,yBAAAA,WAAWI,UAAU,qBAArBJ,uBAAuBK,GAAG,MAAK;QACzCC,UAAUC,QAAQtB,MAAMoB,GAAG;QAC3BG,QAAQR,EAAAA,0BAAAA,WAAWI,UAAU,qBAArBJ,wBAAuBK,GAAG,MAAK;IACzC;IAEA,IAAI,CAACb,kBAAkB;QACrBA,mBAAmBiB,QAAQC,OAAO,CAChC;IAEJ;IAEA,MAAMC,YAAYvC,YAAY8B;IAC9B,MAAMU,YAAYV,SAASM,MAAM;IAEjC,IAAIR,WAAWa,kBAAkB,EAAE;QACjC,MAAM,qBAAiE,CAAjE,IAAIC,MAAM,CAAC,eAAe,EAAE9B,KAAK,EAAE,EAAEV,2BAA2B,GAAhE,qBAAA;mBAAA;wBAAA;0BAAA;QAAgE;IACxE;IAEA,mDAAmD;IACnD,uBAAuB;IACvB,IAAI,CAACoB,eAAeM,WAAWe,cAAc,IAAI,CAACpB,WAAW;QAC3D;IACF;IAEA,kEAAkE;IAClE,0EAA0E;IAC1E,sEAAsE;IACtE,8BAA8B;IAC9B,MAAMqB,yBAAyB;QAC7B,GAAG/B,KAAK;QACR,GAAGC,MAAM;IACX;IAEA,IAAIc,WAAWe,cAAc,IAAI,CAAC5B,aAAa8B,QAAQ,CAAC,UAAU;QAChE,0DAA0D;QAC1D9B,gBAAgB;QAChBC,gBAAgB;IAClB;IAEA,IAAI8B;IAEJ,IAAI,OAAOlB,WAAWmB,SAAS,KAAK,UAAU;QAC5CD,eAAehD,aAAakD,UAAU,CAACpB,WAAWmB,SAAS;QAE3D,IAAIrB,oBAAoB;YACtB,MAAM,qBAEL,CAFK,IAAIgB,MACR,CAAC,uCAAuC,EAAE/B,KAAK,mLAAmL,CAAC,GAD/N,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;IACF,OAAO;QACL;;;;KAIC,GACD,IAAIgB,WAAWsB,WAAW,EAAE;YAC1BC,QAAQC,GAAG,CAACC,mBAAmB,GAAGC,KAAKC,SAAS,CAAC;QACnD;QACA,IAAI;YACFR,eAAe,MAAMvC,oBACnBE,KACAC,KACAE,MACAgC,wBACAjB,YACAH,eACAC;QAEJ,EAAE,OAAO8B,KAAK;YACZ,IAAI,CAACpD,oBAAoBoD,MAAM,MAAMA;QACvC;IACF;IAEA,MAAMC,cAAcV,gCAAAA,aAAcW,QAAQ,CAACC,UAAU;IAErD,MAAMC,iBAAkC,EAAE;IAE1C,MAAMC,cAAc,OAClBC,YACAC,aACAC;QAEA,MAAMC,YAAY,MAAM5D,iBAAiB6D,WAAW,CAACF;QACrD,MAAMG,SAASF,UAAUG,cAAc,CAACN;QACxC,MAAMO,SAASF,OAAOE,MAAM,CAACC,MAAM,CAAC,CAACC,IAAMA,EAAEC,QAAQ,KAAK;QAC1D,MAAMC,WAAWN,OAAOE,MAAM,CAACC,MAAM,CAAC,CAACC,IAAMA,EAAEC,QAAQ,KAAK;QAE5D,IAAIC,SAASC,MAAM,IAAIL,OAAOK,MAAM,EAAE;YACpCd,eAAee,IAAI,CAAC;gBAClB9D,MAAMkD;gBACNI,QAAQ;oBACNE;oBACAI;gBACF;YACF;QACF;IACF;IAEA,MAAMG,OACJ7B,gBAAgB,CAACA,aAAa8B,MAAM,GAAG9B,aAAa+B,iBAAiB,KAAK;IAE5E,IAAIC;IAEJ,IAAIvC,aAAa,CAACZ,WAAWoD,iBAAiB,EAAE;QAC9C,IAAI,CAACvB,aAAa;YAChB,MAAMI,YAAYe,MAAMhE,MAAMS;QAChC;IACF,OAAO,IAAIoB,WAAW;QACpB,MAAMwC,kBAAkB9D,aACpBnB,KAAKkB,SAAS,gBACd,GAAGA,QAAQ,KAAK,CAAC;QAErB,MAAMgE,kBAAkBlF,KAAKoB,QAAQ6D;QAErC,MAAME,SAAS,MAAM5E,WAAW2E,iBAAiB5E,SAAS8E,IAAI;QAC9D,IAAI,CAACD,QAAQ;YACX,IAAI;gBACFJ,kBAAkB,MAAMvE,oBACtBE,KACAC,KACAE,MACA;oBAAE,GAAGgC,sBAAsB;oBAAEX,KAAK;gBAAI,GACtCN,YACAH,eACAC;YAEJ,EAAE,OAAO8B,KAAK;gBACZ,IAAI,CAACpD,oBAAoBoD,MAAM,MAAMA;YACvC;YAEA,MAAM6B,UACJN,mBAAmB,CAACA,gBAAgBF,MAAM,GACtCE,gBAAgBD,iBAAiB,KACjC;YACN,IAAI,CAAClD,WAAWoD,iBAAiB,EAAE;gBACjC,MAAMnB,YAAYwB,SAASxE,OAAO,UAAUQ;YAC9C;YAEAS,WAAWwD,MAAM,CAACJ,iBAAiBG;QACrC;IACF;IAEA,MAAM3B,WAAWX,CAAAA,gCAAAA,aAAcW,QAAQ,MAAIqB,mCAAAA,gBAAiBrB,QAAQ,KAAI,CAAC;IACzE,IAAIA,SAAS6B,QAAQ,EAAE;QACrB,MAAMC,WAAWxF,KACfsB,cACAL,aAAawE,OAAO,CAAC,WAAWvF;QAGlC4B,WAAWwD,MAAM,CAACE,UAAUlC,KAAKC,SAAS,CAACG,SAAS6B,QAAQ;QAE5D,IAAI9C,WAAW;YACbX,WAAWwD,MAAM,CACfE,SAASC,OAAO,CAAC,WAAW,cAC5BnC,KAAKC,SAAS,CAACG,SAAS6B,QAAQ;QAEpC;IACF;IAEA,IAAI,CAAC9B,aAAa;QAChB,qEAAqE;QACrE3B,WAAWwD,MAAM,CAACtE,cAAc4D;IAClC;IAEA,OAAO;QACLhB;QACA8B,cAAchC,SAASgC,YAAY,IAAI;YACrCC,YAAY;YACZC,QAAQC;QACV;QACApC;IACF;AACF"}