{"version": 3, "sources": ["../../../../src/client/components/errors/console-error.ts"], "sourcesContent": ["// To distinguish from React error.digest, we use a different symbol here to determine if the error is from console.error or unhandled promise rejection.\nconst digestSym = Symbol.for('next.console.error.digest')\nconst consoleTypeSym = Symbol.for('next.console.error.type')\n\n// Represent non Error shape unhandled promise rejections or console.error errors.\n// Those errors will be captured and displayed in Error Overlay.\ntype UnhandledError = Error & {\n  [digestSym]: 'NEXT_UNHANDLED_ERROR'\n  [consoleTypeSym]: 'string' | 'error'\n  environmentName: string\n}\n\nexport function createUnhandledError(\n  message: string | Error,\n  environmentName?: string | null\n): UnhandledError {\n  const error = (\n    typeof message === 'string' ? new Error(message) : message\n  ) as UnhandledError\n  error[digestSym] = 'NEXT_UNHANDLED_ERROR'\n  error[consoleTypeSym] = typeof message === 'string' ? 'string' : 'error'\n\n  if (environmentName && !error.environmentName) {\n    error.environmentName = environmentName\n  }\n\n  return error\n}\n\nexport const isUnhandledConsoleOrRejection = (\n  error: any\n): error is UnhandledError => {\n  return error && error[digestSym] === 'NEXT_UNHANDLED_ERROR'\n}\n\nexport const getUnhandledErrorType = (error: UnhandledError) => {\n  return error[consoleTypeSym]\n}\n"], "names": ["createUnhandledError", "getUnhandledErrorType", "isUnhandledConsoleOrRejection", "digestSym", "Symbol", "for", "consoleTypeSym", "message", "environmentName", "error", "Error"], "mappings": "AAAA,yJAAyJ;;;;;;;;;;;;;;;;;IAYzIA,oBAAoB;eAApBA;;IAuBHC,qBAAqB;eAArBA;;IANAC,6BAA6B;eAA7BA;;;AA5Bb,MAAMC,YAAYC,OAAOC,GAAG,CAAC;AAC7B,MAAMC,iBAAiBF,OAAOC,GAAG,CAAC;AAU3B,SAASL,qBACdO,OAAuB,EACvBC,eAA+B;IAE/B,MAAMC,QACJ,OAAOF,YAAY,WAAW,qBAAkB,CAAlB,IAAIG,MAAMH,UAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAiB,KAAIA;IAErDE,KAAK,CAACN,UAAU,GAAG;IACnBM,KAAK,CAACH,eAAe,GAAG,OAAOC,YAAY,WAAW,WAAW;IAEjE,IAAIC,mBAAmB,CAACC,MAAMD,eAAe,EAAE;QAC7CC,MAAMD,eAAe,GAAGA;IAC1B;IAEA,OAAOC;AACT;AAEO,MAAMP,gCAAgC,CAC3CO;IAEA,OAAOA,SAASA,KAAK,CAACN,UAAU,KAAK;AACvC;AAEO,MAAMF,wBAAwB,CAACQ;IACpC,OAAOA,KAAK,CAACH,eAAe;AAC9B"}