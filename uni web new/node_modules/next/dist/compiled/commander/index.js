(()=>{var e={718:e=>{"use strict";e.exports=require("node:child_process")},673:e=>{"use strict";e.exports=require("node:events")},561:e=>{"use strict";e.exports=require("node:fs")},411:e=>{"use strict";e.exports=require("node:path")},742:e=>{"use strict";e.exports=require("node:process")},253:(e,t,i)=>{const{InvalidArgumentError:n}=i(299);class Argument{constructor(e,t){this.description=t||"";this.variadic=false;this.parseArg=undefined;this.defaultValue=undefined;this.defaultValueDescription=undefined;this.argChoices=undefined;switch(e[0]){case"<":this.required=true;this._name=e.slice(1,-1);break;case"[":this.required=false;this._name=e.slice(1,-1);break;default:this.required=true;this._name=e;break}if(this._name.length>3&&this._name.slice(-3)==="..."){this.variadic=true;this._name=this._name.slice(0,-3)}}name(){return this._name}_concatValue(e,t){if(t===this.defaultValue||!Array.isArray(t)){return[e]}return t.concat(e)}default(e,t){this.defaultValue=e;this.defaultValueDescription=t;return this}argParser(e){this.parseArg=e;return this}choices(e){this.argChoices=e.slice();this.parseArg=(e,t)=>{if(!this.argChoices.includes(e)){throw new n(`Allowed choices are ${this.argChoices.join(", ")}.`)}if(this.variadic){return this._concatValue(e,t)}return e};return this}argRequired(){this.required=true;return this}argOptional(){this.required=false;return this}}function humanReadableArgName(e){const t=e.name()+(e.variadic===true?"...":"");return e.required?"<"+t+">":"["+t+"]"}t.Argument=Argument;t.humanReadableArgName=humanReadableArgName},119:(e,t,i)=>{const n=i(673).EventEmitter;const s=i(718);const r=i(411);const o=i(561);const a=i(742);const{Argument:h,humanReadableArgName:l}=i(253);const{CommanderError:u}=i(299);const{Help:c}=i(638);const{Option:p,DualOptions:d}=i(77);const{suggestSimilar:m}=i(301);class Command extends n{constructor(e){super();this.commands=[];this.options=[];this.parent=null;this._allowUnknownOption=false;this._allowExcessArguments=true;this.registeredArguments=[];this._args=this.registeredArguments;this.args=[];this.rawArgs=[];this.processedArgs=[];this._scriptPath=null;this._name=e||"";this._optionValues={};this._optionValueSources={};this._storeOptionsAsProperties=false;this._actionHandler=null;this._executableHandler=false;this._executableFile=null;this._executableDir=null;this._defaultCommandName=null;this._exitCallback=null;this._aliases=[];this._combineFlagAndOptionalValue=true;this._description="";this._summary="";this._argsDescription=undefined;this._enablePositionalOptions=false;this._passThroughOptions=false;this._lifeCycleHooks={};this._showHelpAfterError=false;this._showSuggestionAfterError=true;this._outputConfiguration={writeOut:e=>a.stdout.write(e),writeErr:e=>a.stderr.write(e),getOutHelpWidth:()=>a.stdout.isTTY?a.stdout.columns:undefined,getErrHelpWidth:()=>a.stderr.isTTY?a.stderr.columns:undefined,outputError:(e,t)=>t(e)};this._hidden=false;this._helpOption=undefined;this._addImplicitHelpCommand=undefined;this._helpCommand=undefined;this._helpConfiguration={}}copyInheritedSettings(e){this._outputConfiguration=e._outputConfiguration;this._helpOption=e._helpOption;this._helpCommand=e._helpCommand;this._helpConfiguration=e._helpConfiguration;this._exitCallback=e._exitCallback;this._storeOptionsAsProperties=e._storeOptionsAsProperties;this._combineFlagAndOptionalValue=e._combineFlagAndOptionalValue;this._allowExcessArguments=e._allowExcessArguments;this._enablePositionalOptions=e._enablePositionalOptions;this._showHelpAfterError=e._showHelpAfterError;this._showSuggestionAfterError=e._showSuggestionAfterError;return this}_getCommandAndAncestors(){const e=[];for(let t=this;t;t=t.parent){e.push(t)}return e}command(e,t,i){let n=t;let s=i;if(typeof n==="object"&&n!==null){s=n;n=null}s=s||{};const[,r,o]=e.match(/([^ ]+) *(.*)/);const a=this.createCommand(r);if(n){a.description(n);a._executableHandler=true}if(s.isDefault)this._defaultCommandName=a._name;a._hidden=!!(s.noHelp||s.hidden);a._executableFile=s.executableFile||null;if(o)a.arguments(o);this._registerCommand(a);a.parent=this;a.copyInheritedSettings(this);if(n)return this;return a}createCommand(e){return new Command(e)}createHelp(){return Object.assign(new c,this.configureHelp())}configureHelp(e){if(e===undefined)return this._helpConfiguration;this._helpConfiguration=e;return this}configureOutput(e){if(e===undefined)return this._outputConfiguration;Object.assign(this._outputConfiguration,e);return this}showHelpAfterError(e=true){if(typeof e!=="string")e=!!e;this._showHelpAfterError=e;return this}showSuggestionAfterError(e=true){this._showSuggestionAfterError=!!e;return this}addCommand(e,t){if(!e._name){throw new Error(`Command passed to .addCommand() must have a name\n- specify the name in Command constructor or using .name()`)}t=t||{};if(t.isDefault)this._defaultCommandName=e._name;if(t.noHelp||t.hidden)e._hidden=true;this._registerCommand(e);e.parent=this;e._checkForBrokenPassThrough();return this}createArgument(e,t){return new h(e,t)}argument(e,t,i,n){const s=this.createArgument(e,t);if(typeof i==="function"){s.default(n).argParser(i)}else{s.default(i)}this.addArgument(s);return this}arguments(e){e.trim().split(/ +/).forEach((e=>{this.argument(e)}));return this}addArgument(e){const t=this.registeredArguments.slice(-1)[0];if(t&&t.variadic){throw new Error(`only the last argument can be variadic '${t.name()}'`)}if(e.required&&e.defaultValue!==undefined&&e.parseArg===undefined){throw new Error(`a default value for a required argument is never used: '${e.name()}'`)}this.registeredArguments.push(e);return this}helpCommand(e,t){if(typeof e==="boolean"){this._addImplicitHelpCommand=e;return this}e=e??"help [command]";const[,i,n]=e.match(/([^ ]+) *(.*)/);const s=t??"display help for command";const r=this.createCommand(i);r.helpOption(false);if(n)r.arguments(n);if(s)r.description(s);this._addImplicitHelpCommand=true;this._helpCommand=r;return this}addHelpCommand(e,t){if(typeof e!=="object"){this.helpCommand(e,t);return this}this._addImplicitHelpCommand=true;this._helpCommand=e;return this}_getHelpCommand(){const e=this._addImplicitHelpCommand??(this.commands.length&&!this._actionHandler&&!this._findCommand("help"));if(e){if(this._helpCommand===undefined){this.helpCommand(undefined,undefined)}return this._helpCommand}return null}hook(e,t){const i=["preSubcommand","preAction","postAction"];if(!i.includes(e)){throw new Error(`Unexpected value for event passed to hook : '${e}'.\nExpecting one of '${i.join("', '")}'`)}if(this._lifeCycleHooks[e]){this._lifeCycleHooks[e].push(t)}else{this._lifeCycleHooks[e]=[t]}return this}exitOverride(e){if(e){this._exitCallback=e}else{this._exitCallback=e=>{if(e.code!=="commander.executeSubCommandAsync"){throw e}else{}}}return this}_exit(e,t,i){if(this._exitCallback){this._exitCallback(new u(e,t,i))}a.exit(e)}action(e){const listener=t=>{const i=this.registeredArguments.length;const n=t.slice(0,i);if(this._storeOptionsAsProperties){n[i]=this}else{n[i]=this.opts()}n.push(this);return e.apply(this,n)};this._actionHandler=listener;return this}createOption(e,t){return new p(e,t)}_callParseArg(e,t,i,n){try{return e.parseArg(t,i)}catch(e){if(e.code==="commander.invalidArgument"){const t=`${n} ${e.message}`;this.error(t,{exitCode:e.exitCode,code:e.code})}throw e}}_registerOption(e){const t=e.short&&this._findOption(e.short)||e.long&&this._findOption(e.long);if(t){const i=e.long&&this._findOption(e.long)?e.long:e.short;throw new Error(`Cannot add option '${e.flags}'${this._name&&` to command '${this._name}'`} due to conflicting flag '${i}'\n-  already used by option '${t.flags}'`)}this.options.push(e)}_registerCommand(e){const knownBy=e=>[e.name()].concat(e.aliases());const t=knownBy(e).find((e=>this._findCommand(e)));if(t){const i=knownBy(this._findCommand(t)).join("|");const n=knownBy(e).join("|");throw new Error(`cannot add command '${n}' as already have command '${i}'`)}this.commands.push(e)}addOption(e){this._registerOption(e);const t=e.name();const i=e.attributeName();if(e.negate){const t=e.long.replace(/^--no-/,"--");if(!this._findOption(t)){this.setOptionValueWithSource(i,e.defaultValue===undefined?true:e.defaultValue,"default")}}else if(e.defaultValue!==undefined){this.setOptionValueWithSource(i,e.defaultValue,"default")}const handleOptionValue=(t,n,s)=>{if(t==null&&e.presetArg!==undefined){t=e.presetArg}const r=this.getOptionValue(i);if(t!==null&&e.parseArg){t=this._callParseArg(e,t,r,n)}else if(t!==null&&e.variadic){t=e._concatValue(t,r)}if(t==null){if(e.negate){t=false}else if(e.isBoolean()||e.optional){t=true}else{t=""}}this.setOptionValueWithSource(i,t,s)};this.on("option:"+t,(t=>{const i=`error: option '${e.flags}' argument '${t}' is invalid.`;handleOptionValue(t,i,"cli")}));if(e.envVar){this.on("optionEnv:"+t,(t=>{const i=`error: option '${e.flags}' value '${t}' from env '${e.envVar}' is invalid.`;handleOptionValue(t,i,"env")}))}return this}_optionEx(e,t,i,n,s){if(typeof t==="object"&&t instanceof p){throw new Error("To add an Option object use addOption() instead of option() or requiredOption()")}const r=this.createOption(t,i);r.makeOptionMandatory(!!e.mandatory);if(typeof n==="function"){r.default(s).argParser(n)}else if(n instanceof RegExp){const e=n;n=(t,i)=>{const n=e.exec(t);return n?n[0]:i};r.default(s).argParser(n)}else{r.default(n)}return this.addOption(r)}option(e,t,i,n){return this._optionEx({},e,t,i,n)}requiredOption(e,t,i,n){return this._optionEx({mandatory:true},e,t,i,n)}combineFlagAndOptionalValue(e=true){this._combineFlagAndOptionalValue=!!e;return this}allowUnknownOption(e=true){this._allowUnknownOption=!!e;return this}allowExcessArguments(e=true){this._allowExcessArguments=!!e;return this}enablePositionalOptions(e=true){this._enablePositionalOptions=!!e;return this}passThroughOptions(e=true){this._passThroughOptions=!!e;this._checkForBrokenPassThrough();return this}_checkForBrokenPassThrough(){if(this.parent&&this._passThroughOptions&&!this.parent._enablePositionalOptions){throw new Error(`passThroughOptions cannot be used for '${this._name}' without turning on enablePositionalOptions for parent command(s)`)}}storeOptionsAsProperties(e=true){if(this.options.length){throw new Error("call .storeOptionsAsProperties() before adding options")}if(Object.keys(this._optionValues).length){throw new Error("call .storeOptionsAsProperties() before setting option values")}this._storeOptionsAsProperties=!!e;return this}getOptionValue(e){if(this._storeOptionsAsProperties){return this[e]}return this._optionValues[e]}setOptionValue(e,t){return this.setOptionValueWithSource(e,t,undefined)}setOptionValueWithSource(e,t,i){if(this._storeOptionsAsProperties){this[e]=t}else{this._optionValues[e]=t}this._optionValueSources[e]=i;return this}getOptionValueSource(e){return this._optionValueSources[e]}getOptionValueSourceWithGlobals(e){let t;this._getCommandAndAncestors().forEach((i=>{if(i.getOptionValueSource(e)!==undefined){t=i.getOptionValueSource(e)}}));return t}_prepareUserArgs(e,t){if(e!==undefined&&!Array.isArray(e)){throw new Error("first parameter to parse must be array or undefined")}t=t||{};if(e===undefined&&t.from===undefined){if(a.versions?.electron){t.from="electron"}const e=a.execArgv??[];if(e.includes("-e")||e.includes("--eval")||e.includes("-p")||e.includes("--print")){t.from="eval"}}if(e===undefined){e=a.argv}this.rawArgs=e.slice();let i;switch(t.from){case undefined:case"node":this._scriptPath=e[1];i=e.slice(2);break;case"electron":if(a.defaultApp){this._scriptPath=e[1];i=e.slice(2)}else{i=e.slice(1)}break;case"user":i=e.slice(0);break;case"eval":i=e.slice(1);break;default:throw new Error(`unexpected parse option { from: '${t.from}' }`)}if(!this._name&&this._scriptPath)this.nameFromFilename(this._scriptPath);this._name=this._name||"program";return i}parse(e,t){const i=this._prepareUserArgs(e,t);this._parseCommand([],i);return this}async parseAsync(e,t){const i=this._prepareUserArgs(e,t);await this._parseCommand([],i);return this}_executeSubCommand(e,t){t=t.slice();let i=false;const n=[".js",".ts",".tsx",".mjs",".cjs"];function findFile(e,t){const i=r.resolve(e,t);if(o.existsSync(i))return i;if(n.includes(r.extname(t)))return undefined;const s=n.find((e=>o.existsSync(`${i}${e}`)));if(s)return`${i}${s}`;return undefined}this._checkForMissingMandatoryOptions();this._checkForConflictingOptions();let h=e._executableFile||`${this._name}-${e._name}`;let l=this._executableDir||"";if(this._scriptPath){let e;try{e=o.realpathSync(this._scriptPath)}catch(t){e=this._scriptPath}l=r.resolve(r.dirname(e),l)}if(l){let t=findFile(l,h);if(!t&&!e._executableFile&&this._scriptPath){const i=r.basename(this._scriptPath,r.extname(this._scriptPath));if(i!==this._name){t=findFile(l,`${i}-${e._name}`)}}h=t||h}i=n.includes(r.extname(h));let c;if(a.platform!=="win32"){if(i){t.unshift(h);t=incrementNodeInspectorPort(a.execArgv).concat(t);c=s.spawn(a.argv[0],t,{stdio:"inherit"})}else{c=s.spawn(h,t,{stdio:"inherit"})}}else{t.unshift(h);t=incrementNodeInspectorPort(a.execArgv).concat(t);c=s.spawn(a.execPath,t,{stdio:"inherit"})}if(!c.killed){const e=["SIGUSR1","SIGUSR2","SIGTERM","SIGINT","SIGHUP"];e.forEach((e=>{a.on(e,(()=>{if(c.killed===false&&c.exitCode===null){c.kill(e)}}))}))}const p=this._exitCallback;c.on("close",(e=>{e=e??1;if(!p){a.exit(e)}else{p(new u(e,"commander.executeSubCommandAsync","(close)"))}}));c.on("error",(t=>{if(t.code==="ENOENT"){const t=l?`searched for local subcommand relative to directory '${l}'`:"no directory for search for local subcommand, use .executableDir() to supply a custom directory";const i=`'${h}' does not exist\n - if '${e._name}' is not meant to be an executable command, remove description parameter from '.command()' and use '.description()' instead\n - if the default executable name is not suitable, use the executableFile option to supply a custom name or path\n - ${t}`;throw new Error(i)}else if(t.code==="EACCES"){throw new Error(`'${h}' not executable`)}if(!p){a.exit(1)}else{const e=new u(1,"commander.executeSubCommandAsync","(error)");e.nestedError=t;p(e)}}));this.runningCommand=c}_dispatchSubcommand(e,t,i){const n=this._findCommand(e);if(!n)this.help({error:true});let s;s=this._chainOrCallSubCommandHook(s,n,"preSubcommand");s=this._chainOrCall(s,(()=>{if(n._executableHandler){this._executeSubCommand(n,t.concat(i))}else{return n._parseCommand(t,i)}}));return s}_dispatchHelpCommand(e){if(!e){this.help()}const t=this._findCommand(e);if(t&&!t._executableHandler){t.help()}return this._dispatchSubcommand(e,[],[this._getHelpOption()?.long??this._getHelpOption()?.short??"--help"])}_checkNumberOfArguments(){this.registeredArguments.forEach(((e,t)=>{if(e.required&&this.args[t]==null){this.missingArgument(e.name())}}));if(this.registeredArguments.length>0&&this.registeredArguments[this.registeredArguments.length-1].variadic){return}if(this.args.length>this.registeredArguments.length){this._excessArguments(this.args)}}_processArguments(){const myParseArg=(e,t,i)=>{let n=t;if(t!==null&&e.parseArg){const s=`error: command-argument value '${t}' is invalid for argument '${e.name()}'.`;n=this._callParseArg(e,t,i,s)}return n};this._checkNumberOfArguments();const e=[];this.registeredArguments.forEach(((t,i)=>{let n=t.defaultValue;if(t.variadic){if(i<this.args.length){n=this.args.slice(i);if(t.parseArg){n=n.reduce(((e,i)=>myParseArg(t,i,e)),t.defaultValue)}}else if(n===undefined){n=[]}}else if(i<this.args.length){n=this.args[i];if(t.parseArg){n=myParseArg(t,n,t.defaultValue)}}e[i]=n}));this.processedArgs=e}_chainOrCall(e,t){if(e&&e.then&&typeof e.then==="function"){return e.then((()=>t()))}return t()}_chainOrCallHooks(e,t){let i=e;const n=[];this._getCommandAndAncestors().reverse().filter((e=>e._lifeCycleHooks[t]!==undefined)).forEach((e=>{e._lifeCycleHooks[t].forEach((t=>{n.push({hookedCommand:e,callback:t})}))}));if(t==="postAction"){n.reverse()}n.forEach((e=>{i=this._chainOrCall(i,(()=>e.callback(e.hookedCommand,this)))}));return i}_chainOrCallSubCommandHook(e,t,i){let n=e;if(this._lifeCycleHooks[i]!==undefined){this._lifeCycleHooks[i].forEach((e=>{n=this._chainOrCall(n,(()=>e(this,t)))}))}return n}_parseCommand(e,t){const i=this.parseOptions(t);this._parseOptionsEnv();this._parseOptionsImplied();e=e.concat(i.operands);t=i.unknown;this.args=e.concat(t);if(e&&this._findCommand(e[0])){return this._dispatchSubcommand(e[0],e.slice(1),t)}if(this._getHelpCommand()&&e[0]===this._getHelpCommand().name()){return this._dispatchHelpCommand(e[1])}if(this._defaultCommandName){this._outputHelpIfRequested(t);return this._dispatchSubcommand(this._defaultCommandName,e,t)}if(this.commands.length&&this.args.length===0&&!this._actionHandler&&!this._defaultCommandName){this.help({error:true})}this._outputHelpIfRequested(i.unknown);this._checkForMissingMandatoryOptions();this._checkForConflictingOptions();const checkForUnknownOptions=()=>{if(i.unknown.length>0){this.unknownOption(i.unknown[0])}};const n=`command:${this.name()}`;if(this._actionHandler){checkForUnknownOptions();this._processArguments();let i;i=this._chainOrCallHooks(i,"preAction");i=this._chainOrCall(i,(()=>this._actionHandler(this.processedArgs)));if(this.parent){i=this._chainOrCall(i,(()=>{this.parent.emit(n,e,t)}))}i=this._chainOrCallHooks(i,"postAction");return i}if(this.parent&&this.parent.listenerCount(n)){checkForUnknownOptions();this._processArguments();this.parent.emit(n,e,t)}else if(e.length){if(this._findCommand("*")){return this._dispatchSubcommand("*",e,t)}if(this.listenerCount("command:*")){this.emit("command:*",e,t)}else if(this.commands.length){this.unknownCommand()}else{checkForUnknownOptions();this._processArguments()}}else if(this.commands.length){checkForUnknownOptions();this.help({error:true})}else{checkForUnknownOptions();this._processArguments()}}_findCommand(e){if(!e)return undefined;return this.commands.find((t=>t._name===e||t._aliases.includes(e)))}_findOption(e){return this.options.find((t=>t.is(e)))}_checkForMissingMandatoryOptions(){this._getCommandAndAncestors().forEach((e=>{e.options.forEach((t=>{if(t.mandatory&&e.getOptionValue(t.attributeName())===undefined){e.missingMandatoryOptionValue(t)}}))}))}_checkForConflictingLocalOptions(){const e=this.options.filter((e=>{const t=e.attributeName();if(this.getOptionValue(t)===undefined){return false}return this.getOptionValueSource(t)!=="default"}));const t=e.filter((e=>e.conflictsWith.length>0));t.forEach((t=>{const i=e.find((e=>t.conflictsWith.includes(e.attributeName())));if(i){this._conflictingOption(t,i)}}))}_checkForConflictingOptions(){this._getCommandAndAncestors().forEach((e=>{e._checkForConflictingLocalOptions()}))}parseOptions(e){const t=[];const i=[];let n=t;const s=e.slice();function maybeOption(e){return e.length>1&&e[0]==="-"}let r=null;while(s.length){const e=s.shift();if(e==="--"){if(n===i)n.push(e);n.push(...s);break}if(r&&!maybeOption(e)){this.emit(`option:${r.name()}`,e);continue}r=null;if(maybeOption(e)){const t=this._findOption(e);if(t){if(t.required){const e=s.shift();if(e===undefined)this.optionMissingArgument(t);this.emit(`option:${t.name()}`,e)}else if(t.optional){let e=null;if(s.length>0&&!maybeOption(s[0])){e=s.shift()}this.emit(`option:${t.name()}`,e)}else{this.emit(`option:${t.name()}`)}r=t.variadic?t:null;continue}}if(e.length>2&&e[0]==="-"&&e[1]!=="-"){const t=this._findOption(`-${e[1]}`);if(t){if(t.required||t.optional&&this._combineFlagAndOptionalValue){this.emit(`option:${t.name()}`,e.slice(2))}else{this.emit(`option:${t.name()}`);s.unshift(`-${e.slice(2)}`)}continue}}if(/^--[^=]+=/.test(e)){const t=e.indexOf("=");const i=this._findOption(e.slice(0,t));if(i&&(i.required||i.optional)){this.emit(`option:${i.name()}`,e.slice(t+1));continue}}if(maybeOption(e)){n=i}if((this._enablePositionalOptions||this._passThroughOptions)&&t.length===0&&i.length===0){if(this._findCommand(e)){t.push(e);if(s.length>0)i.push(...s);break}else if(this._getHelpCommand()&&e===this._getHelpCommand().name()){t.push(e);if(s.length>0)t.push(...s);break}else if(this._defaultCommandName){i.push(e);if(s.length>0)i.push(...s);break}}if(this._passThroughOptions){n.push(e);if(s.length>0)n.push(...s);break}n.push(e)}return{operands:t,unknown:i}}opts(){if(this._storeOptionsAsProperties){const e={};const t=this.options.length;for(let i=0;i<t;i++){const t=this.options[i].attributeName();e[t]=t===this._versionOptionName?this._version:this[t]}return e}return this._optionValues}optsWithGlobals(){return this._getCommandAndAncestors().reduce(((e,t)=>Object.assign(e,t.opts())),{})}error(e,t){this._outputConfiguration.outputError(`${e}\n`,this._outputConfiguration.writeErr);if(typeof this._showHelpAfterError==="string"){this._outputConfiguration.writeErr(`${this._showHelpAfterError}\n`)}else if(this._showHelpAfterError){this._outputConfiguration.writeErr("\n");this.outputHelp({error:true})}const i=t||{};const n=i.exitCode||1;const s=i.code||"commander.error";this._exit(n,s,e)}_parseOptionsEnv(){this.options.forEach((e=>{if(e.envVar&&e.envVar in a.env){const t=e.attributeName();if(this.getOptionValue(t)===undefined||["default","config","env"].includes(this.getOptionValueSource(t))){if(e.required||e.optional){this.emit(`optionEnv:${e.name()}`,a.env[e.envVar])}else{this.emit(`optionEnv:${e.name()}`)}}}}))}_parseOptionsImplied(){const e=new d(this.options);const hasCustomOptionValue=e=>this.getOptionValue(e)!==undefined&&!["default","implied"].includes(this.getOptionValueSource(e));this.options.filter((t=>t.implied!==undefined&&hasCustomOptionValue(t.attributeName())&&e.valueFromOption(this.getOptionValue(t.attributeName()),t))).forEach((e=>{Object.keys(e.implied).filter((e=>!hasCustomOptionValue(e))).forEach((t=>{this.setOptionValueWithSource(t,e.implied[t],"implied")}))}))}missingArgument(e){const t=`error: missing required argument '${e}'`;this.error(t,{code:"commander.missingArgument"})}optionMissingArgument(e){const t=`error: option '${e.flags}' argument missing`;this.error(t,{code:"commander.optionMissingArgument"})}missingMandatoryOptionValue(e){const t=`error: required option '${e.flags}' not specified`;this.error(t,{code:"commander.missingMandatoryOptionValue"})}_conflictingOption(e,t){const findBestOptionFromValue=e=>{const t=e.attributeName();const i=this.getOptionValue(t);const n=this.options.find((e=>e.negate&&t===e.attributeName()));const s=this.options.find((e=>!e.negate&&t===e.attributeName()));if(n&&(n.presetArg===undefined&&i===false||n.presetArg!==undefined&&i===n.presetArg)){return n}return s||e};const getErrorMessage=e=>{const t=findBestOptionFromValue(e);const i=t.attributeName();const n=this.getOptionValueSource(i);if(n==="env"){return`environment variable '${t.envVar}'`}return`option '${t.flags}'`};const i=`error: ${getErrorMessage(e)} cannot be used with ${getErrorMessage(t)}`;this.error(i,{code:"commander.conflictingOption"})}unknownOption(e){if(this._allowUnknownOption)return;let t="";if(e.startsWith("--")&&this._showSuggestionAfterError){let i=[];let n=this;do{const e=n.createHelp().visibleOptions(n).filter((e=>e.long)).map((e=>e.long));i=i.concat(e);n=n.parent}while(n&&!n._enablePositionalOptions);t=m(e,i)}const i=`error: unknown option '${e}'${t}`;this.error(i,{code:"commander.unknownOption"})}_excessArguments(e){if(this._allowExcessArguments)return;const t=this.registeredArguments.length;const i=t===1?"":"s";const n=this.parent?` for '${this.name()}'`:"";const s=`error: too many arguments${n}. Expected ${t} argument${i} but got ${e.length}.`;this.error(s,{code:"commander.excessArguments"})}unknownCommand(){const e=this.args[0];let t="";if(this._showSuggestionAfterError){const i=[];this.createHelp().visibleCommands(this).forEach((e=>{i.push(e.name());if(e.alias())i.push(e.alias())}));t=m(e,i)}const i=`error: unknown command '${e}'${t}`;this.error(i,{code:"commander.unknownCommand"})}version(e,t,i){if(e===undefined)return this._version;this._version=e;t=t||"-V, --version";i=i||"output the version number";const n=this.createOption(t,i);this._versionOptionName=n.attributeName();this._registerOption(n);this.on("option:"+n.name(),(()=>{this._outputConfiguration.writeOut(`${e}\n`);this._exit(0,"commander.version",e)}));return this}description(e,t){if(e===undefined&&t===undefined)return this._description;this._description=e;if(t){this._argsDescription=t}return this}summary(e){if(e===undefined)return this._summary;this._summary=e;return this}alias(e){if(e===undefined)return this._aliases[0];let t=this;if(this.commands.length!==0&&this.commands[this.commands.length-1]._executableHandler){t=this.commands[this.commands.length-1]}if(e===t._name)throw new Error("Command alias can't be the same as its name");const i=this.parent?._findCommand(e);if(i){const t=[i.name()].concat(i.aliases()).join("|");throw new Error(`cannot add alias '${e}' to command '${this.name()}' as already have command '${t}'`)}t._aliases.push(e);return this}aliases(e){if(e===undefined)return this._aliases;e.forEach((e=>this.alias(e)));return this}usage(e){if(e===undefined){if(this._usage)return this._usage;const e=this.registeredArguments.map((e=>l(e)));return[].concat(this.options.length||this._helpOption!==null?"[options]":[],this.commands.length?"[command]":[],this.registeredArguments.length?e:[]).join(" ")}this._usage=e;return this}name(e){if(e===undefined)return this._name;this._name=e;return this}nameFromFilename(e){this._name=r.basename(e,r.extname(e));return this}executableDir(e){if(e===undefined)return this._executableDir;this._executableDir=e;return this}helpInformation(e){const t=this.createHelp();if(t.helpWidth===undefined){t.helpWidth=e&&e.error?this._outputConfiguration.getErrHelpWidth():this._outputConfiguration.getOutHelpWidth()}return t.formatHelp(this,t)}_getHelpContext(e){e=e||{};const t={error:!!e.error};let i;if(t.error){i=e=>this._outputConfiguration.writeErr(e)}else{i=e=>this._outputConfiguration.writeOut(e)}t.write=e.write||i;t.command=this;return t}outputHelp(e){let t;if(typeof e==="function"){t=e;e=undefined}const i=this._getHelpContext(e);this._getCommandAndAncestors().reverse().forEach((e=>e.emit("beforeAllHelp",i)));this.emit("beforeHelp",i);let n=this.helpInformation(i);if(t){n=t(n);if(typeof n!=="string"&&!Buffer.isBuffer(n)){throw new Error("outputHelp callback must return a string or a Buffer")}}i.write(n);if(this._getHelpOption()?.long){this.emit(this._getHelpOption().long)}this.emit("afterHelp",i);this._getCommandAndAncestors().forEach((e=>e.emit("afterAllHelp",i)))}helpOption(e,t){if(typeof e==="boolean"){if(e){this._helpOption=this._helpOption??undefined}else{this._helpOption=null}return this}e=e??"-h, --help";t=t??"display help for command";this._helpOption=this.createOption(e,t);return this}_getHelpOption(){if(this._helpOption===undefined){this.helpOption(undefined,undefined)}return this._helpOption}addHelpOption(e){this._helpOption=e;return this}help(e){this.outputHelp(e);let t=a.exitCode||0;if(t===0&&e&&typeof e!=="function"&&e.error){t=1}this._exit(t,"commander.help","(outputHelp)")}addHelpText(e,t){const i=["beforeAll","before","after","afterAll"];if(!i.includes(e)){throw new Error(`Unexpected value for position to addHelpText.\nExpecting one of '${i.join("', '")}'`)}const n=`${e}Help`;this.on(n,(e=>{let i;if(typeof t==="function"){i=t({error:e.error,command:e.command})}else{i=t}if(i){e.write(`${i}\n`)}}));return this}_outputHelpIfRequested(e){const t=this._getHelpOption();const i=t&&e.find((e=>t.is(e)));if(i){this.outputHelp();this._exit(0,"commander.helpDisplayed","(outputHelp)")}}}function incrementNodeInspectorPort(e){return e.map((e=>{if(!e.startsWith("--inspect")){return e}let t;let i="127.0.0.1";let n="9229";let s;if((s=e.match(/^(--inspect(-brk)?)$/))!==null){t=s[1]}else if((s=e.match(/^(--inspect(-brk|-port)?)=([^:]+)$/))!==null){t=s[1];if(/^\d+$/.test(s[3])){n=s[3]}else{i=s[3]}}else if((s=e.match(/^(--inspect(-brk|-port)?)=([^:]+):(\d+)$/))!==null){t=s[1];i=s[3];n=s[4]}if(t&&n!=="0"){return`${t}=${i}:${parseInt(n)+1}`}return e}))}t.Command=Command},299:(e,t)=>{class CommanderError extends Error{constructor(e,t,i){super(i);Error.captureStackTrace(this,this.constructor);this.name=this.constructor.name;this.code=t;this.exitCode=e;this.nestedError=undefined}}class InvalidArgumentError extends CommanderError{constructor(e){super(1,"commander.invalidArgument",e);Error.captureStackTrace(this,this.constructor);this.name=this.constructor.name}}t.CommanderError=CommanderError;t.InvalidArgumentError=InvalidArgumentError},638:(e,t,i)=>{const{humanReadableArgName:n}=i(253);class Help{constructor(){this.helpWidth=undefined;this.sortSubcommands=false;this.sortOptions=false;this.showGlobalOptions=false}visibleCommands(e){const t=e.commands.filter((e=>!e._hidden));const i=e._getHelpCommand();if(i&&!i._hidden){t.push(i)}if(this.sortSubcommands){t.sort(((e,t)=>e.name().localeCompare(t.name())))}return t}compareOptions(e,t){const getSortKey=e=>e.short?e.short.replace(/^-/,""):e.long.replace(/^--/,"");return getSortKey(e).localeCompare(getSortKey(t))}visibleOptions(e){const t=e.options.filter((e=>!e.hidden));const i=e._getHelpOption();if(i&&!i.hidden){const n=i.short&&e._findOption(i.short);const s=i.long&&e._findOption(i.long);if(!n&&!s){t.push(i)}else if(i.long&&!s){t.push(e.createOption(i.long,i.description))}else if(i.short&&!n){t.push(e.createOption(i.short,i.description))}}if(this.sortOptions){t.sort(this.compareOptions)}return t}visibleGlobalOptions(e){if(!this.showGlobalOptions)return[];const t=[];for(let i=e.parent;i;i=i.parent){const e=i.options.filter((e=>!e.hidden));t.push(...e)}if(this.sortOptions){t.sort(this.compareOptions)}return t}visibleArguments(e){if(e._argsDescription){e.registeredArguments.forEach((t=>{t.description=t.description||e._argsDescription[t.name()]||""}))}if(e.registeredArguments.find((e=>e.description))){return e.registeredArguments}return[]}subcommandTerm(e){const t=e.registeredArguments.map((e=>n(e))).join(" ");return e._name+(e._aliases[0]?"|"+e._aliases[0]:"")+(e.options.length?" [options]":"")+(t?" "+t:"")}optionTerm(e){return e.flags}argumentTerm(e){return e.name()}longestSubcommandTermLength(e,t){return t.visibleCommands(e).reduce(((e,i)=>Math.max(e,t.subcommandTerm(i).length)),0)}longestOptionTermLength(e,t){return t.visibleOptions(e).reduce(((e,i)=>Math.max(e,t.optionTerm(i).length)),0)}longestGlobalOptionTermLength(e,t){return t.visibleGlobalOptions(e).reduce(((e,i)=>Math.max(e,t.optionTerm(i).length)),0)}longestArgumentTermLength(e,t){return t.visibleArguments(e).reduce(((e,i)=>Math.max(e,t.argumentTerm(i).length)),0)}commandUsage(e){let t=e._name;if(e._aliases[0]){t=t+"|"+e._aliases[0]}let i="";for(let t=e.parent;t;t=t.parent){i=t.name()+" "+i}return i+t+" "+e.usage()}commandDescription(e){return e.description()}subcommandDescription(e){return e.summary()||e.description()}optionDescription(e){const t=[];if(e.argChoices){t.push(`choices: ${e.argChoices.map((e=>JSON.stringify(e))).join(", ")}`)}if(e.defaultValue!==undefined){const i=e.required||e.optional||e.isBoolean()&&typeof e.defaultValue==="boolean";if(i){t.push(`default: ${e.defaultValueDescription||JSON.stringify(e.defaultValue)}`)}}if(e.presetArg!==undefined&&e.optional){t.push(`preset: ${JSON.stringify(e.presetArg)}`)}if(e.envVar!==undefined){t.push(`env: ${e.envVar}`)}if(t.length>0){return`${e.description} (${t.join(", ")})`}return e.description}argumentDescription(e){const t=[];if(e.argChoices){t.push(`choices: ${e.argChoices.map((e=>JSON.stringify(e))).join(", ")}`)}if(e.defaultValue!==undefined){t.push(`default: ${e.defaultValueDescription||JSON.stringify(e.defaultValue)}`)}if(t.length>0){const i=`(${t.join(", ")})`;if(e.description){return`${e.description} ${i}`}return i}return e.description}formatHelp(e,t){const i=t.padWidth(e,t);const n=t.helpWidth||80;const s=2;const r=2;function formatItem(e,o){if(o){const a=`${e.padEnd(i+r)}${o}`;return t.wrap(a,n-s,i+r)}return e}function formatList(e){return e.join("\n").replace(/^/gm," ".repeat(s))}let o=[`Usage: ${t.commandUsage(e)}`,""];const a=t.commandDescription(e);if(a.length>0){o=o.concat([t.wrap(a,n,0),""])}const h=t.visibleArguments(e).map((e=>formatItem(t.argumentTerm(e),t.argumentDescription(e))));if(h.length>0){o=o.concat(["Arguments:",formatList(h),""])}const l=t.visibleOptions(e).map((e=>formatItem(t.optionTerm(e),t.optionDescription(e))));if(l.length>0){o=o.concat(["Options:",formatList(l),""])}if(this.showGlobalOptions){const i=t.visibleGlobalOptions(e).map((e=>formatItem(t.optionTerm(e),t.optionDescription(e))));if(i.length>0){o=o.concat(["Global Options:",formatList(i),""])}}const u=t.visibleCommands(e).map((e=>formatItem(t.subcommandTerm(e),t.subcommandDescription(e))));if(u.length>0){o=o.concat(["Commands:",formatList(u),""])}return o.join("\n")}padWidth(e,t){return Math.max(t.longestOptionTermLength(e,t),t.longestGlobalOptionTermLength(e,t),t.longestSubcommandTermLength(e,t),t.longestArgumentTermLength(e,t))}wrap(e,t,i,n=40){const s=" \\f\\t\\v   -   　\ufeff";const r=new RegExp(`[\\n][${s}]+`);if(e.match(r))return e;const o=t-i;if(o<n)return e;const a=e.slice(0,i);const h=e.slice(i).replace("\r\n","\n");const l=" ".repeat(i);const u="​";const c=`\\s${u}`;const p=new RegExp(`\n|.{1,${o-1}}([${c}]|$)|[^${c}]+?([${c}]|$)`,"g");const d=h.match(p)||[];return a+d.map(((e,t)=>{if(e==="\n")return"";return(t>0?l:"")+e.trimEnd()})).join("\n")}}t.Help=Help},77:(e,t,i)=>{const{InvalidArgumentError:n}=i(299);class Option{constructor(e,t){this.flags=e;this.description=t||"";this.required=e.includes("<");this.optional=e.includes("[");this.variadic=/\w\.\.\.[>\]]$/.test(e);this.mandatory=false;const i=splitOptionFlags(e);this.short=i.shortFlag;this.long=i.longFlag;this.negate=false;if(this.long){this.negate=this.long.startsWith("--no-")}this.defaultValue=undefined;this.defaultValueDescription=undefined;this.presetArg=undefined;this.envVar=undefined;this.parseArg=undefined;this.hidden=false;this.argChoices=undefined;this.conflictsWith=[];this.implied=undefined}default(e,t){this.defaultValue=e;this.defaultValueDescription=t;return this}preset(e){this.presetArg=e;return this}conflicts(e){this.conflictsWith=this.conflictsWith.concat(e);return this}implies(e){let t=e;if(typeof e==="string"){t={[e]:true}}this.implied=Object.assign(this.implied||{},t);return this}env(e){this.envVar=e;return this}argParser(e){this.parseArg=e;return this}makeOptionMandatory(e=true){this.mandatory=!!e;return this}hideHelp(e=true){this.hidden=!!e;return this}_concatValue(e,t){if(t===this.defaultValue||!Array.isArray(t)){return[e]}return t.concat(e)}choices(e){this.argChoices=e.slice();this.parseArg=(e,t)=>{if(!this.argChoices.includes(e)){throw new n(`Allowed choices are ${this.argChoices.join(", ")}.`)}if(this.variadic){return this._concatValue(e,t)}return e};return this}name(){if(this.long){return this.long.replace(/^--/,"")}return this.short.replace(/^-/,"")}attributeName(){return camelcase(this.name().replace(/^no-/,""))}is(e){return this.short===e||this.long===e}isBoolean(){return!this.required&&!this.optional&&!this.negate}}class DualOptions{constructor(e){this.positiveOptions=new Map;this.negativeOptions=new Map;this.dualOptions=new Set;e.forEach((e=>{if(e.negate){this.negativeOptions.set(e.attributeName(),e)}else{this.positiveOptions.set(e.attributeName(),e)}}));this.negativeOptions.forEach(((e,t)=>{if(this.positiveOptions.has(t)){this.dualOptions.add(t)}}))}valueFromOption(e,t){const i=t.attributeName();if(!this.dualOptions.has(i))return true;const n=this.negativeOptions.get(i).presetArg;const s=n!==undefined?n:false;return t.negate===(s===e)}}function camelcase(e){return e.split("-").reduce(((e,t)=>e+t[0].toUpperCase()+t.slice(1)))}function splitOptionFlags(e){let t;let i;const n=e.split(/[ |,]+/);if(n.length>1&&!/^[[<]/.test(n[1]))t=n.shift();i=n.shift();if(!t&&/^-[^-]$/.test(i)){t=i;i=undefined}return{shortFlag:t,longFlag:i}}t.Option=Option;t.DualOptions=DualOptions},301:(e,t)=>{const i=3;function editDistance(e,t){if(Math.abs(e.length-t.length)>i)return Math.max(e.length,t.length);const n=[];for(let t=0;t<=e.length;t++){n[t]=[t]}for(let e=0;e<=t.length;e++){n[0][e]=e}for(let i=1;i<=t.length;i++){for(let s=1;s<=e.length;s++){let r=1;if(e[s-1]===t[i-1]){r=0}else{r=1}n[s][i]=Math.min(n[s-1][i]+1,n[s][i-1]+1,n[s-1][i-1]+r);if(s>1&&i>1&&e[s-1]===t[i-2]&&e[s-2]===t[i-1]){n[s][i]=Math.min(n[s][i],n[s-2][i-2]+1)}}}return n[e.length][t.length]}function suggestSimilar(e,t){if(!t||t.length===0)return"";t=Array.from(new Set(t));const n=e.startsWith("--");if(n){e=e.slice(2);t=t.map((e=>e.slice(2)))}let s=[];let r=i;const o=.4;t.forEach((t=>{if(t.length<=1)return;const i=editDistance(e,t);const n=Math.max(e.length,t.length);const a=(n-i)/n;if(a>o){if(i<r){r=i;s=[t]}else if(i===r){s.push(t)}}}));s.sort(((e,t)=>e.localeCompare(t)));if(n){s=s.map((e=>`--${e}`))}if(s.length>1){return`\n(Did you mean one of ${s.join(", ")}?)`}if(s.length===1){return`\n(Did you mean ${s[0]}?)`}return""}t.suggestSimilar=suggestSimilar}};var t={};function __nccwpck_require__(i){var n=t[i];if(n!==undefined){return n.exports}var s=t[i]={exports:{}};var r=true;try{e[i](s,s.exports,__nccwpck_require__);r=false}finally{if(r)delete t[i]}return s.exports}if(typeof __nccwpck_require__!=="undefined")__nccwpck_require__.ab=__dirname+"/";var i={};(()=>{var e=i;const{Argument:t}=__nccwpck_require__(253);const{Command:n}=__nccwpck_require__(119);const{CommanderError:s,InvalidArgumentError:r}=__nccwpck_require__(299);const{Help:o}=__nccwpck_require__(638);const{Option:a}=__nccwpck_require__(77);e.program=new n;e.createCommand=e=>new n(e);e.createOption=(e,t)=>new a(e,t);e.createArgument=(e,i)=>new t(e,i);e.Command=n;e.Option=a;e.Argument=t;e.Help=o;e.CommanderError=s;e.InvalidArgumentError=r;e.InvalidOptionArgumentError=r})();module.exports=i})();