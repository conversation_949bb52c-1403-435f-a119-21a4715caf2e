# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v1.1.1](https://github.com/inspect-js/is-finalizationregistry/compare/v1.1.0...v1.1.1) - 2024-12-16

### Commits

- [actions] re-add finishers [`0f41639`](https://github.com/inspect-js/is-finalizationregistry/commit/0f41639657eb79da783ab99246ffbab97ce52785)
- [Dev <PERSON>] update `@arethetypeswrong/cli`, `@ljharb/tsconfig`, `@types/tape` [`1467f11`](https://github.com/inspect-js/is-finalizationregistry/commit/****************************************)
- [meta] sort package.json [`bd48b68`](https://github.com/inspect-js/is-finalizationregistry/commit/bd48b686cf499ae590404729b4d3a6fca9c9d0f3)
- [Refactor] use `call-bound` directly [`ce9dcd4`](https://github.com/inspect-js/is-finalizationregistry/commit/ce9dcd4abea4d17ca63b2d46385d78bebaf05d84)
- [Deps] update `call-bind` [`9b8daac`](https://github.com/inspect-js/is-finalizationregistry/commit/9b8daac5237a420ec8de65222d1b14e56de55b50)
- [meta] add `sideEffects` flag [`c5cb18b`](https://github.com/inspect-js/is-finalizationregistry/commit/c5cb18b51f22a52dcdc17afc3ce02a1ace41d418)

## [v1.1.0](https://github.com/inspect-js/is-finalizationregistry/compare/v1.0.2...v1.1.0) - 2024-11-23

### Commits

- [actions] reuse common workflows [`b9b56fe`](https://github.com/inspect-js/is-finalizationregistry/commit/b9b56fe405d0385fcbcc587abcf7b6f0c5b4e7be)
- [meta] use `npmignore` to autogenerate an npmignore file [`4b65c3d`](https://github.com/inspect-js/is-finalizationregistry/commit/4b65c3dfe532ba3d1130f9bd7d36d674790b836d)
- [New] add TS types [`22c7c81`](https://github.com/inspect-js/is-finalizationregistry/commit/22c7c81612a906c787432a0c70df27c2a278f370)
- [actions] split out node 10-20, and 20+ [`b4bc95a`](https://github.com/inspect-js/is-finalizationregistry/commit/b4bc95a923323b9064965442f2a5646c1ec4b99b)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `object-inspect`, `safe-publish-latest`, `tape` [`25d2e81`](https://github.com/inspect-js/is-finalizationregistry/commit/25d2e815627d5549bbd366fd95dcd134f99fc16d)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `object-inspect`, `tape` [`a045ec6`](https://github.com/inspect-js/is-finalizationregistry/commit/a045ec6d56106561232bd7dbd95c058d195cd3d9)
- [actions] update rebase action to use reusable workflow [`14ee45b`](https://github.com/inspect-js/is-finalizationregistry/commit/14ee45b34361a4a14cfc444a71601e55ddd98a6e)
- [actions] update codecov uploader [`d37bfcb`](https://github.com/inspect-js/is-finalizationregistry/commit/d37bfcb43695e56c06829b05051f5b5f4cdbcd6b)
- [Dev Deps] update `@ljharb/eslint-config`, `auto-changelog`, `npmignore`, `object-inspect`, `tape` [`a36b69c`](https://github.com/inspect-js/is-finalizationregistry/commit/a36b69c8f1a43beb07416658609b9950e58bb615)
- [meta] clean up `exports` [`35b3562`](https://github.com/inspect-js/is-finalizationregistry/commit/35b35627f8234e76a92e72669f7c5041e2c4074b)
- [meta] add missing `engines.node` [`0603193`](https://github.com/inspect-js/is-finalizationregistry/commit/06031931c5f4e672f47463da2c7b43b45a629819)
- [Tests] replace `aud` with `npm audit` [`05b4596`](https://github.com/inspect-js/is-finalizationregistry/commit/05b459606f22cebe6a42101438bfcdae68313786)
- [Deps] update `call-bind` [`6482025`](https://github.com/inspect-js/is-finalizationregistry/commit/6482025cba84cff2b51db51768ed6d391f01335d)
- [Dev Deps] add missing peer dep [`90f97dd`](https://github.com/inspect-js/is-finalizationregistry/commit/90f97ddc8998ead46f26d8b2fa0fd8be17ea48bb)

## [v1.0.2](https://github.com/inspect-js/is-finalizationregistry/compare/v1.0.1...v1.0.2) - 2021-10-05

### Commits

- [meta] do not publish Github Actions workflows [`5509e8c`](https://github.com/inspect-js/is-finalizationregistry/commit/5509e8c13173a128244fc306d304c9be958b62f3)
- [actions] use `node/install` instead of `node/run`; use `codecov` action [`b566204`](https://github.com/inspect-js/is-finalizationregistry/commit/b5662048c9824089baf6fe3e0c408d6297635b2c)
- [readme] fix markdown [`02602dd`](https://github.com/inspect-js/is-finalizationregistry/commit/02602dda3176944f6681b97fb0d26e43bf2accc8)
- [Fix] use `call-bind` and obviate missing `es-abstract` dep [`9d71846`](https://github.com/inspect-js/is-finalizationregistry/commit/9d718467ef713e10242c28e48d82947d108ed5ef)
- [readme] add actions and codecov badges [`75381a7`](https://github.com/inspect-js/is-finalizationregistry/commit/75381a76ad373fd4bea0f032e8593dec60785332)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `auto-changelog`, `object-inspect`, `tape` [`7a0f9d8`](https://github.com/inspect-js/is-finalizationregistry/commit/7a0f9d8befeee03f578cf9184190e4a143a05abf)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `object-inspect`, `tape` [`962689f`](https://github.com/inspect-js/is-finalizationregistry/commit/962689f01c33d06eadb4456004d231b56cb13825)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `tape` [`762ebf2`](https://github.com/inspect-js/is-finalizationregistry/commit/762ebf2f8d06597029b794053f0630b6ddeb7cc2)
- [meta] add `safe-publish-latest`; use `prepublishOnly` script for npm 7+ [`fe0b226`](https://github.com/inspect-js/is-finalizationregistry/commit/fe0b2268e0eaa4fd56418aba87d2e761c04917e6)

## [v1.0.1](https://github.com/inspect-js/is-finalizationregistry/compare/v1.0.0...v1.0.1) - 2020-12-04

### Commits

- [Tests] migrate tests to Github Actions [`b697250`](https://github.com/inspect-js/is-finalizationregistry/commit/b69725063681eeb9179d9945512a62112b360cd2)
- [Tests] run `nyc` on all tests [`9091806`](https://github.com/inspect-js/is-finalizationregistry/commit/9091806c8cc05340dc964fb3c566e650c1bff947)
- [actions] add "Allow Edits" workflow [`5d8c4f5`](https://github.com/inspect-js/is-finalizationregistry/commit/5d8c4f5e5ca4f001f6a16744c2cce5d32bdae39b)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `object-inspect` [`3af34c1`](https://github.com/inspect-js/is-finalizationregistry/commit/3af34c194a8dfb41e773f7e1647e1e1cf05ec98d)
- [actions] switch Automatic Rebase workflow to `pull_request_target` event [`d0720ff`](https://github.com/inspect-js/is-finalizationregistry/commit/d0720ff2d07308ec3e90f13c7a15affa05339fe7)
- [readme] remove travis badge [`2757b27`](https://github.com/inspect-js/is-finalizationregistry/commit/2757b27db4cbb93d712bd85c1741f3071a720dc7)
- [meta] add `package.json` to `exports` [`c680142`](https://github.com/inspect-js/is-finalizationregistry/commit/c680142dcd73d3f3c8ec75c0ab1c9281edaeb91f)

## v1.0.0 - 2020-08-02

### Commits

- Initial commit [`094a595`](https://github.com/inspect-js/is-finalizationregistry/commit/094a59522ab29b1701ad2f1cb67ee01f1e68cae2)
- readme [`38d75fd`](https://github.com/inspect-js/is-finalizationregistry/commit/38d75fd9d38106b6a4d09bdb4ac5c5ca186c62de)
- Tests [`1052cd2`](https://github.com/inspect-js/is-finalizationregistry/commit/1052cd21bfa90e83e5fbf656ce67f3c038aa9336)
- npm init [`307016d`](https://github.com/inspect-js/is-finalizationregistry/commit/307016d5228e184a22a1ee2992f24ed208c3cec6)
- Implementation [`48df57a`](https://github.com/inspect-js/is-finalizationregistry/commit/48df57a25d3cde99f43a46fe458e878f79179520)
- [meta] add auto-changelog [`cd1c8e1`](https://github.com/inspect-js/is-finalizationregistry/commit/cd1c8e1f97a499a0ea7edc6e7afde3b522fb8329)
- [actions] add automatic rebasing / merge commit blocking [`d6c9220`](https://github.com/inspect-js/is-finalizationregistry/commit/d6c92207d7f76785dada19e09b937326dc3b499d)
- [meta] add "funding"; create `FUNDING.yml` [`0e74e10`](https://github.com/inspect-js/is-finalizationregistry/commit/0e74e10f3667b1f8c9ea2a7a3dba67b373a17902)
- [Tests] add `npm run lint` [`edb8d13`](https://github.com/inspect-js/is-finalizationregistry/commit/edb8d138a6350b0fb7f398657125c74f218c547f)
- [Tests] use shared travis-ci configs [`8eb7a3a`](https://github.com/inspect-js/is-finalizationregistry/commit/8eb7a3a0a45e5aab60edb385ddee2e7bd1e22d81)
- Only apps should have lockfiles [`566b021`](https://github.com/inspect-js/is-finalizationregistry/commit/566b021e66bc2325e12f88324174413348cb987d)
