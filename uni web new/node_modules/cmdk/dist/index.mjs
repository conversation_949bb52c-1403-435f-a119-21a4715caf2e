"use client";import{a as ce}from"./chunk-NZJY6EH4.mjs";import*as w from"@radix-ui/react-dialog";import*as n from"react";import{Primitive as D}from"@radix-ui/react-primitive";import{useId as L}from"@radix-ui/react-id";import{useSyncExternalStore as Re}from"use-sync-external-store/shim/index.js";var N='[cmdk-group=""]',Q='[cmdk-group-items=""]',be='[cmdk-group-heading=""]',Z='[cmdk-item=""]',le=`${Z}:not([aria-disabled="true"])`,Y="cmdk-item-select",I="data-value",he=(r,o,t)=>ce(r,o,t),ue=n.createContext(void 0),K=()=>n.useContext(ue),de=n.createContext(void 0),ee=()=>n.useContext(de),fe=n.createContext(void 0);var me=n.forwardRef((r,o)=>{let t=k(()=>{var e,s;return{search:"",value:(s=(e=r.value)!=null?e:r.defaultValue)!=null?s:"",filtered:{count:0,items:new Map,groups:new Set}}}),u=k(()=>new Set),c=k(()=>new Map),d=k(()=>new Map),f=k(()=>new Set),p=pe(r),{label:v,children:b,value:l,onValueChange:y,filter:E,shouldFilter:C,loop:H,disablePointerSelection:ge=!1,vimBindings:$=!0,...O}=r,te=L(),B=L(),F=L(),x=n.useRef(null),R=Te();M(()=>{if(l!==void 0){let e=l.trim();t.current.value=e,h.emit()}},[l]),M(()=>{R(6,re)},[]);let h=n.useMemo(()=>({subscribe:e=>(f.current.add(e),()=>f.current.delete(e)),snapshot:()=>t.current,setState:(e,s,i)=>{var a,m,g;if(!Object.is(t.current[e],s)){if(t.current[e]=s,e==="search")W(),U(),R(1,z);else if(e==="value"&&(i||R(5,re),((a=p.current)==null?void 0:a.value)!==void 0)){let S=s!=null?s:"";(g=(m=p.current).onValueChange)==null||g.call(m,S);return}h.emit()}},emit:()=>{f.current.forEach(e=>e())}}),[]),q=n.useMemo(()=>({value:(e,s,i)=>{var a;s!==((a=d.current.get(e))==null?void 0:a.value)&&(d.current.set(e,{value:s,keywords:i}),t.current.filtered.items.set(e,ne(s,i)),R(2,()=>{U(),h.emit()}))},item:(e,s)=>(u.current.add(e),s&&(c.current.has(s)?c.current.get(s).add(e):c.current.set(s,new Set([e]))),R(3,()=>{W(),U(),t.current.value||z(),h.emit()}),()=>{d.current.delete(e),u.current.delete(e),t.current.filtered.items.delete(e);let i=A();R(4,()=>{W(),(i==null?void 0:i.getAttribute("id"))===e&&z(),h.emit()})}),group:e=>(c.current.has(e)||c.current.set(e,new Set),()=>{d.current.delete(e),c.current.delete(e)}),filter:()=>p.current.shouldFilter,label:v||r["aria-label"],getDisablePointerSelection:()=>p.current.disablePointerSelection,listId:te,inputId:F,labelId:B,listInnerRef:x}),[]);function ne(e,s){var a,m;let i=(m=(a=p.current)==null?void 0:a.filter)!=null?m:he;return e?i(e,t.current.search,s):0}function U(){if(!t.current.search||p.current.shouldFilter===!1)return;let e=t.current.filtered.items,s=[];t.current.filtered.groups.forEach(a=>{let m=c.current.get(a),g=0;m.forEach(S=>{let P=e.get(S);g=Math.max(P,g)}),s.push([a,g])});let i=x.current;_().sort((a,m)=>{var P,V;let g=a.getAttribute("id"),S=m.getAttribute("id");return((P=e.get(S))!=null?P:0)-((V=e.get(g))!=null?V:0)}).forEach(a=>{let m=a.closest(Q);m?m.appendChild(a.parentElement===m?a:a.closest(`${Q} > *`)):i.appendChild(a.parentElement===i?a:a.closest(`${Q} > *`))}),s.sort((a,m)=>m[1]-a[1]).forEach(a=>{var g;let m=(g=x.current)==null?void 0:g.querySelector(`${N}[${I}="${encodeURIComponent(a[0])}"]`);m==null||m.parentElement.appendChild(m)})}function z(){let e=_().find(i=>i.getAttribute("aria-disabled")!=="true"),s=e==null?void 0:e.getAttribute(I);h.setState("value",s||void 0)}function W(){var s,i,a,m;if(!t.current.search||p.current.shouldFilter===!1){t.current.filtered.count=u.current.size;return}t.current.filtered.groups=new Set;let e=0;for(let g of u.current){let S=(i=(s=d.current.get(g))==null?void 0:s.value)!=null?i:"",P=(m=(a=d.current.get(g))==null?void 0:a.keywords)!=null?m:[],V=ne(S,P);t.current.filtered.items.set(g,V),V>0&&e++}for(let[g,S]of c.current)for(let P of S)if(t.current.filtered.items.get(P)>0){t.current.filtered.groups.add(g);break}t.current.filtered.count=e}function re(){var s,i,a;let e=A();e&&(((s=e.parentElement)==null?void 0:s.firstChild)===e&&((a=(i=e.closest(N))==null?void 0:i.querySelector(be))==null||a.scrollIntoView({block:"nearest"})),e.scrollIntoView({block:"nearest"}))}function A(){var e;return(e=x.current)==null?void 0:e.querySelector(`${Z}[aria-selected="true"]`)}function _(){var e;return Array.from(((e=x.current)==null?void 0:e.querySelectorAll(le))||[])}function J(e){let i=_()[e];i&&h.setState("value",i.getAttribute(I))}function X(e){var g;let s=A(),i=_(),a=i.findIndex(S=>S===s),m=i[a+e];(g=p.current)!=null&&g.loop&&(m=a+e<0?i[i.length-1]:a+e===i.length?i[0]:i[a+e]),m&&h.setState("value",m.getAttribute(I))}function oe(e){let s=A(),i=s==null?void 0:s.closest(N),a;for(;i&&!a;)i=e>0?Ie(i,N):Me(i,N),a=i==null?void 0:i.querySelector(le);a?h.setState("value",a.getAttribute(I)):X(e)}let ie=()=>J(_().length-1),ae=e=>{e.preventDefault(),e.metaKey?ie():e.altKey?oe(1):X(1)},se=e=>{e.preventDefault(),e.metaKey?J(0):e.altKey?oe(-1):X(-1)};return n.createElement(D.div,{ref:o,tabIndex:-1,...O,"cmdk-root":"",onKeyDown:e=>{var s;if((s=O.onKeyDown)==null||s.call(O,e),!e.defaultPrevented)switch(e.key){case"n":case"j":{$&&e.ctrlKey&&ae(e);break}case"ArrowDown":{ae(e);break}case"p":case"k":{$&&e.ctrlKey&&se(e);break}case"ArrowUp":{se(e);break}case"Home":{e.preventDefault(),J(0);break}case"End":{e.preventDefault(),ie();break}case"Enter":if(!e.nativeEvent.isComposing&&e.keyCode!==229){e.preventDefault();let i=A();if(i){let a=new Event(Y);i.dispatchEvent(a)}}}}},n.createElement("label",{"cmdk-label":"",htmlFor:q.inputId,id:q.labelId,style:Le},v),j(r,e=>n.createElement(de.Provider,{value:h},n.createElement(ue.Provider,{value:q},e))))}),ye=n.forwardRef((r,o)=>{var F,x;let t=L(),u=n.useRef(null),c=n.useContext(fe),d=K(),f=pe(r),p=(x=(F=f.current)==null?void 0:F.forceMount)!=null?x:c==null?void 0:c.forceMount;M(()=>{if(!p)return d.item(t,c==null?void 0:c.id)},[p]);let v=ve(t,u,[r.value,r.children,u],r.keywords),b=ee(),l=T(R=>R.value&&R.value===v.current),y=T(R=>p||d.filter()===!1?!0:R.search?R.filtered.items.get(t)>0:!0);n.useEffect(()=>{let R=u.current;if(!(!R||r.disabled))return R.addEventListener(Y,E),()=>R.removeEventListener(Y,E)},[y,r.onSelect,r.disabled]);function E(){var R,h;C(),(h=(R=f.current).onSelect)==null||h.call(R,v.current)}function C(){b.setState("value",v.current,!0)}if(!y)return null;let{disabled:H,value:ge,onSelect:$,forceMount:O,keywords:te,...B}=r;return n.createElement(D.div,{ref:G([u,o]),...B,id:t,"cmdk-item":"",role:"option","aria-disabled":!!H,"aria-selected":!!l,"data-disabled":!!H,"data-selected":!!l,onPointerMove:H||d.getDisablePointerSelection()?void 0:C,onClick:H?void 0:E},r.children)}),Se=n.forwardRef((r,o)=>{let{heading:t,children:u,forceMount:c,...d}=r,f=L(),p=n.useRef(null),v=n.useRef(null),b=L(),l=K(),y=T(C=>c||l.filter()===!1?!0:C.search?C.filtered.groups.has(f):!0);M(()=>l.group(f),[]),ve(f,p,[r.value,r.heading,v]);let E=n.useMemo(()=>({id:f,forceMount:c}),[c]);return n.createElement(D.div,{ref:G([p,o]),...d,"cmdk-group":"",role:"presentation",hidden:y?void 0:!0},t&&n.createElement("div",{ref:v,"cmdk-group-heading":"","aria-hidden":!0,id:b},t),j(r,C=>n.createElement("div",{"cmdk-group-items":"",role:"group","aria-labelledby":t?b:void 0},n.createElement(fe.Provider,{value:E},C))))}),Ee=n.forwardRef((r,o)=>{let{alwaysRender:t,...u}=r,c=n.useRef(null),d=T(f=>!f.search);return!t&&!d?null:n.createElement(D.div,{ref:G([c,o]),...u,"cmdk-separator":"",role:"separator"})}),Ce=n.forwardRef((r,o)=>{let{onValueChange:t,...u}=r,c=r.value!=null,d=ee(),f=T(l=>l.search),p=T(l=>l.value),v=K(),b=n.useMemo(()=>{var y;let l=(y=v.listInnerRef.current)==null?void 0:y.querySelector(`${Z}[${I}="${encodeURIComponent(p)}"]`);return l==null?void 0:l.getAttribute("id")},[]);return n.useEffect(()=>{r.value!=null&&d.setState("search",r.value)},[r.value]),n.createElement(D.input,{ref:o,...u,"cmdk-input":"",autoComplete:"off",autoCorrect:"off",spellCheck:!1,"aria-autocomplete":"list",role:"combobox","aria-expanded":!0,"aria-controls":v.listId,"aria-labelledby":v.labelId,"aria-activedescendant":b,id:v.inputId,type:"text",value:c?r.value:f,onChange:l=>{c||d.setState("search",l.target.value),t==null||t(l.target.value)}})}),xe=n.forwardRef((r,o)=>{let{children:t,label:u="Suggestions",...c}=r,d=n.useRef(null),f=n.useRef(null),p=K();return n.useEffect(()=>{if(f.current&&d.current){let v=f.current,b=d.current,l,y=new ResizeObserver(()=>{l=requestAnimationFrame(()=>{let E=v.offsetHeight;b.style.setProperty("--cmdk-list-height",E.toFixed(1)+"px")})});return y.observe(v),()=>{cancelAnimationFrame(l),y.unobserve(v)}}},[]),n.createElement(D.div,{ref:G([d,o]),...c,"cmdk-list":"",role:"listbox","aria-label":u,id:p.listId},j(r,v=>n.createElement("div",{ref:G([f,p.listInnerRef]),"cmdk-list-sizer":""},v)))}),Pe=n.forwardRef((r,o)=>{let{open:t,onOpenChange:u,overlayClassName:c,contentClassName:d,container:f,...p}=r;return n.createElement(w.Root,{open:t,onOpenChange:u},n.createElement(w.Portal,{container:f},n.createElement(w.Overlay,{"cmdk-overlay":"",className:c}),n.createElement(w.Content,{"aria-label":r.label,"cmdk-dialog":"",className:d},n.createElement(me,{ref:o,...p}))))}),we=n.forwardRef((r,o)=>T(u=>u.filtered.count===0)?n.createElement(D.div,{ref:o,...r,"cmdk-empty":"",role:"presentation"}):null),De=n.forwardRef((r,o)=>{let{progress:t,children:u,label:c="Loading...",...d}=r;return n.createElement(D.div,{ref:o,...d,"cmdk-loading":"",role:"progressbar","aria-valuenow":t,"aria-valuemin":0,"aria-valuemax":100,"aria-label":c},j(r,f=>n.createElement("div",{"aria-hidden":!0},f)))}),Ve=Object.assign(me,{List:xe,Item:ye,Input:Ce,Group:Se,Separator:Ee,Dialog:Pe,Empty:we,Loading:De});function Ie(r,o){let t=r.nextElementSibling;for(;t;){if(t.matches(o))return t;t=t.nextElementSibling}}function Me(r,o){let t=r.previousElementSibling;for(;t;){if(t.matches(o))return t;t=t.previousElementSibling}}function pe(r){let o=n.useRef(r);return M(()=>{o.current=r}),o}var M=typeof window=="undefined"?n.useEffect:n.useLayoutEffect;function k(r){let o=n.useRef();return o.current===void 0&&(o.current=r()),o}function G(r){return o=>{r.forEach(t=>{typeof t=="function"?t(o):t!=null&&(t.current=o)})}}function T(r){let o=ee(),t=()=>r(o.snapshot());return Re(o.subscribe,t,t)}function ve(r,o,t,u=[]){let c=n.useRef(),d=K();return M(()=>{var v;let f=(()=>{var b;for(let l of t){if(typeof l=="string")return l.trim();if(typeof l=="object"&&"current"in l)return l.current?(b=l.current.textContent)==null?void 0:b.trim():c.current}})(),p=u.map(b=>b.trim());d.value(r,f,p),(v=o.current)==null||v.setAttribute(I,f),c.current=f}),c}var Te=()=>{let[r,o]=n.useState(),t=k(()=>new Map);return M(()=>{t.current.forEach(u=>u()),t.current=new Map},[r]),(u,c)=>{t.current.set(u,c),o({})}};function ke(r){let o=r.type;return typeof o=="function"?o(r.props):"render"in o?o.render(r.props):r}function j({asChild:r,children:o},t){return r&&n.isValidElement(o)?n.cloneElement(ke(o),{ref:o.ref},t(o.props.children)):t(o)}var Le={position:"absolute",width:"1px",height:"1px",padding:"0",margin:"-1px",overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0"};export{Ve as Command,Pe as CommandDialog,we as CommandEmpty,Se as CommandGroup,Ce as CommandInput,ye as CommandItem,xe as CommandList,De as CommandLoading,me as CommandRoot,Ee as CommandSeparator,he as defaultFilter,T as useCommandState};
