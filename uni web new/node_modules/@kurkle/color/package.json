{"name": "@kurkle/color", "type": "module", "version": "0.3.4", "description": "css color parsing, manupulation and conversion", "sideEffects": false, "main": "dist/color.cjs", "module": "dist/color.esm.js", "types": "dist/color.d.ts", "exports": {"types": "./dist/color.d.ts", "import": "./dist/color.esm.js", "require": "./dist/color.cjs"}, "scripts": {"build": "node util/copy_dist.js && rollup -c", "lint": "eslint src/*.js test/*.js util/*.js", "test": "node test/index.js"}, "repository": {"type": "git", "url": "git+https://github.com/kurkle/color.git"}, "files": ["dist/*", "dist/color.d.ts"], "keywords": ["color", "colour", "css", "hsl", "hex", "rgb", "rgba", "hwb", "hsv", "cmyk"], "author": "<PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/kurkle/color/issues"}, "homepage": "https://github.com/kurkle/color#readme", "devDependencies": {"@rollup/plugin-terser": "^0.4.0", "assert": "^2.0.0", "benchmark": "^2.1.4", "chartjs-color": "^2.4.1", "chartjs-color-string": "^0.6.0", "child_process": "^1.0.2", "chroma-js": "^3.1.1", "color-name": "^2.0.0", "color-names": "^2.0.0", "color-parse": "^2.0.2", "color-parser": "^0.1.0", "color-string": "^1.5.5", "csscolorparser": "^1.0.3", "eslint": "^9.15.0", "eslint-config-chartjs": "^0.3.0", "eslint-config-defaults": "^9.0.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-react": "^7.22.0", "fs": "0.0.1-security", "perf_hooks": "0.0.1", "rollup": "^4.25.0", "rollup-plugin-analyzer": "^4.0.0", "rollup-plugin-cleanup": "^3.2.1", "rollup-plugin-istanbul": "^5.0.0", "rollup-plugin-visualizer": "^5.8.3", "tinycolor2": "^1.4.2", "typedoc": "^0.26.7", "typescript": "^5.6.2", "util": "^0.12.3"}}