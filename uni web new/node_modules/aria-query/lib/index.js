"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.roles = exports.roleElements = exports.elementRoles = exports.dom = exports.aria = void 0;
var _ariaPropsMap = _interopRequireDefault(require("./ariaPropsMap"));
var _domMap = _interopRequireDefault(require("./domMap"));
var _rolesMap = _interopRequireDefault(require("./rolesMap"));
var _elementRoleMap = _interopRequireDefault(require("./elementRoleMap"));
var _roleElementMap = _interopRequireDefault(require("./roleElementMap"));
function _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }
var aria = exports.aria = _ariaPropsMap.default;
var dom = exports.dom = _domMap.default;
var roles = exports.roles = _rolesMap.default;
var elementRoles = exports.elementRoles = _elementRoleMap.default;
var roleElements = exports.roleElements = _roleElementMap.default;