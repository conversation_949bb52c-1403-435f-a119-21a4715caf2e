# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v1.2.3](https://github.com/inspect-js/functions-have-names/compare/v1.2.2...v1.2.3) - 2022-04-19

### Fixed

- [Fix] in IE 9-11, the descriptor is absent [`#11`](https://github.com/inspect-js/functions-have-names/issues/11) [`#25`](https://github.com/es-shims/RegExp.prototype.flags/issues/25)

### Commits

- [actions] reuse common workflows [`4ed274a`](https://github.com/inspect-js/functions-have-names/commit/4ed274a2441c7fd38ff6add741c309e268550d97)
- [actions] use `node/install` instead of `node/run`; use `codecov` action [`96dfcaa`](https://github.com/inspect-js/functions-have-names/commit/96dfcaaf1c9c5305f2b66ef69f9cddf1d9d9a578)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `tape` [`9e674f8`](https://github.com/inspect-js/functions-have-names/commit/9e674f85520a93235e412a3fd7671d2356c6e45b)
- [readme] add github actions/codecov badges; update URLs [`d913f5b`](https://github.com/inspect-js/functions-have-names/commit/d913f5bf38ccab32d5fbea4a044b9cd93a4b9bec)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `safe-publish-latest`, `tape` [`f61058f`](https://github.com/inspect-js/functions-have-names/commit/f61058fe1e34f2cfa9235283a4fc6c0c0172c91a)
- [actions] update codecov uploader [`3348839`](https://github.com/inspect-js/functions-have-names/commit/33488394e7cadbf499bee4775c627c1370d033d0)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `safe-publish-latest`, `tape` [`ee1a321`](https://github.com/inspect-js/functions-have-names/commit/ee1a3211a40902af59aa629e3ac41ec36360dc1b)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `tape` [`b8dc1a2`](https://github.com/inspect-js/functions-have-names/commit/b8dc1a277b08362bebedfeba2faca8964f68283b)
- [Dev Deps] update `@ljharb/eslint-config`, `tape` [`0e825c4`](https://github.com/inspect-js/functions-have-names/commit/0e825c4ba8525b02d9acaaf2511371f76c0562ce)
- [meta] use `prepublishOnly` script for npm 7+ [`9489d66`](https://github.com/inspect-js/functions-have-names/commit/9489d666c59702ea6bafd3ff611b3eadfee6570e)

## [v1.2.2](https://github.com/inspect-js/functions-have-names/compare/v1.2.1...v1.2.2) - 2020-12-14

### Commits

- [Tests] migrate tests to Github Actions [`39bf4fe`](https://github.com/inspect-js/functions-have-names/commit/39bf4fe5ae5b3610a80ba13726f3ee00e3c49e2f)
- [meta] do not publish github action workflow files [`45ab0cb`](https://github.com/inspect-js/functions-have-names/commit/45ab0cbdc0da2efd64f5deb9810be63009bac4a0)
- [readme] add docs, fix URLs [`fad3af6`](https://github.com/inspect-js/functions-have-names/commit/fad3af61e9cbc27f47d2097614f43c62ae1022dd)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `tape` [`82df94a`](https://github.com/inspect-js/functions-have-names/commit/82df94ae06f05a5fa321dda9b7d902ac9fc26424)
- [Tests] run `nyc` on all tests; use `tape` runner [`8038329`](https://github.com/inspect-js/functions-have-names/commit/8038329fec493043639d9d8c779141dcb7d00c2d)
- [actions] add automatic rebasing / merge commit blocking [`49795eb`](https://github.com/inspect-js/functions-have-names/commit/49795ebf38ae3ba724ff7ac5c53598ec66ab814b)
- [actions] add "Allow Edits" workflow [`2096fe6`](https://github.com/inspect-js/functions-have-names/commit/2096fe6d67d435c0e0da25f3cfe9ff02991c41e6)
- [actions] switch Automatic Rebase workflow to `pull_request_target` event [`ec1c6fe`](https://github.com/inspect-js/functions-have-names/commit/ec1c6fe209419c722d732cd512e4375c48366392)
- [Dev Deps] update `auto-changelog`; add `aud` [`79fdb23`](https://github.com/inspect-js/functions-have-names/commit/79fdb23d1ed2b4125f443be193c37330e634e654)
- [Tests] only audit prod deps [`d9ca245`](https://github.com/inspect-js/functions-have-names/commit/d9ca2455e26a45994024d1027344c268a06818bd)
- [Dev Deps] update `auto-changelog`, `tape` [`ac026d4`](https://github.com/inspect-js/functions-have-names/commit/ac026d4bda77e9820b74456fc752d2069e5b8a7f)
- [Dev Deps] update `tape` [`a8c5ee3`](https://github.com/inspect-js/functions-have-names/commit/a8c5ee3622b487938462f82698dae3ceb32da1a7)
- [Dev Deps] update `@ljharb/eslint-config` [`b25fafd`](https://github.com/inspect-js/functions-have-names/commit/b25fafd0923dcf53c3aeca92268e497ffd96ec34)

## [v1.2.1](https://github.com/inspect-js/functions-have-names/compare/v1.2.0...v1.2.1) - 2020-01-19

### Commits

- [Tests] use shared travis-ci configs [`612823a`](https://github.com/inspect-js/functions-have-names/commit/612823a064b4be4c61a1e52d1009abed4a4fc4fb)
- [Fix] IE 8 has a broken `Object.getOwnPropertyDescriptor` [`ba01c22`](https://github.com/inspect-js/functions-have-names/commit/ba01c22795162b787a698950ea34250ce68a7bb1)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `auto-changelog`; add `safe-publish-latest` [`b28d9d2`](https://github.com/inspect-js/functions-have-names/commit/b28d9d2e8bc0b758671bcaf2f7aa0d4ad4b42046)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `tape` [`a62fbd6`](https://github.com/inspect-js/functions-have-names/commit/a62fbd69a34a2b1d1860acfa2afc6dcc839bc180)
- [meta] add `funding` field [`8734a94`](https://github.com/inspect-js/functions-have-names/commit/8734a940e39acdf7619eb89e358746bd278b4c90)

## [v1.2.0](https://github.com/inspect-js/functions-have-names/compare/v1.1.1...v1.2.0) - 2019-10-20

### Commits

- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `auto-changelog` [`7e07444`](https://github.com/inspect-js/functions-have-names/commit/7e0744437789641ea462005d2e350ef476aa7141)
- [New] add `boundFunctionsHaveNames()` [`05661be`](https://github.com/inspect-js/functions-have-names/commit/05661be26c3c260bb3984e433dc9cea3fd82f9ac)

## [v1.1.1](https://github.com/inspect-js/functions-have-names/compare/v1.1.0...v1.1.1) - 2019-07-24

### Commits

- [Tests] fix linting errors [`0cb8017`](https://github.com/inspect-js/functions-have-names/commit/0cb8017203ae37d1e019bb1c99120f3f56a266a5)
- [Tests] fix tests when name is not configurable [`38a8aee`](https://github.com/inspect-js/functions-have-names/commit/38a8aeee0403bd7aa7f35da76dc433cbcdd3f85a)
- [Fix] ensure function name mangling does not break detection [`f6926ab`](https://github.com/inspect-js/functions-have-names/commit/f6926abaaebc81366f73cf0c3f874ad7e4ba16d2)

## [v1.1.0](https://github.com/inspect-js/functions-have-names/compare/v1.0.0...v1.1.0) - 2019-07-23

### Commits

- [New] add `functionsHaveConfigurableNames` function on main export [`ce73f75`](https://github.com/inspect-js/functions-have-names/commit/ce73f75891640a462326df7266d90b09519a5fca)

## v1.0.0 - 2019-07-22

### Commits

- [Tests] add travis.yml [`06ed096`](https://github.com/inspect-js/functions-have-names/commit/06ed09681a3dc067094562e8d21a31400a782add)
- Initial commit [`ced60bd`](https://github.com/inspect-js/functions-have-names/commit/ced60bd089539eb228c68fc2ad7c7bc04b959b02)
- npm init [`79088ab`](https://github.com/inspect-js/functions-have-names/commit/79088ab607e7e91a402e198ab6d1837a317c6fa9)
- add tests [`c9e8e09`](https://github.com/inspect-js/functions-have-names/commit/c9e8e09c5153797c97c324cca4b837540eddeff8)
- [Tests] add `npm run lint` [`988b924`](https://github.com/inspect-js/functions-have-names/commit/988b924a8a49ea5c0f30d5aa2b2ea9add0b39474)
- [meta] create FUNDING.yml [`2e443ef`](https://github.com/inspect-js/functions-have-names/commit/2e443ef67748214d05898b3da76f908a7e2d7488)
- [meta] add version scripts [`52005e3`](https://github.com/inspect-js/functions-have-names/commit/52005e3794fd0799db5963a5359846798cb95c14)
- implementation [`b7b4942`](https://github.com/inspect-js/functions-have-names/commit/b7b49421ef69fb5e042194a650cb4f71bb4996e4)
- Only apps should have lockfiles [`81d2e04`](https://github.com/inspect-js/functions-have-names/commit/81d2e04e7a43cbff2e46e72781bb0693dbb67800)
- [Tests] use `npx aud` [`baa92d8`](https://github.com/inspect-js/functions-have-names/commit/baa92d8aba331fe8821663bc14baf2e11685474a)
