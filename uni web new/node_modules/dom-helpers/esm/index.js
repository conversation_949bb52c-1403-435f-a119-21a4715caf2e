import activeElement from './activeElement';
import addClass from './addClass';
import addEventListener from './addEventListener';
import animate from './animate';
import { cancel as cancelAnimationFrame, request as requestAnimationFrame } from './animationFrame';
import attribute from './attribute';
import childElements from './childElements';
import clear from './clear';
import closest from './closest';
import contains from './contains';
import childNodes from './childNodes';
import style from './css';
import filter from './filterEventHandler';
import getComputedStyle from './getComputedStyle';
import hasClass from './hasClass';
import height from './height';
import insertAfter from './insertAfter';
import isInput from './isInput';
import isVisible from './isVisible';
import listen from './listen';
import matches from './matches';
import nextUntil from './nextUntil';
import offset from './offset';
import offsetParent from './offsetParent';
import ownerDocument from './ownerDocument';
import ownerWindow from './ownerWindow';
import parents from './parents';
import position from './position';
import prepend from './prepend';
import querySelectorAll from './querySelectorAll';
import remove from './remove';
import removeClass from './removeClass';
import removeEventListener from './removeEventListener';
import scrollbarSize from './scrollbarSize';
import scrollLeft from './scrollLeft';
import scrollParent from './scrollParent';
import scrollTo from './scrollTo';
import scrollTop from './scrollTop';
import siblings from './siblings';
import text from './text';
import toggleClass from './toggleClass';
import transitionEnd from './transitionEnd';
import triggerEvent from './triggerEvent';
import width from './width';
export { addEventListener, removeEventListener, triggerEvent, animate, filter, listen, style, getComputedStyle, attribute, activeElement, ownerDocument, ownerWindow, requestAnimationFrame, cancelAnimationFrame, matches, height, width, offset, offsetParent, position, contains, scrollbarSize, scrollLeft, scrollParent, scrollTo, scrollTop, querySelectorAll, closest, addClass, removeClass, hasClass, toggleClass, transitionEnd, childNodes, childElements, nextUntil, parents, siblings, clear, insertAfter, isInput, isVisible, prepend, remove, text };
export default {
  addEventListener: addEventListener,
  removeEventListener: removeEventListener,
  triggerEvent: triggerEvent,
  animate: animate,
  filter: filter,
  listen: listen,
  style: style,
  getComputedStyle: getComputedStyle,
  attribute: attribute,
  activeElement: activeElement,
  ownerDocument: ownerDocument,
  ownerWindow: ownerWindow,
  requestAnimationFrame: requestAnimationFrame,
  cancelAnimationFrame: cancelAnimationFrame,
  matches: matches,
  height: height,
  width: width,
  offset: offset,
  offsetParent: offsetParent,
  position: position,
  contains: contains,
  scrollbarSize: scrollbarSize,
  scrollLeft: scrollLeft,
  scrollParent: scrollParent,
  scrollTo: scrollTo,
  scrollTop: scrollTop,
  querySelectorAll: querySelectorAll,
  closest: closest,
  addClass: addClass,
  removeClass: removeClass,
  hasClass: hasClass,
  toggleClass: toggleClass,
  transitionEnd: transitionEnd,
  childNodes: childNodes,
  childElements: childElements,
  nextUntil: nextUntil,
  parents: parents,
  siblings: siblings,
  clear: clear,
  insertAfter: insertAfter,
  isInput: isInput,
  isVisible: isVisible,
  prepend: prepend,
  remove: remove,
  text: text
};