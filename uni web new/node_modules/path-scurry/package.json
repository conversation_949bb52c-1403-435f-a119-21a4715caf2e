{"name": "path-scurry", "version": "1.11.1", "description": "walk paths fast and efficiently", "author": "<PERSON> <<EMAIL>> (https://blog.izs.me)", "main": "./dist/commonjs/index.js", "type": "module", "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}}, "files": ["dist"], "license": "BlueOak-1.0.0", "scripts": {"preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "prepare": "tshy", "pretest": "npm run prepare", "presnap": "npm run prepare", "test": "tap", "snap": "tap", "format": "prettier --write . --loglevel warn", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "bench": "bash ./scripts/bench.sh"}, "prettier": {"experimentalTernaries": true, "semi": false, "printWidth": 75, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "devDependencies": {"@nodelib/fs.walk": "^1.2.8", "@types/node": "^20.12.11", "c8": "^7.12.0", "eslint-config-prettier": "^8.6.0", "mkdirp": "^3.0.0", "prettier": "^3.2.5", "rimraf": "^5.0.1", "tap": "^18.7.2", "ts-node": "^10.9.2", "tshy": "^1.14.0", "typedoc": "^0.25.12", "typescript": "^5.4.3"}, "tap": {"typecheck": true}, "engines": {"node": ">=16 || 14 >=14.18"}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/path-scurry"}, "dependencies": {"lru-cache": "^10.2.0", "minipass": "^5.0.0 || ^6.0.2 || ^7.0.0"}, "tshy": {"selfLink": false, "exports": {"./package.json": "./package.json", ".": "./src/index.ts"}}, "types": "./dist/commonjs/index.d.ts"}