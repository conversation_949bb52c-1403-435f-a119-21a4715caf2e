# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v3.0.5](https://github.com/fengyuanchen/is-array-buffer/compare/v3.0.4...v3.0.5) - 2024-12-16

### Commits

- [types] use shared config [`6180b31`](https://github.com/fengyuanchen/is-array-buffer/commit/6180b3180cd15a49e6394cb6de0ae2667124d3f7)
- [actions] split out node 10-20, and 20+ [`1ea4712`](https://github.com/fengyuanchen/is-array-buffer/commit/1ea471223e393bfc124fdecdbeb23fe08209514f)
- [<PERSON>] update `@ljharb/eslint-config`, `@ljharb/tsconfig`, `@types/get-intrinsic`, `@types/object-inspect`, `@types/tape`, `auto-changelog`, `es-value-fixtures`, `object-inspect`, `tape` [`de2b6ab`](https://github.com/fengyuanchen/is-array-buffer/commit/de2b6aba5e88b382bd6707409aa39822707fed50)
- [Deps] update `call-bind`, `get-intrinsic` [`55b80a1`](https://github.com/fengyuanchen/is-array-buffer/commit/55b80a10ae9151e1bd52382610d7330f37a1dc05)
- [Deps] update `call-bind`, `get-intrinsic` [`184484a`](https://github.com/fengyuanchen/is-array-buffer/commit/****************************************)
- [Dev Deps] update `available-typed-arrays`, `tape` [`81582a7`](https://github.com/fengyuanchen/is-array-buffer/commit/81582a72d6ddb28f67ad542edcd673d6c01cc2c0)
- [Tests] add `@arethetypeswrong/cli` [`6d67841`](https://github.com/fengyuanchen/is-array-buffer/commit/6d6784170ec8a0b766428ce44be3157147860a09)
- [Refactor] use `call-bound` directly [`dd0bad1`](https://github.com/fengyuanchen/is-array-buffer/commit/dd0bad194e4ff0e5413be23d69018fa961ce4af7)
- [Tests] replace `aud` with `npm audit` [`99b62d1`](https://github.com/fengyuanchen/is-array-buffer/commit/99b62d1755b6965bbc236342215e28a10d6839a7)
- [Dev Deps] remove obsolete DT package [`9fc6971`](https://github.com/fengyuanchen/is-array-buffer/commit/9fc69715867796c5854fd5377d90da6f01fb981e)
- [Dev Deps] add missing peer dep [`a3b8dbb`](https://github.com/fengyuanchen/is-array-buffer/commit/a3b8dbb538c13abb8f1a4d4d9a682ec71c2f52f8)

## [v3.0.4](https://github.com/fengyuanchen/is-array-buffer/compare/v3.0.3...v3.0.4) - 2024-02-02

### Commits

- [patch] add types [`15fab4c`](https://github.com/fengyuanchen/is-array-buffer/commit/15fab4c68378904a12592969042e638dbc6be8e5)

## [v3.0.3](https://github.com/fengyuanchen/is-array-buffer/compare/v3.0.2...v3.0.3) - 2024-02-02

### Commits

- [Fix] TAs can take a DataView in node 0.8; use a simpler check [`69a03f6`](https://github.com/fengyuanchen/is-array-buffer/commit/69a03f671f892b724be1a899a3d90c981e7601c9)
- [Dev Deps] update `aud`, `available-typed-arrays`, `npmignore`, `object-inspect`, `tape` [`53ca341`](https://github.com/fengyuanchen/is-array-buffer/commit/53ca34182d2aab61e90e744ee47d01f6577b616e)
- [Deps] update `call-bind`, `get-intrinsic`, `is-typed-array` [`bec883f`](https://github.com/fengyuanchen/is-array-buffer/commit/bec883f31e83410a46927a843ded46ebffbbb1f6)
- [Dev Deps] update `@ljharb/eslint-config`, `aud`, `tape` [`944d4ce`](https://github.com/fengyuanchen/is-array-buffer/commit/944d4cea229ce29a0965665bf59df290c53ecbbb)
- [meta] add missing `engines.node` [`0852be6`](https://github.com/fengyuanchen/is-array-buffer/commit/0852be6f64188912d2383ff9b6a7cc12bd369006)
- [Deps] update `get-intrinsic` [`b59c4af`](https://github.com/fengyuanchen/is-array-buffer/commit/b59c4af432014649d6cd1f070cf6e9917e6ad524)

## [v3.0.2](https://github.com/fengyuanchen/is-array-buffer/compare/v3.0.1...v3.0.2) - 2023-03-01

### Commits

- [Fix] `node` 0.8: an object arg to a TA only throws a RangeError when it is an ArrayBuffer of an incompatible byte length [`d5108f6`](https://github.com/fengyuanchen/is-array-buffer/commit/d5108f6d06245e616b6c563995f214a38732243c)
- [Dev Deps] update `object-inspect`, `tape` [`400f456`](https://github.com/fengyuanchen/is-array-buffer/commit/400f4563ccbe27c7fbb485665352c76210bba9cb)
- [Deps] update `get-intrinsic` [`133732e`](https://github.com/fengyuanchen/is-array-buffer/commit/133732ec88f8dded1c705b758badc2240077a6d8)

## [v3.0.1](https://github.com/fengyuanchen/is-array-buffer/compare/v3.0.0...v3.0.1) - 2023-01-05

### Commits

- [Fix] in node 0.8, TAs do not coerce Uint8Arrays to an ArrayBuffer properly [`e488763`](https://github.com/fengyuanchen/is-array-buffer/commit/e48876346f446825dad619e55dcc830ed93f2853)
- [Dev Deps] update `@ljharb/eslint-config`, `aud` [`8eebfa2`](https://github.com/fengyuanchen/is-array-buffer/commit/8eebfa21881f3a9fa5094f8c486f00e496658ea9)

## [v3.0.0](https://github.com/fengyuanchen/is-array-buffer/compare/v2.0.0...v3.0.0) - 2023-01-04

### Commits

- [Breaking] replace package implementation [`b65f929`](https://github.com/fengyuanchen/is-array-buffer/commit/b65f929d856d2a42f043be0f5a0fc2e067370ed1)
- Initial implementation, tests, readme [`06afa73`](https://github.com/fengyuanchen/is-array-buffer/commit/06afa73e775960802ea9257cc6b4cdf768c72d3f)
- Initial commit [`051813f`](https://github.com/fengyuanchen/is-array-buffer/commit/051813f15e3cbf515e2447306761dd9c42819150)
- npm init [`946d3de`](https://github.com/fengyuanchen/is-array-buffer/commit/946d3de82b15471fb2c00a4a2a5a52eb0515eb04)
- [meta] use `npmignore` to autogenerate an npmignore file [`ca4c446`](https://github.com/fengyuanchen/is-array-buffer/commit/ca4c446f37daf5ab8cc590f2194574c2706561ed)
- Only apps should have lockfiles [`be7d8eb`](https://github.com/fengyuanchen/is-array-buffer/commit/be7d8eb09dc5033c04df85d7ba9a8714f4e54357)
- docs: fix badge link [`9ea7fb6`](https://github.com/fengyuanchen/is-array-buffer/commit/9ea7fb638e79f8938161b3b7370cb965d8e93a8b)

<!-- auto-changelog-above -->

## 2.0.0 (Feb 12, 2021)

- Refactor in TypeScript.
- Drop the `dist` directory.
- Drop the UMD bundled file.
- Add a declaration file for TypeScript.

## 1.0.1 (Apr 1, 2018)

- Improve code style.

## 1.0.0 (Jul 25, 2017)

- Supports UMD, CommonJS and ES Module.

## 0.1.0 (Nov 28, 2015)

- Check if ArrayBuffer is defined first.

## 0.0.1 (Nov 11, 2015)

- Initial release.
