globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./components/providers/preload-provider.tsx":{"*":{"id":"(ssr)/./components/providers/preload-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/theme-context.tsx":{"*":{"id":"(ssr)/./components/theme-context.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/ui/toaster.tsx":{"*":{"id":"(ssr)/./components/ui/toaster.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/page.tsx":{"*":{"id":"(ssr)/./app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/admin/sidebar.tsx":{"*":{"id":"(ssr)/./components/admin/sidebar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./contexts/annotation-context.tsx":{"*":{"id":"(ssr)/./contexts/annotation-context.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/dashboard/page.tsx":{"*":{"id":"(ssr)/./app/admin/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/user-management/page.tsx":{"*":{"id":"(ssr)/./app/admin/user-management/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/cafeteria-approvals/page.tsx":{"*":{"id":"(ssr)/./app/admin/cafeteria-approvals/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/reports/page.tsx":{"*":{"id":"(ssr)/./app/admin/reports/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\uni web new\\app\\globals.css":{"id":"(app-pages-browser)/./app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\uni web new\\components\\providers\\preload-provider.tsx":{"id":"(app-pages-browser)/./components/providers/preload-provider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\uni web new\\components\\theme-context.tsx":{"id":"(app-pages-browser)/./components/theme-context.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\uni web new\\components\\ui\\toaster.tsx":{"id":"(app-pages-browser)/./components/ui/toaster.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\uni web new\\node_modules\\next\\font\\google\\target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\uni web new\\app\\page.tsx":{"id":"(app-pages-browser)/./app/page.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\uni web new\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\uni web new\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\uni web new\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\uni web new\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\uni web new\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\uni web new\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\uni web new\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\uni web new\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\uni web new\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\uni web new\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\uni web new\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\uni web new\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\uni web new\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\uni web new\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\uni web new\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\uni web new\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\uni web new\\components\\admin\\sidebar.tsx":{"id":"(app-pages-browser)/./components/admin/sidebar.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\uni web new\\contexts\\annotation-context.tsx":{"id":"(app-pages-browser)/./contexts/annotation-context.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\uni web new\\app\\admin\\dashboard\\page.tsx":{"id":"(app-pages-browser)/./app/admin/dashboard/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\uni web new\\app\\admin\\user-management\\page.tsx":{"id":"(app-pages-browser)/./app/admin/user-management/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\uni web new\\app\\admin\\cafeteria-approvals\\page.tsx":{"id":"(app-pages-browser)/./app/admin/cafeteria-approvals/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\uni web new\\app\\admin\\reports\\page.tsx":{"id":"(app-pages-browser)/./app/admin/reports/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\uni web new\\":[],"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\uni web new\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"C:\\Users\\<USER>\\Desktop\\web and mob\\gradproject v5\\uni web new\\app\\page":[]},"rscModuleMapping":{"(app-pages-browser)/./app/globals.css":{"*":{"id":"(rsc)/./app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/providers/preload-provider.tsx":{"*":{"id":"(rsc)/./components/providers/preload-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/theme-context.tsx":{"*":{"id":"(rsc)/./components/theme-context.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/ui/toaster.tsx":{"*":{"id":"(rsc)/./components/ui/toaster.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/page.tsx":{"*":{"id":"(rsc)/./app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/admin/sidebar.tsx":{"*":{"id":"(rsc)/./components/admin/sidebar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./contexts/annotation-context.tsx":{"*":{"id":"(rsc)/./contexts/annotation-context.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/dashboard/page.tsx":{"*":{"id":"(rsc)/./app/admin/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/user-management/page.tsx":{"*":{"id":"(rsc)/./app/admin/user-management/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/cafeteria-approvals/page.tsx":{"*":{"id":"(rsc)/./app/admin/cafeteria-approvals/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/reports/page.tsx":{"*":{"id":"(rsc)/./app/admin/reports/page.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}