import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseAdmin } from '@/lib/supabase'

export async function POST(request: NextRequest) {
  try {
    console.log('🧪 Testing user creation...')
    
    const body = await request.json()
    const { name, email, password, role = 'student', status = 'active' } = body

    console.log('📝 Request data:', { name, email, role, status })

    if (!name || !email || !password) {
      return NextResponse.json(
        { error: 'Missing required fields: name, email, password' },
        { status: 400 }
      )
    }

    const supabaseAdmin = createSupabaseAdmin()
    console.log('🔧 Supabase admin client created')

    // Test 1: Check if we can list users
    try {
      const { data: users, error: listError } = await supabaseAdmin.auth.admin.listUsers()
      console.log('✅ Can list users:', users?.users?.length || 0, 'users found')
      if (listError) {
        console.error('❌ List users error:', listError)
      }
    } catch (listErr) {
      console.error('❌ Failed to list users:', listErr)
      return NextResponse.json(
        { error: 'Cannot access Supabase admin functions', details: listErr },
        { status: 500 }
      )
    }

    // Test 2: Try to create user
    console.log('🔄 Attempting to create user...')
    const { data: authData, error: authError } = await supabaseAdmin.auth.admin.createUser({
      email,
      password,
      email_confirm: true,
      user_metadata: {
        full_name: name,
        role: role
      }
    })

    if (authError) {
      console.error('❌ Auth error:', authError)
      return NextResponse.json(
        { error: `Failed to create user account: ${authError.message}`, details: authError },
        { status: 400 }
      )
    }

    if (!authData.user) {
      console.error('❌ No user data returned')
      return NextResponse.json(
        { error: 'User creation failed - no user data returned' },
        { status: 400 }
      )
    }

    console.log('✅ User created in auth:', authData.user.id)

    // Test 3: Try to create profile
    console.log('🔄 Attempting to create profile...')
    const { error: profileError } = await supabaseAdmin
      .from('profiles')
      .upsert({
        id: authData.user.id,
        full_name: name,
        role: role,
        status: status,
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })

    if (profileError) {
      console.error('❌ Profile error:', profileError)
      // Clean up auth user
      await supabaseAdmin.auth.admin.deleteUser(authData.user.id)
      return NextResponse.json(
        { error: `Failed to create user profile: ${profileError.message}`, details: profileError },
        { status: 400 }
      )
    }

    console.log('✅ Profile created successfully')

    return NextResponse.json({
      success: true,
      message: 'User created successfully',
      user: {
        id: authData.user.id,
        email: email,
        full_name: name,
        role: role,
        status: status
      }
    })

  } catch (error) {
    console.error('💥 Unexpected error:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
