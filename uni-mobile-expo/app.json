{"expo": {"name": "UniEats", "slug": "unieats-mobile", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "dark", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#0f1424"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.unieats.mobile", "buildNumber": "1.0.0"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#0f1424"}, "package": "com.unieats.mobile", "versionCode": 1}, "web": {"favicon": "./assets/favicon.png"}, "plugins": ["expo-notifications", "expo-image-picker", ["expo-build-properties", {"android": {"compileSdkVersion": 34, "targetSdkVersion": 34, "buildToolsVersion": "34.0.0"}, "ios": {"deploymentTarget": "13.0"}}]], "extra": {"eas": {"projectId": "your-project-id-here"}}}}