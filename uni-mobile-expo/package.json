{"name": "uni-mobile-expo", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build:android": "eas build --platform android", "build:ios": "eas build --platform ios", "submit:android": "eas submit --platform android", "submit:ios": "eas submit --platform ios"}, "dependencies": {"@expo/vector-icons": "^14.0.0", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/native": "^6.1.9", "@react-navigation/native-stack": "^6.9.17", "@react-navigation/stack": "^6.3.20", "@supabase/supabase-js": "^2.38.4", "expo": "~50.0.0", "expo-auth-session": "~5.4.0", "expo-crypto": "~12.8.0", "expo-image": "~1.10.1", "expo-image-picker": "~14.7.1", "expo-linear-gradient": "~12.7.0", "expo-notifications": "~0.27.6", "expo-secure-store": "~12.8.1", "expo-splash-screen": "~0.26.4", "expo-status-bar": "~1.11.1", "expo-url": "~1.2.0", "react": "18.2.0", "react-native": "0.73.4", "react-native-animatable": "^1.4.0", "react-native-gesture-handler": "~2.14.0", "react-native-paper": "^5.12.3", "react-native-reanimated": "~3.6.2", "react-native-safe-area-context": "4.8.2", "react-native-screens": "~3.29.0", "react-native-svg": "14.1.0", "react-native-vector-icons": "^10.0.3"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.2.45", "@types/react-native": "~0.73.0", "typescript": "^5.1.3"}, "private": true}