{"extends": "expo/tsconfig.base", "compilerOptions": {"strict": true, "allowSyntheticDefaultImports": true, "jsx": "react-native", "lib": ["dom", "esnext"], "moduleResolution": "node", "noEmit": true, "skipLibCheck": true, "resolveJsonModule": true, "allowJs": true, "esModuleInterop": true, "isolatedModules": true, "target": "esnext", "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/components/*": ["./src/components/*"], "@/screens/*": ["./src/screens/*"], "@/services/*": ["./src/services/*"], "@/types/*": ["./src/types/*"], "@/utils/*": ["./src/utils/*"], "@/hooks/*": ["./src/hooks/*"], "@/contexts/*": ["./src/contexts/*"]}}, "include": ["**/*.ts", "**/*.tsx", ".expo/types/**/*.ts", "expo-env.d.ts"], "exclude": ["node_modules"]}